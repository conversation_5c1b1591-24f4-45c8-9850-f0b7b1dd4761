# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-java-with-maven

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: (Manual) Deploy To Prod

on: workflow_dispatch

run-name: Deploy ${{ github.ref_name }} to prod
    
env:
  environment: prod
  ARTIFACTORY_DOCKER_REGISTRY: ${{ vars.ARTIFACTORY_DOCKER_REGISTRY }}
  VAULT_ADDRESS_PROD: ${{ vars.VAULT_ADDRESS_PROD }}
  VAULT_GITHUB_ROLE: ${{ vars.VAULT_GITHUB_ROLE }}
  VAULT_GITHUB_PATH: ${{ vars.VAULT_GITHUB_PATH }}
  vars: ${{ toJSON(vars) }}
  
jobs:
  build:
    environment: prod
    permissions:
      contents: read
      id-token: write
    runs-on:
      - kt-gu-cn-heavy
    steps:
    - uses: kering-technologies-china/dso-cus-github-action/frontend/setup@main
      name: Setup Environment
    - name: Verify git tag
      uses: kering-technologies-china/dso-cus-github-action/verify-git-tag@main
    - name: Parse app name
      id: parse_app_name
      uses: kering-technologies-china/dso-cus-github-action/frontend/parse-app-name@main
    - name: Npm Build
      uses: kering-technologies-china/dso-cus-github-action/frontend/build@main
      id: npm_build
      with:
        tag: ${{ github.ref_name }}
        app-name: ${{ steps.parse_app_name.outputs.app_name }}
    - name: Upload to jfrog
      uses: kering-technologies-china/dso-cus-github-action/frontend/upload-jfrog@main
      with:
        app-name: ${{ steps.parse_app_name.outputs.app_name }}
        tag: ${{ steps.npm_build.outputs.tag }}
        file-path: ${{ steps.npm_build.outputs.zip_name }}
    - name: Upload zip to OSS
      uses: kering-technologies-china/dso-cus-github-action/frontend/upload-oss@main
      env:
        OSS_ACCESS_KEY: ${{ env.PROD_OSS_ACCESS_KEY }}
        OSS_ACCESS_SECRET: ${{ env.PROD_OSS_ACCESS_SECRET }}
      with:
        asset-path: ${{ steps.npm_build.outputs.zip_name }}
    - name: Post deploy
      uses: kering-technologies-china/dso-cus-github-action/post-deploy@main