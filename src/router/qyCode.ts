export default {
  path: `/${import.meta.env.VITE_PREFIX}/qyCode`,
  name: `${import.meta.env.VITE_PREFIX}-qyCode`,
  redirect: `/${import.meta.env.VITE_PREFIX}/qyCode/list`,
  children: [
    {
      path: 'list',
      name: `${import.meta.env.VITE_PREFIX}-qyCode-list`,
      component: () => import('@/views/qyCode/list.vue'),
    },
    {
      path: 'create',
      name: `${import.meta.env.VITE_PREFIX}-qyCode-create`,
      component: () => import('@/views/qyCode/edit.vue'),
    },
    {
      path: 'edit',
      name: `${import.meta.env.VITE_PREFIX}-qyCode-edit`,
      component: () => import('@/views/qyCode/edit.vue'),
    },
    {
      path: 'detailStore',
      name: `${import.meta.env.VITE_PREFIX}-qyCode-detailStore`,
      component: () => import('@/views/qyCode/detailStore.vue'),
    },
    {
      path: 'detailMember',
      name: `${import.meta.env.VITE_PREFIX}-qyCode-detailMember`,
      component: () => import('@/views/qyCode/detailMember.vue'),
    },
  ],
};
