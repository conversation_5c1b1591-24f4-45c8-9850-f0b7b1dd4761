import type { App } from 'vue';

import {
  createRouter,
  createWebHashHistory,
  createMemoryHistory,
  type RouteRecordRaw,
} from 'vue-router';

import { setupRouterGuard } from '@/router/guard';

import channel from './channel';
import customer from './customer';
import organizational from './organizational';
import qyCode from './qyCode';
import qyWelcome from './qyWelcome';
import recommendCode from './recommendCode';

const routes: Array<RouteRecordRaw> = [
  channel,
  customer,
  organizational,
  qyCode,
  qyWelcome,
  recommendCode,
];

export const setupRouter = (app: App, props: any) => {
  // If rendering in a popup box, use MemoryHistory
  // Otherwise use WebHashHistory
  const router = createRouter({
    history: props?.inDialog ? createMemoryHistory() : createWebHashHistory(),
    routes,
  });

  if (props?.isInPortal?.()) {
    router.listening = false;
  }

  app.use(router);

  setupRouterGuard(router, props);

  return router;
};
