import { describe, it, expect } from 'vitest';
import routeConfig from './channel'; // 导入待测函数

describe('routeConfig', () => {
  it('should have the correct path', () => {
    expect(routeConfig.path).toEqual(`/${import.meta.env.VITE_PREFIX}/channel`);
  });

  it('should have the correct name', () => {
    expect(routeConfig.name).toEqual(`${import.meta.env.VITE_PREFIX}-channel`);
  });

  it('should have the correct redirect path', () => {
    expect(routeConfig.redirect).toEqual(`/${import.meta.env.VITE_PREFIX}/channel/list`);
  });

  it('should have a child with the correct path and name', () => {
    const child = routeConfig.children[0];
    expect(child.path).toEqual('list');
    expect(child.name).toEqual(`${import.meta.env.VITE_PREFIX}-channel-list`);
  });
});
