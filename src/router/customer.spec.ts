import routes from './customer'; // 导入待测函数
import { describe, it, expect } from 'vitest';

describe('Routes', () => {
  it('should have the correct path', () => {
    expect(routes.path).toEqual(`/${import.meta.env.VITE_PREFIX}/customer`);
  });

  it('should have the correct name', () => {
    expect(routes.name).toEqual(`${import.meta.env.VITE_PREFIX}-customer`);
  });

  it('should have the correct redirect path', () => {
    expect(routes.redirect).toEqual(`/${import.meta.env.VITE_PREFIX}/customer/list`);
  });

  it('should have the correct list child route', () => {
    const listRoute = routes.children.find((child) => child.path === 'list');
    expect(listRoute).toBeDefined();
    expect(listRoute.name).toEqual(`${import.meta.env.VITE_PREFIX}-customer-list`);
    expect(listRoute.component).toEqual(expect.any(Function));
  });

  it('should have the correct detail child route', () => {
    const detailRoute = routes.children.find((child) => child.path === 'detail');
    expect(detailRoute).toBeDefined();
    expect(detailRoute.name).toEqual(`${import.meta.env.VITE_PREFIX}-customer-detail`);
    expect(detailRoute.component).toEqual(expect.any(Function));
  });
});
