export default {
  path: `/${import.meta.env.VITE_PREFIX}/recommendCode`,
  name: `${import.meta.env.VITE_PREFIX}-recommendCode`,
  redirect: `/${import.meta.env.VITE_PREFIX}/recommendCode/list`,
  children: [
    {
      path: 'list',
      name: `${import.meta.env.VITE_PREFIX}-recommendCode-list`,
      component: () => import('@/views/recommendCode/list.vue'),
    },
    {
      path: 'create',
      name: `${import.meta.env.VITE_PREFIX}-recommendCode-create`,
      component: () => import('@/views/recommendCode/edit.vue'),
    },
    {
      path: 'edit',
      name: `${import.meta.env.VITE_PREFIX}-recommendCode-edit`,
      component: () => import('@/views/recommendCode/edit.vue'),
    },
    {
      path: 'detail',
      name: `${import.meta.env.VITE_PREFIX}-recommendCode-detail`,
      component: () => import('@/views/recommendCode/detail.vue'),
    },
  ],
};
