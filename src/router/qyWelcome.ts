export default {
  path: `/${import.meta.env.VITE_PREFIX}/qyWelcome`,
  name: `${import.meta.env.VITE_PREFIX}-qyWelcome`,
  redirect: `/${import.meta.env.VITE_PREFIX}/qyWelcome/list`,
  children: [
    {
      path: 'list',
      name: `${import.meta.env.VITE_PREFIX}-qyWelcome-list`,
      component: () => import('@/views/welcome/list.vue'),
    },
    {
      path: 'edit',
      name: `${import.meta.env.VITE_PREFIX}-qyWelcome-edit`,
      component: () => import('@/views/welcome/edit.vue'),
    },
    {
      path: 'add',
      name: `${import.meta.env.VITE_PREFIX}-qyWelcome-add`,
      component: () => import('@/views/welcome/add.vue'),
    },
  ],
};
