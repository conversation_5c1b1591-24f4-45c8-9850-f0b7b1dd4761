export default {
  path: `/${import.meta.env.VITE_PREFIX}/customer`,
  name: `${import.meta.env.VITE_PREFIX}-customer`,
  redirect: `/${import.meta.env.VITE_PREFIX}/customer/list`,
  children: [
    {
      path: 'list',
      name: `${import.meta.env.VITE_PREFIX}-customer-list`,
      component: () => import('@/views/customer/list.vue'),
    },
    {
      path: 'detail',
      name: `${import.meta.env.VITE_PREFIX}-customer-detail`,
      component: () => import('@/views/customer/detail.vue'),
    },
  ],
};
