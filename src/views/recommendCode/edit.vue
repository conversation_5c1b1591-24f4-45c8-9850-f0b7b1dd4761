<template>
  <a-card class="code-create-container">
    <a-page-header
      style="padding: 0 0 24px"
      :title="isEditCode ? '编辑推优活动' : '创建推优活动'"
      @back="backList"
    />
    <a-row>
      <a-col :span="20">
        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          :label-col="{ span: 3 }"
          :wrapper-col="{ span: 21 }"
        >
          <div class="form-title">基础信息</div>

          <div class="form-desc">说明：此处创建的推优活动规则仅支持API对接使用，不可下载使用</div>
          <!-- 活码规则名称 -->
          <a-form-item label="活动名称" name="activityName">
            <a-input
              v-model:value="formData.activityName"
              placeholder="请输入内容"
              :maxlength="50"
              :disabled="isEditCode"
              showCount
              allowClear
            />
          </a-form-item>

          <!-- 有效期 -->
          <a-form-item label="有效期" name="longTermEffect" :autoLink="false">
            <a-range-picker
              :disabled="dateDisabled"
              v-model:value="expireTime"
              show-time
              :disabled-date="disabledDate"
              @change="validatorDateForm"
            />
            <a-checkbox
              class="m-l-16"
              v-model:checked="formData.longTermEffect"
              :disabled="formData.status !== 0"
              @change="expireChange"
              >长期有效</a-checkbox
            >
          </a-form-item>

          <!-- 关联渠道 -->
          <a-form-item label="关联渠道" name="channels" key="channels">
            <select-channel-comp
              :chose="selectChannel.list"
              :visible="selectChannel.visible"
              @submit="submitChannelFn"
              @close="selectChannel.visible = false"
            />

            <a-button class="m-r-16" type="primary" @click="selectChannel.visible = true">
              {{ selectChannel.list.length === 0 ? '选择渠道' : '修改渠道' }}
            </a-button>
            <a-button v-if="selectChannel.list.length > 0" @click="selectChannel.list = []">
              清空已选渠道
            </a-button>
            <div class="tips">添加渠道后，通过当前活码添加的外部联系人都将会打上已选择的渠道。</div>

            <div class="t-table" v-if="selectChannel.list.length" style="margin-top: 10px">
              <div class="t-header">已选渠道:{{ selectChannel.list.length }}</div>
              <div class="t-body">
                <!-- item -->
                <div class="item-staff" v-for="(item, index) in selectChannel.list" :key="index">
                  <TeamOutlined class="staff-icon" />
                  <span>{{ item.name }}</span>
                </div>
                <!-- item end -->
              </div>
              <!-- body end -->
            </div>
            <!-- table end -->
          </a-form-item>

          <!-- 展码规则 -->
          <a-form-item label="展码规则" name="activityType">
            <a-radio-group v-model:value="formData.activityType" :disabled="isEditCode">
              <a-radio v-for="item in ruleGroup" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <!-- 选择员工 -->
          <a-form-item
            v-if="[1, 2, 3, 4].includes(formData.activityType)"
            label=" "
            :colon="false"
            name="userList"
            key="userList"
          >
            <select-person-comp
              :selectedData="selectPerson.selectedData"
              :visible="selectPerson.visible"
              @submit="submitFn"
              @close="selectPerson.visible = false"
            />

            <a-button class="m-r-16" type="primary" @click="selectPerson.visible = true">
              {{ formData.userList.length === 0 ? '选择员工' : '修改员工' }}
            </a-button>
            <a-button v-if="formData.userList.length > 0" @click="clearStaff">
              清空已选员工
            </a-button>
            <div class="tips" v-if="formData.activityType == 5">
              如客人未添加 SA，展示去除掉门店下店经理、副店经理之后的门店二维码；<br />
              如已添加 SA，直接展示已添加 SA
              的头像、昵称及二维码。如果添加多个，展示最近添加一次添加的 SA 头像、昵称、二维码；
            </div>
            <div class="tips" v-else>{{ ruleGroup[formData.activityType - 1].explains }}</div>

            <div v-if="formData.userList.length" style="margin-top: 10px">
              <div class="t-table" v-if="formData.userList.length" style="margin-top: 10px">
                <div class="t-header">已选员工</div>
                <div class="t-body">
                  <!-- flex tag-->
                  <a-flex gap="4px 0" wrap="wrap">
                    <!-- user -->
                    <div class="item-staff" v-for="(item, index) in formData.userList" :key="index">
                      <UserOutlined class="staff-icon" />
                      <span>{{ item.name }}</span>
                    </div>
                    <!-- user end -->
                  </a-flex>
                  <!-- flex end -->
                </div>
              </div>
            </div>
          </a-form-item>

          <!-- 选择门店 -->
          <a-form-item
            v-if="formData.activityType === 5"
            label=" "
            :colon="false"
            name="storeList"
            key="storeList"
          >
            <select-store-comp
              :selectedData="formData.storeList"
              :visible="selectStore.visible"
              @submit="submitStoreFn"
              @close="selectStore.visible = false"
            />

            <a-button class="m-r-16" type="primary" @click="selectStore.visible = true">
              {{ formData.storeList.length === 0 ? '选择门店' : '修改门店' }}
            </a-button>
            <a-button v-if="formData.storeList.length > 0" @click="formData.storeList = []">
              清空已选门店
            </a-button>
            <div class="tips" v-if="formData.activityType == 5">
              如客人未添加 SA，展示去除掉门店下店经理、副店经理之后的门店二维码；<br />
              如已添加 SA，直接展示已添加 SA
              的头像、昵称及二维码。如果添加多个，展示最近添加一次添加的 SA 头像、昵称、二维码；
            </div>
            <div class="tips" v-else>{{ ruleGroup[formData.activityType - 1].explains }}</div>
            <!-- table -->
            <div class="t-table" v-if="formData.storeList.length" style="margin-top: 10px">
              <div class="t-header">已选门店:{{ formData.storeList.length }}</div>
              <div class="t-body">
                <!-- item -->
                <div class="item-staff" v-for="(item, index) in formData.storeList" :key="index">
                  <TeamOutlined class="staff-icon" />
                  <span>{{ item.name }}</span>
                </div>
                <!-- item end -->
              </div>
              <!-- body end -->
            </div>
          </a-form-item>

          <div class="form-title">欢迎语配置</div>
          <!-- 是否配置欢迎语 -->
          <a-form-item label="是否配置欢迎语" name="isWelcome">
            <a-switch
              v-model:checked="formData.wecomeMessageType"
              :checked-value="1"
              :un-checked-value="0"
            />
            <div class="tips">
              开启后，客人通过扫描企业微信二维码添加导购后，系统会向客户推送配置的欢迎语内容。
            </div>
          </a-form-item>
          <template v-if="formData.wecomeMessageType == 1">
            <a-form-item label="触发统一欢迎语" name="uniformType">
              <a-switch
                v-model:checked="formData.uniformType"
                :checked-value="1"
                :un-checked-value="0"
              />
              <div class="tips">
                关闭后，可针对会员或非会员客人配置不同的欢迎语内容；开启后将推送统一的回复内容。
              </div>
              <!-- 回复内容配置 -->
              <template v-if="formData.uniformType == 1">
                <div class="header">统一欢迎语内容配置</div>
                <div class="material-wrap">
                  <auto-answer-comp v-model:answer="sameAnswer" />
                </div>
              </template>
              <template v-if="formData.uniformType == 0">
                <!-- 非会员扫码添加导购后触发欢迎语内容配置 -->
                <div class="header"> 非会员扫码添加导购后触发欢迎语内容配置 </div>
                <div class="material-wrap mb20">
                  <auto-answer-comp v-model:answer="normalAnswer" />
                </div>
                <!-- 会员扫码添加导购后触发欢迎语内容配置 -->
                <div class="header">会员扫码添加导购后触发欢迎语内容配置</div>
                <div class="material-wrap">
                  <auto-answer-comp v-model:answer="vipAnswer" />
                </div>
              </template>
            </a-form-item>
          </template>
        </a-form>
      </a-col>
    </a-row>
    <a-divider style="margin-top: 100px" />
    <div class="footer-box">
      <a-button :loading="loading" :disabled="isUpload" type="primary" @click="submitForm">
        保存
      </a-button>
      <a-button @click="handleCancel">取消</a-button>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, watch, computed, nextTick } from 'vue';
  import { useRoute } from 'vue-router';
  import dayjs, { Dayjs } from 'dayjs';
  import selectPersonComp from '@/components/common-select-person/index.vue';
  import selectStoreComp from '@/components/common-select-store/index.vue';
  import selectChannelComp from '@/components/common-select-channel/index.vue';
  import autoAnswerComp from '@/components/common-auto-answer/index.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { addActivityApi, getActivityDetailApi, updateActivityApi } from '@/api/sys/recommendCode';
  import { TeamOutlined, UserOutlined } from '@ant-design/icons-vue';
  import { buildUUID } from '@/utils/uuid';
  import { cloneDeep } from 'lodash-es';

  type RangeValue = [Dayjs, Dayjs];
  interface Answer {
    textContent: string;
    materialList: Array<any>;
    isUpload: boolean;
    isChecking: boolean;
  }
  const { createMessage } = useMessage();
  const { warning, success } = createMessage;
  const route = useRoute();
  const backList = () => {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/recommendCode/list`,
      true,
    );
  };
  const loading = ref(false);
  const isUpload = ref(false);
  const isEditCode = ref(false);

  const selectPerson = reactive({
    visible: false,
    selectedData: {
      member: [],
      dept: [],
    },
  });

  const selectStore = reactive({
    visible: false,
  });

  const selectChannel = reactive({
    visible: false,
    list: [],
  });

  const expireTime = ref<RangeValue>();
  const formRef = ref();

  const formData = reactive({
    /**是否长期有效 */
    longTermEffect: false,
    /** 开始时间 */
    startTime: 0,
    /**结束时间 */
    endTime: 0,
    /** 活码名称 */
    activityName: '',
    /** 展码规则 */
    activityType: 1,
    /** 渠道ids */
    channels: [],
    /** 选择员工 */
    userList: [],
    /** 选择门店 */
    storeList: [],
    /** 是否配置 欢迎语   1:打开，0:关闭*/
    wecomeMessageType: 1,
    /** 是否统一配置 欢迎语 */
    uniformType: 0,
    /** 活码生成状态 0 -未开始 1-生效中 2-已结束 3-已终止 */
    status: 0,
    /** 展码规则的员工/门店数据 */
    // qrCodeParamList: {
    //   userIds: [],
    //   storeId: [],
    // },
  });

  /** rule 数组 */
  const ruleGroup = reactive([
    {
      label: '剔除已添加成员',
      value: 1,
      explains:
        '查询客人已添加的成员是否在当前活动配置成员内，当客人存在已添加成员时，从剩余成员中随机一个成员码进行展示',
    },
    {
      label: '优先展示最近添加成员',
      value: 2,
      explains:
        '查询客人已添加的成员比较添加好友时间，展示据当前时间最近的成员二维码进行展示，如客人无已添加的成员，将随机一个成员码进行展示',
    },
    {
      label: '优先展示最早添加成员',
      value: 3,
      explains:
        '查询客人已添加的成员比较添加好友时间，展示客人添加最早的成员二维码，如客人无已添加的成员，将随机一个成员码进行展示',
    },
    // {
    //   label: '优先展示prefer SA',
    //   value: 4,
    //   explains:
    //     '查询客人是否有prefer SA，当客人有prefer SA时展示prefer SA，否则将随机一个成员码进行展示',
    // },
    {
      label: 'BV定制(附近门店)',
      value: 5,
    },
  ]);

  /** 设置禁用日期回调（只能选择当天及之后） */
  const disabledDate = (current: Dayjs) => {
    return current && current < dayjs().startOf('day');
  };

  /** 有效期时间选择表单禁用状态 */
  const dateDisabled = computed(() => {
    const { longTermEffect, status } = formData || {};
    if (!isEditCode.value) {
      // 创建时不限制
      return longTermEffect;
    } else if (longTermEffect || status == 2 || status == 3) {
      // 有效期为长期有效，状态已结束、已终止时禁用
      return true;
    } else if (status == 1) {
      // 状态生效中时，开始时间禁用
      return [true, false];
    } else {
      return false;
    }
  });

  /** 有效期字段校验（这里校验的实际是 longTermEffect、satartTime、endTime 三个参数） */
  const dateValidator = () => {
    const { longTermEffect, startTime, endTime, status } = formData;
    if (!longTermEffect && (!startTime || !endTime)) {
      return Promise.reject('请设置有效期');
    } else {
      if (startTime && endTime && startTime < dayjs().unix()) {
        if (isEditCode.value && status != 0) return Promise.resolve(); // 编辑时，除了未开始状态，其余状态开始时间均不可调整，所以跳过验证
        warning('有效期开始时间不能晚于当前时间');
        return Promise.reject('有效期开始时间不能晚于当前时间');
      }
      return Promise.resolve();
    }
  };

  /** 门店校验 */
  const storeValidator = (rule, value) => {
    if (formData.activityType === 3) {
      if (Array.isArray(value) && value.length > 0) {
        return Promise.resolve();
      } else {
        return Promise.reject('请选择门店');
      }
    } else {
      return Promise.resolve();
    }
  };

  /** 员工 校验 */
  const userValidator = (rule, value) => {
    if ([1, 2, 3, 4].includes(formData.activityType)) {
      if (Array.isArray(value) && value.length > 0) {
        return Promise.resolve();
      } else {
        return Promise.reject('请选择员工');
      }
    } else {
      return Promise.resolve();
    }
  };

  /** 规则 */
  const rules = reactive({
    activityName: [{ required: true, message: '活动名称不能为空', trigger: 'blur' }],
    longTermEffect: [{ required: true, validator: dateValidator }],
    userList: [{ required: true, validator: userValidator }],
    storeList: [{ required: true, validator: storeValidator }],
  });

  /** 统一欢迎语内容配置 */
  const sameAnswer: Answer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });

  /** 会员欢迎语 */
  const vipAnswer: Answer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });

  /** 非会员 */
  const normalAnswer: Answer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });

  watch(
    () => expireTime.value,
    () => {
      if (expireTime.value && expireTime.value[0] && expireTime.value[1]) {
        formData.startTime = expireTime.value[0].unix();
        formData.endTime = expireTime.value[1].unix();
      } else {
        formData.startTime = 0;
        formData.endTime = 0;
      }
    },
    { deep: true },
  );

  /** 单独检验选择员工 */
  watch(
    () => formData.userList,
    () => {
      formRef.value.validateFields('userList');
    },
  );

  /**
   * 校验表单有效期
   */
  const validatorDateForm = async () => {
    // 校验有效期
    await nextTick();
    formRef.value.validateFields('longTermEffect');
  };

  /** 选择长期有效 */
  const expireChange = () => {
    if (formData.longTermEffect) {
      expireTime.value = undefined;
    }
    validatorDateForm();
  };
  const getDetails = async (id) => {
    try {
      const res = await getActivityDetailApi(id);
      if (res) {
        mapBasicDetails(res);
        mapChannels(res.channels || []);
        mapSelectContent(res.activityType, res.selectContent);
        mapWelcomeMessages(
          res.wecomeMessageType,
          res.uniformType,
          res.uniformWecomeMessageContent,
          res.wecomeMessageContent,
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  const mapBasicDetails = (res) => {
    const { activityName, activityType, startTime, endTime, longTermEffect, uniformType, status } =
      res;

    if (startTime && endTime) {
      expireTime.value = [dayjs(startTime * 1000), dayjs(endTime * 1000)];
    }

    formData.longTermEffect = longTermEffect == 1;
    formData.uniformType = uniformType;
    formData.activityName = activityName;
    formData.activityType = activityType;
    formData.wecomeMessageType = res.wecomeMessageType;
    formData.status = status;
  };

  const mapChannels = (channels) => {
    if (channels && channels.length) {
      selectChannel.list = channels.map((item) => ({
        id: item.channelId,
        name: item.channelName,
        channelId: item.channelId,
      }));
    }
  };

  const mapSelectContent = (activityType, selectContent) => {
    if ([1, 2, 3, 4].includes(activityType)) {
      const selectPersonData = JSON.parse(selectContent);
      formData.userList = selectPersonData.userList;
      selectPerson.selectedData = selectPersonData.selectedData;
    } else {
      const storeListData = JSON.parse(selectContent).storeList.map((store) => ({
        wecomStoreId: store.wecomStoreId,
        name: store.name,
      }));
      formData.storeList = storeListData;
    }
  };

  const mapWelcomeMessages = (
    wecomeMessageType,
    uniformType,
    uniformWecomeMessageContent,
    wecomeMessageContent,
  ) => {
    if (wecomeMessageType === 1) {
      setAnswerAndMaterialList(
        uniformType == 1 ? sameAnswer : normalAnswer,
        uniformWecomeMessageContent,
      );

      if (uniformType !== 1) {
        setAnswerAndMaterialList(vipAnswer, wecomeMessageContent);
      }
    }
  };

  // 抽象解析和设置文本内容及材料列表的逻辑
  const setAnswerAndMaterialList = (answer, messageContent) => {
    const messageData = JSON.parse(messageContent);
    answer.textContent = messageData[0]?.text?.content || '';
    answer.materialList = dealMaterialList(messageData);
  };
  /**
   * 处理素材列表
   *
   * @param list 素材列表
   * @returns 处理后的素材列表
   */
  function dealMaterialList(list) {
    return list.slice(1).map((item) => {
      if (['link', 'miniprogram'].includes(item.msgtype)) {
        let temp = {
          ...item[item.msgtype],
          msgtype: item.msgtype,
        };
        temp.fileList = [
          {
            uid: buildUUID(),
            name: item[item.msgtype].name,
            status: 'done',
            url: item[item.msgtype].picurl || item[item.msgtype].pic_url,
          },
        ];
        return temp;
      } else {
        return {
          ...item[item.msgtype],
          msgtype: item.msgtype,
        };
      }
    });
  }
  /** 选择员工 */
  const submitFn = (data) => {
    const { member, dept } = data.rawData;
    selectPerson.selectedData = { member, dept };
    formData.userList = data.list;
    selectPerson.visible = false;
  };

  /** 清除员工 */
  const clearStaff = () => {
    selectPerson.selectedData = { member: [], dept: [] };
    formData.userList = [];
  };

  /** 选中门店 */
  const submitStoreFn = (data) => {
    formData.storeList = cloneDeep(data);
    selectStore.visible = false;
  };

  /** 选中渠道 */
  const submitChannelFn = (data) => {
    console.log('submitChannelFn', data);
    selectChannel.list = data.map((item) => {
      return {
        channelId: item.id,
        id: item.id,
        name: item.name,
      };
    });
    selectChannel.visible = false;
  };

  /** 保存 - 验证欢迎语 */
  const validateAnswer = () => {
    // 未开启欢迎语配置
    if (formData.wecomeMessageType !== 1) return true;

    // 开启欢迎语
    if (formData.uniformType === 1) {
      // 校验uniformType
      const contentFlag = sameAnswer.textContent !== '';
      const mateFlag = sameAnswer.materialList.length > 0;
      if (!mateFlag && !contentFlag) {
        warning('请填写消息内容或配置消息素材!');
        return false;
      }
      return true;
    } else {
      // 校验vipAnswer、normalAnswer
      const contentFlag = vipAnswer.textContent !== '' && normalAnswer.textContent !== '';
      const mateFlag = vipAnswer.materialList.length > 0 && normalAnswer.materialList.length > 0;
      if (!mateFlag && !contentFlag) {
        warning('请填写消息内容或配置消息素材!');
        return false;
      }
      return true;
    }
  };

  /** 保存 - 验证、组装数据 */
  const validateSave = () => {
    formRef.value
      .validate()
      .then(() => {
        if (validateAnswer()) {
          const params: any = {
            ...formData,
            longTermEffect: formData.longTermEffect ? 1 : 0,
            autoPass: 1,
            wecomeMessageContent: '',
            normalWecomeMessageContent: '',
          };
          delete params.status;

          /** 活码规则 */
          if ([1, 2, 3, 4].includes(formData.activityType)) {
            // 关联员工
            params.qrCodeParam = formData.userList.map((item: { id: string }) => item.id).join(',');
            // 选择的部门数据
            params.departmentIds = selectPerson.selectedData.dept.map(
              (item: { id: string }) => item.id,
            );

            const selectPersonData = JSON.parse(JSON.stringify(selectPerson));
            selectPersonData.userList = formData.userList;
            delete selectPersonData.visible;
            delete selectPersonData.submit;

            params.selectContent = JSON.stringify(selectPersonData);
          } else {
            //关联门店
            const selectStoreData = {
              storeList: formData.storeList.map((store: any) => ({
                wecomStoreId: store.wecomStoreId,
                name: store.name,
              })),
            };
            params.qrCodeParam = formData.storeList
              .map((item: { wecomStoreId: string }) => item.wecomStoreId)
              .join(',');

            params.selectContent = JSON.stringify(selectStoreData);
          }

          delete params.userList;
          delete params.storeList;

          // 关联渠道
          if (selectChannel.list.length > 0) {
            params.channels = selectChannel.list.map((item: { id: string }) => item.id).join(',');
          } else {
            params.channels = '';
          }

          // 开启欢迎语
          if (formData.wecomeMessageType === 1) {
            // 统一
            if (formData.uniformType === 1) {
              const newMaterialList: any = sameAnswer.materialList.map((item: any) => {
                if (item.msgtype === 'miniprogram' || item.msgtype === 'link') {
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  delete temp[item.msgtype].fileList;
                  return temp;
                } else {
                  //图片、视频、文件
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  return temp;
                }
              });

              newMaterialList.unshift({
                msgtype: 'text',
                text: { content: sameAnswer.textContent },
              });
              params.uniformWecomeMessageContent = JSON.stringify(newMaterialList);
            } else {
              // 会员
              const vipMaterialList: any = vipAnswer.materialList.map((item: any) => {
                if (item.msgtype === 'miniprogram' || item.msgtype === 'link') {
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  delete temp[item.msgtype].fileList;
                  return temp;
                } else {
                  //图片、视频、文件
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  return temp;
                }
              });
              vipMaterialList.unshift({
                msgtype: 'text',
                text: { content: vipAnswer.textContent },
              });
              params.wecomeMessageContent = JSON.stringify(vipMaterialList);
              // 非会员
              const normalMaterialList: any = normalAnswer.materialList.map((item: any) => {
                if (item.msgtype === 'miniprogram' || item.msgtype === 'link') {
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  delete temp[item.msgtype].fileList;
                  return temp;
                } else {
                  //图片、视频、文件
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  return temp;
                }
              });
              normalMaterialList.unshift({
                msgtype: 'text',
                text: { content: normalAnswer.textContent },
              });
              params.uniformWecomeMessageContent = JSON.stringify(normalMaterialList);
            }
          }
          handleSave(params);
        }
      })
      .catch((error) => {
        console.log('error', error);
      });
  };

  /** 保存/编辑 - 接口 */
  const handleSave = async (params) => {
    loading.value = true;
    try {
      if (!route.query.id || route.query.type === 'copy') {
        await addActivityApi(params);
        success('创建成功');
        handleCancel();
      } else {
        await updateActivityApi(route.query.id, params);
        success('编辑成功');
        handleCancel();
      }
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  /** 保存 - 按钮事件 */
  const submitForm = () => {
    validateSave();
  };

  /** 取消操作 */
  const handleCancel = () => {
    formData.activityName = '';

    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/recommendCode/list`,
      true,
    );
  };

  /** 生命周期 mounted */
  onMounted(() => {
    // 有id 则是编辑
    if (route.query.id) {
      getDetails(route.query.id);
      // 仅编辑-不能切换展码规则
      isEditCode.value = true;
    }
  });
</script>

<style lang="scss">
  @import '@/theme/index.scss';
</style>

<style lang="scss" scoped>
  .m-l-16 {
    margin-left: 16px;
  }

  .m-r-16 {
    margin-right: 16px;
  }

  .code-create-container {
    margin: 16px 24px;
    padding: 0;
    border-radius: 0;
    background-color: var(--component-background-color);

    /** 基础信息、欢迎语配置-标题 */
    .form-title {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 700;
    }

    .form-desc {
      margin-bottom: 24px;
      color: #a12117;
      font-size: 12px;
      line-height: 32px;
    }

    .tips {
      margin: 5px 0;
      color: rgba(0 0 0 / 85%);
      font-size: 12px;
    }

    .item-staff {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      min-width: 87px;
      height: 28px;
      margin-right: 10px;
      margin-bottom: 10px;
      padding: 0 10px;
      border: solid 1px #d6dffd;
      background-color: #f0f3fe;

      .staff-icon {
        width: 14px;
        height: 14px;
        margin-right: 5px;
      }
    }

    /** 底部按钮区域 */
    .footer-box {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .#{$prefix}-btn {
        margin-right: 24px;
        margin-bottom: 10px;
      }
    }

    /** 内容配置 */
    .header {
      padding: 10px 15px;
      background-color: #ddd;
      font-weight: 700;
    }

    .material-wrap {
      padding: 24px 24px 24px 0;
      border: 1px solid #ddd;
    }
  }

  .t-table {
    .t-header {
      height: 40px;
      padding-left: 24px;
      border-bottom: #f0f0f0;
      background: #fafafa;
      line-height: 40px;
    }

    .t-body {
      padding: 10px 24px;
      border-bottom: #f0f0f0;
    }
  }
</style>
