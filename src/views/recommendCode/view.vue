<template>
  <div class="view-container">
    <a-row>
      <a-col :span="20">
        <a-form :disabled="true" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
          <div class="form-title">基础信息</div>
          <!-- 活码规则名称 -->
          <a-form-item label="活动名称" name="activityName">
            {{ formData.activityName }}
          </a-form-item>

          <!-- 有效期 -->
          <a-form-item label="有效期">
            <span v-if="formData.longTermEffect == 1">长期有效</span>
            <span v-else
              >{{ formatToDateTime(formData.startTime) }} -
              {{ formatToDateTime(formData.endTime) }}</span
            >
          </a-form-item>

          <!-- 关联渠道 -->
          <a-form-item
            v-if="formData.channels.length"
            label="关联渠道"
            name="relations"
            key="relations"
          >
            <div class="t-table" v-if="formData.channels.length" style="margin-top: 10px">
              <div class="t-header">已选渠道: {{ formData.channels.length }}</div>
              <div class="t-body">
                <!-- item -->
                <div class="item-staff" v-for="(item, index) in formData.channels" :key="index">
                  <TeamOutlined class="staff-icon" />
                  <span>{{ item.channelName }}</span>
                </div>
                <!-- item end -->
              </div>
              <!-- body end -->
            </div>
          </a-form-item>

          <!-- 展码规则 -->
          <a-form-item label="展码规则" name="activityType">
            <a-radio-group v-model:value="formData.activityType" :disabled="true">
              <a-radio v-for="item in ruleGroup" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <!-- 选择员工 -->
          <a-form-item
            label=" "
            :colon="false"
            name="staff"
            key="staff"
            v-if="[1, 2, 3, 4].includes(formData.activityType)"
          >
            <div class="t-table" v-if="selectPerson.userList.length" style="margin-top: 10px">
              <div class="t-header">已选员工: {{ selectPerson.userList.length }}</div>
              <div class="t-body">
                <!-- flex tag-->
                <a-flex gap="4px 0" wrap="wrap">
                  <!-- user -->
                  <div
                    class="item-staff"
                    v-for="(item, index) in selectPerson.userList"
                    :key="index"
                  >
                    <UserOutlined class="staff-icon" />
                    <span>{{ item.name }}</span>
                  </div>
                  <!-- user end -->
                </a-flex>
                <!-- flex end -->
              </div>
            </div>
          </a-form-item>

          <!-- 选择门店 -->
          <a-form-item
            label=" "
            :colon="false"
            name="store"
            key="store"
            v-if="formData.activityType === 5"
          >
            <div class="t-table" v-if="selectStore.storeList.length" style="margin-top: 10px">
              <div class="t-header">已选门店: {{ selectStore.storeList.length }}</div>
              <div class="t-body">
                <!-- item -->
                <div class="item-staff" v-for="(item, index) in selectStore.storeList" :key="index">
                  <TeamOutlined class="staff-icon" />
                  <span>{{ item.storeName }}</span>
                </div>
                <!-- item end -->
              </div>
              <!-- body end -->
            </div>
          </a-form-item>

          <div class="form-title" v-if="formData.wecomeMessageType == 1">欢迎语配置</div>

          <a-form-item label="触发统一欢迎语" name="uniformType">
            <a-switch
              class="m-t-1 m-b-5"
              v-model:checked="formData.uniformType"
              :checked-value="1"
              :un-checked-value="0"
            />
            <template v-if="formData.wecomeMessageType == 1">
              <!-- 回复内容配置 -->
              <template v-if="formData.uniformType == 1">
                <div class="header">统一欢迎语内容配置</div>
                <div class="material-wrap">
                  <auto-answer-comp v-model:answer="sameAnswer" disabled />
                </div>
              </template>
              <template v-if="formData.uniformType == 0">
                <!-- 非会员扫码添加导购后触发欢迎语内容配置 -->
                <div class="header"> 非会员扫码添加导购后触发欢迎语内容配置 </div>
                <div class="material-wrap mb20">
                  <auto-answer-comp v-model:answer="normalAnswer" disabled />
                </div>
                <!-- 会员扫码添加导购后触发欢迎语内容配置 -->
                <div class="header">会员扫码添加导购后触发欢迎语内容配置</div>
                <div class="material-wrap">
                  <auto-answer-comp v-model:answer="vipAnswer" disabled />
                </div>
              </template>
            </template>
          </a-form-item>
        </a-form>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import autoAnswerComp from '@/components/common-auto-answer/index.vue';
  import { TeamOutlined, UserOutlined } from '@ant-design/icons-vue';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { getActivityDetailApi } from '@/api/sys/recommendCode';
  import { buildUUID } from '@/utils/uuid';

  interface Answer {
    textContent: string;
    materialList: Array<any>;
    isUpload: boolean;
    isChecking: boolean;
  }

  const route = useRoute();

  const welcomeId = ref('');

  const selectPerson = reactive({
    userList: [],
    selectedData: {
      member: [],
      dept: [],
    },
  });

  const selectStore = reactive({
    storeList: [],
  });

  const formData = reactive({
    activityName: '',
    longTermEffect: 1,
    startTime: 0,
    endTime: 0,
    channels: [],
    activityType: 1,
    relations: [],
    wecomeMessageType: 1,
    uniformType: 0,
  });

  const ruleGroup = reactive([
    {
      label: '剔除已添加成员',
      value: 1,
      explains:
        '查询客人已添加的成员是否在当前活动配置成员内，当客人存在已添加成员时，从剩余成员中随机一个成员码进行展示',
    },
    {
      label: '优先展示最近添加成员',
      value: 2,
      explains:
        '查询客人已添加的成员比较添加好友时间，展示据当前时间最近的成员二维码进行展示，如客人无已添加的成员，将随机一个成员码进行展示',
    },
    {
      label: '优先展示最早添加成员',
      value: 3,
      explains:
        '查询客人已添加的成员比较添加好友时间，展示客人添加最早的成员二维码，如客人无已添加的成员，将随机一个成员码进行展示',
    },
    // {
    //   label: '优先展示prefer SA',
    //   value: 4,
    //   explains:
    //     '查询客人是否有prefer SA，当客人有prefer SA时展示prefer SA，否则将随机一个成员码进行展示',
    // },
    {
      label: 'BV定制(附近门店)',
      value: 5,
    },
  ]);
  const sameAnswer: Answer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });

  const vipAnswer: Answer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });

  const normalAnswer: Answer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });
  function dealMaterialList(list) {
    return list.slice(1).map((item) => {
      if (['link', 'miniprogram'].includes(item.msgtype)) {
        let temp = {
          ...item[item.msgtype],
          msgtype: item.msgtype,
        };
        temp.fileList = [
          {
            uid: buildUUID(),
            name: item[item.msgtype].name,
            status: 'done',
            url: item[item.msgtype].picurl || item[item.msgtype].pic_url,
          },
        ];
        return temp;
      } else {
        return {
          ...item[item.msgtype],
          msgtype: item.msgtype,
        };
      }
    });
  }
  const getDetails = async (id) => {
    try {
      const res = await getActivityDetailApi(id);
      if (res) {
        mapBasicDetails(res);
        mapSelectContent(res.activityType, res.selectContent);
        mapWelcomeMessages(
          res.wecomeMessageType,
          res.uniformType,
          res.uniformWecomeMessageContent,
          res.wecomeMessageContent,
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  const mapBasicDetails = (res) => {
    const {
      activityName,
      activityType,
      startTime,
      endTime,
      channels = [],
      wecomeMessageType,
      uniformType,
      welcomeId: id,
      longTermEffect,
    } = res;

    formData.startTime = longTermEffect === 0 && startTime ? startTime * 1000 : 0;
    formData.endTime = longTermEffect === 0 && endTime ? endTime * 1000 : 0;
    formData.longTermEffect = longTermEffect;
    formData.activityName = activityName;
    formData.channels = channels;
    formData.activityType = activityType;
    formData.wecomeMessageType = wecomeMessageType;
    formData.uniformType = uniformType;
    welcomeId.value = id;
  };

  const mapSelectContent = (activityType, selectContent) => {
    if ([1, 2, 3, 4].includes(activityType)) {
      const { userList, selectedData } = JSON.parse(selectContent);
      selectPerson.userList = userList;
      selectPerson.selectedData = selectedData;
    } else {
      const storeListData = JSON.parse(selectContent).storeList.map((store) => ({
        id: store.id,
        storeName: store.name,
      }));
      selectStore.storeList = storeListData;
    }
  };

  const mapWelcomeMessages = (
    wecomeMessageType,
    uniformType,
    uniformWecomeMessageContent,
    wecomeMessageContent,
  ) => {
    if (wecomeMessageType === 1) {
      setAnswerAndMaterialList(
        uniformType === 1 ? sameAnswer : normalAnswer,
        uniformWecomeMessageContent,
      );

      if (uniformType !== 1) {
        setAnswerAndMaterialList(vipAnswer, wecomeMessageContent);
      }
    }
  };
  // 抽象解析和设置文本内容及材料列表的逻辑
  const setAnswerAndMaterialList = (answer, messageContent) => {
    const messageData = JSON.parse(messageContent);
    answer.textContent = messageData[0]?.text?.content || '';
    answer.materialList = dealMaterialList(messageData);
  };

  onMounted(() => {
    if (route.query.id) {
      getDetails(route.query.id);
    }
  });
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="less" scoped>
  .view-container {
    border: 0;
    margin: 0;

    /** 基础信息、欢迎语配置-标题 */
    .form-title {
      font-size: 12px;
      margin-bottom: 16px;
      font-weight: 700;
    }

    .tips {
      margin: 5px 0;
      color: rgba(0 0 0 / 85%);
      font-size: 12px;
    }

    .item-staff {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      min-width: 87px;
      height: 28px;
      margin-right: 10px;
      margin-bottom: 10px;
      padding: 0 10px;
      border: solid 1px #d6dffd;
      background-color: #f0f3fe;

      .staff-icon {
        width: 14px;
        height: 14px;
        margin-right: 5px;
      }
    }

    /** 底部按钮区域 */
    .footer-box {
      padding: 20px 100px 40px;
      border-top: 1px solid #f2f2f2;
    }

    /** 内容配置 */
    .header {
      margin-top: 16px;
      padding: 10px 15px;
      background-color: #ddd;
      font-weight: 700;
    }

    .material-wrap {
      padding: 20px 20px 20px 0;
      border: 1px solid #ddd;
    }
  }

  .t-table {
    .t-header {
      height: 40px;
      padding-left: 20px;
      border-bottom: #f0f0f0;
      background: #fafafa;
      line-height: 40px;
    }

    .t-body {
      padding: 10px 20px;
      border-bottom: #f0f0f0;
    }
  }
</style>
