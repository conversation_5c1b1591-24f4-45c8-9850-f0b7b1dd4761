<template>
  <a-card class="code-create-container">
    <a-page-header style="padding: 0" title="推优活动详情" @back="backList" />
    <a-tabs v-model:activeKey="activeKey" class="tabs">
      <a-tab-pane :key="1" tab="基础信息" />
      <a-tab-pane :key="2" tab="访问数据" />
    </a-tabs>
    <template v-if="activeKey == 1">
      <View />
    </template>

    <template v-if="activeKey == 2">
      <a-row :gutter="16" class="m-b-16">
        <a-col :span="12">
          <a-card>
            <a-spin :spinning="spinningLeft" />
            <div>
              <a-range-picker
                :value="hackValue || dateRange"
                format="YYYY/MM/DD"
                :disabled-date="disabledDate"
                @open-change="onOpenChange"
                @change="onChange"
                @calendar-change="onCalendarChange"
              />
            </div>
            <div class="m-t-16 grid">
              <a-card-grid class="statistic" v-for="staticItem in staticList" :key="staticItem.key">
                <div class="title"
                  >{{ staticItem.label }}

                  <a-popover>
                    <template #content>{{ staticItem.explain }}</template>
                    <InfoCircleOutlined />
                  </a-popover>
                </div>
                <CountTo
                  v-if="staticItem.key === 'c' && staticItem.value < 1"
                  :startVal="0"
                  :endVal="staticItem.value"
                  :duration="0"
                  :decimals="2"
                />
                <CountTo
                  v-else-if="staticItem.key === 'c' && staticItem.value >= 1"
                  :startVal="0"
                  :endVal="staticItem.value"
                  :duration="800"
                  :decimals="2"
                />
                <CountTo
                  v-else
                  :startVal="0"
                  :endVal="staticItem.value"
                  :duration="800"
                  :decimals="0"
                />
              </a-card-grid>
            </div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card>
            <a-spin :spinning="spinningRight" />
            <div>今日</div>
            <div class="m-t-16 grid">
              <a-card-grid
                class="statistic"
                v-for="staticItem in staticRightList"
                :key="staticItem.key"
              >
                <div class="title"
                  >{{ staticItem.label }}

                  <a-popover>
                    <template #content>{{ staticItem.explain }}</template>
                    <InfoCircleOutlined />
                  </a-popover>
                </div>
                <CountTo
                  v-if="staticItem.key === 'c' && staticItem.value < 1"
                  :startVal="0"
                  :endVal="staticItem.value"
                  :duration="0"
                  :decimals="2"
                />
                <CountTo
                  v-else-if="staticItem.key === 'c' && staticItem.value >= 1"
                  :startVal="0"
                  :endVal="staticItem.value"
                  :duration="800"
                  :decimals="2"
                />
                <CountTo
                  v-else
                  :startVal="0"
                  :endVal="staticItem.value"
                  :duration="800"
                  :decimals="0"
                />
              </a-card-grid>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-card>
        <a-form layout="inline" class="m-b-16">
          <a-form-item>
            <a-range-picker
              :value="hackValue || createDate"
              format="YYYY/MM/DD"
              :disabled-date="disabledDate"
              @open-change="onOpenChange"
              @change="onTableChange"
              @calendar-change="onCalendarChange"
            />
          </a-form-item>

          <a-form-item>
            <a-input-group compact>
              <a-select
                style="width: 40%"
                v-model:value="currentLableValue"
                :options="labelList"
                @change="
                  () => {
                    searchParams.baName = '';
                    searchParams.userid = '';
                  }
                "
              />
              <a-input
                v-model:value="searchParams[currentLableValue == 1 ? 'baName' : 'userid']"
                :placeholder="currentLableValue == 1 ? '请输入成员姓名' : '请输入userid'"
                style="width: 60%"
              />
            </a-input-group>
          </a-form-item>

          <a-form-item>
            <a-button type="primary" @click="handleSearch"> 查询 </a-button>
            <a-button class="m-l-16" @click="handleReset">重置</a-button>
            <a-button class="m-l-16" @click="handleExport"> 导出活动数据 </a-button>
            <a-button class="m-l-16" @click="handleDayExport"> 导出每日添加数据 </a-button>
          </a-form-item>
        </a-form>

        <a-table
          :loading="loading"
          :columns="columns"
          :pagination="pagination"
          :data-source="dataList"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'operation'">
              <a-button
                type="text"
                :disabled="record.status == 3 || record.status == 4"
                @click="toCustom(record)"
                >查看客人数据</a-button
              >
            </template>
          </template>
        </a-table>
      </a-card>
    </template>
  </a-card>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, watch, nextTick } from 'vue';
  import { useRoute } from 'vue-router';
  import { CountTo } from '@/components/common-count-to';
  import { downloadByData } from '@/utils/file/download';
  import {
    getStaticsApi,
    getStaticsListApi,
    exportListApi,
    exportDayApi,
  } from '@/api/sys/recommendCode';
  import dayjs, { Dayjs } from 'dayjs';
  import { InfoCircleOutlined } from '@ant-design/icons-vue';
  import { formatToDate } from '@/utils/dateUtil';
  import View from './view.vue';

  type RangeValue = [Dayjs, Dayjs];

  const route = useRoute();
  const activeKey = ref(1);
  const dateRange = ref<[Dayjs, Dayjs]>([
    dayjs().subtract(7, 'day').startOf('day'),
    dayjs().subtract(1, 'day').endOf('day'),
  ]);

  const tempDateRange = ref<RangeValue>(); //日期范围引用
  // 创建一个响应式的值引用，用于内部的日期范围逻辑
  const hackValue = ref<RangeValue>();

  const disabledDate = (current: Dayjs) => {
    const yesterday = dayjs().endOf('day').subtract(1, 'days');
    if (!tempDateRange.value || (tempDateRange.value as any).length === 0) {
      return current > yesterday;
    }
    const tooLate = tempDateRange.value[0] && current.diff(tempDateRange.value[0], 'days') > 30;
    const tooEarly = tempDateRange.value[1] && tempDateRange.value[1].diff(current, 'days') > 30;
    return tooEarly || tooLate || current > yesterday;
  };
  // 当RangePicker的打开状态变化时调用
  const onOpenChange = (open: boolean) => {
    if (open) {
      tempDateRange.value = [] as any;
      hackValue.value = [] as any;
    } else {
      hackValue.value = undefined;
    }
  };

  const onChange = (val: RangeValue) => {
    dateRange.value = val;
  };
  const onTableChange = (val: RangeValue) => {
    createDate.value = val;
  };

  const onCalendarChange = (val: RangeValue) => {
    tempDateRange.value = val;
  };

  const createDate = ref<[Dayjs, Dayjs]>([
    dayjs().subtract(7, 'day').startOf('day'),
    dayjs().subtract(1, 'day').endOf('day'),
  ]);
  const backList = () => {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/recommendCode/list`,
      true,
    );
  };
  const codeRuleId = ref('');
  const codeName = ref('');
  const staticList = reactive([
    {
      key: 'a',
      label: '总添加人数',
      explain: '通过当前活动添加客户总人数',
      value: 0,
    },
    {
      key: 'b',
      label: '活动成员人数',
      explain: '当前活动选择员工范围内SA总人数',
      value: 0,
    },
    {
      key: 'c',
      label: '平均添加客户数',
      explain: '计算每个成员平均添加客人数',
      value: 0,
    },
    {
      key: 'd',
      label: '净增好友数',
      explain:
        '对总添加人数进行去重（如果已经通过活动进来过，后续再次进来净增不+1，且不考虑流失场景）',
      value: 0,
    },
    {
      key: 'e',
      label: '总请求次数',
      explain: '通过API获取当前活动推优码请求次数',
      value: 0,
    },
  ]);
  const staticRightList = reactive([
    {
      key: 'a',
      label: '添加人数',
      explain: '通过当前活动添加客户总人数',
      value: 0,
    },
    {
      key: 'b',
      label: '活动成员人数',
      explain: '当前活动选择员工范围内SA总人数',
      value: 0,
    },
    {
      key: 'c',
      label: '平均添加客户数',
      explain: '计算每个成员平均添加客人数',
      value: 0,
    },
    {
      key: 'd',
      label: '净增好友数',
      explain:
        '对总添加人数进行去重（如果已经通过活动进来过，后续再次进来净增不+1，且不考虑流失场景）',
      value: 0,
    },
    {
      key: 'e',
      label: '总请求次数',
      explain: '通过API获取当前活动推优码请求次数',
      value: 0,
    },
  ]);

  const searchParams = reactive({
    baName: '',
    userid: '',
    startTime: 0,
    endTime: 0,
  });

  const labelList = reactive([
    {
      label: '姓名',
      value: 1,
    },
    {
      label: 'userid',
      value: 2,
    },
  ]);
  const currentLableValue = ref(1);

  const dataList = ref<any[]>([]);
  const columns = [
    {
      title: 'userid',
      dataIndex: 'userid',
      key: 'userid',
      ellipsis: true,
      width: 140,
    },
    {
      title: '成员姓名',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      width: 180,
    },
    {
      title: '累计添加人数',
      dataIndex: 'countNumber',
      key: 'countNumber',
      ellipsis: true,
    },
    {
      title: '昨日添加人数',
      dataIndex: 'yesterdayFriendNumber',
      key: 'yesterdayFriendNumber',
      ellipsis: true,
    },
    {
      title: '今日已添加人数',
      dataIndex: 'todayFriendNumber',
      key: 'todayFriendNumber',
      ellipsis: true,
    },
    {
      title: '总展码次数',
      dataIndex: 'viewNumber',
      key: 'viewNumber',
      ellipsis: true,
    },

    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      align: 'center',
    },
  ];
  const pagination = ref({
    total: 0,
    current: 1,
    pageSize: 10,
    onChange: pageChange,
    onShowSizeChange: pageSizeChange,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条数据`,
  });
  const loading = ref(false);
  const spinningLeft = ref(false);
  const spinningRight = ref(false);

  function pageChange(page, size) {
    pagination.value.current = page;
    pagination.value.pageSize = size;
    getList();
  }
  function pageSizeChange(current, size) {
    pagination.value.current = 1;
    pagination.value.pageSize = size;
    handleSearch();
  }
  function handleSearch() {
    pagination.value.current = 1;
    getList();
  }
  async function handleReset() {
    searchParams.baName = '';
    searchParams.userid = '';
    createDate.value = [
      dayjs().subtract(7, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ];
    await nextTick();
    handleSearch();
  }
  function toCustom(record) {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/customer/list?userid=${record.userid}&activity=${codeRuleId.value}`,
    );
  }
  function handleExport() {
    const params = {
      ...searchParams,
      id: route.query.id,
      page: pagination.value.current,
      size: pagination.value.pageSize,
    };

    exportListApi(params)
      .then((res) => {
        const fileName = `${codeName.value}_${formatToDate(new Date())}活动数据.csv`;
        downloadByData(res.data, fileName);
      })
      .catch(() => {});
  }
  function handleDayExport() {
    const params = {
      ...searchParams,
      id: route.query.id,
      page: pagination.value.current,
      size: pagination.value.pageSize,
    };

    exportDayApi(params)
      .then((res) => {
        const fileName = `${codeName.value}_${formatToDate(new Date())}添加客人明细.csv`;
        downloadByData(res.data, fileName);
      })
      .catch(() => {});
  }

  watch(
    [() => createDate.value],
    () => {
      if (createDate.value && createDate.value?.length > 0) {
        searchParams.startTime = createDate.value[0].startOf('date').unix();
        searchParams.endTime = createDate.value[1].endOf('date').unix();
      } else {
        const sevenDaysAgo = dayjs().subtract(7, 'day').startOf('day').unix();
        const today = dayjs().subtract(1, 'day').endOf('day').unix();
        searchParams.startTime = sevenDaysAgo;
        searchParams.endTime = today;
      }
    },
    { immediate: true },
  );

  /**
   * 获取访问统计数据
   * @param {Number} queryType 1 左侧统计数据 2 右侧统计数据（当天）3 列表
   */
  const getStaticsData = async (queryType, params = {}) => {
    try {
      const data = await getStaticsApi({
        ...params,
        queryType,
        id: route.query.id,
      });
      const { wecomContactCodeExternalRes } = data;
      switch (queryType) {
        case 1:
          if (wecomContactCodeExternalRes) {
            staticList[0].value = wecomContactCodeExternalRes.addCountNumber;
            staticList[1].value = wecomContactCodeExternalRes.saNumber;
            staticList[2].value = Number(wecomContactCodeExternalRes.addRate);
            staticList[3].value = wecomContactCodeExternalRes.mergeFriendNumber;
            staticList[4].value = wecomContactCodeExternalRes.countRequestNumber;
          }
          break;
        case 2:
          if (wecomContactCodeExternalRes) {
            staticRightList[0].value = wecomContactCodeExternalRes.addCountNumber;
            staticRightList[1].value = wecomContactCodeExternalRes.saNumber;
            staticRightList[2].value = Number(wecomContactCodeExternalRes.addRate);
            staticRightList[3].value = wecomContactCodeExternalRes.mergeFriendNumber;
            staticRightList[4].value = wecomContactCodeExternalRes.countRequestNumber;
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error('获取访问统计数据错误: ', error);
    }
  };

  /**
   * 获取统计列表数据
   */
  const getList = async () => {
    try {
      loading.value = true;

      const { list, total } = await getStaticsListApi({
        ...searchParams,
        id: route.query.id,
        page: pagination.value.current,
        size: pagination.value.pageSize,
      });
      loading.value = false;
      dataList.value = Array.isArray(list) ? list : [];
      pagination.value.total = total;
    } catch (error) {
      loading.value = false;
      console.log('获取统计列表数据: ', error);
    }
  };

  watch(
    () => dateRange.value,
    () => {
      let params = {};
      if (dateRange.value && dateRange.value?.length > 0) {
        params = {
          startTime: dateRange.value[0].startOf('date').unix(),
          endTime: dateRange.value[1].endOf('date').unix(),
        };
      } else {
        const sevenDaysAgo = dayjs().subtract(7, 'day').startOf('day').unix();
        const yesterday = dayjs().subtract(1, 'day').endOf('day').unix();
        params = { startTime: sevenDaysAgo, endTime: yesterday };
      }
      getStaticsData(1, params);
    },
    { immediate: true },
  );
  onMounted(() => {
    codeRuleId.value = route.query.id as string;
    codeName.value = decodeURIComponent(route.query.name as string);
    getStaticsData(2);
    getList();
  });
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="scss" scoped>
  .m-b-16 {
    margin-bottom: 16px;
  }

  .m-t-16 {
    margin-top: 16px;
  }

  .m-l-16 {
    margin-left: 16px;
  }

  .code-create-container {
    margin: 16px 24px;
    padding: 0;
    border-radius: 0;
    background-color: var(--component-background-color);
  }

  :deep(.tabs) .#{$prefix}-tabs-nav-list {
    padding: 0 15px;
  }

  .grid {
    display: grid;
    grid-gap: 10px;
    grid-template-columns: repeat(3, 1fr);

    .statistic {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
    }
  }
</style>
