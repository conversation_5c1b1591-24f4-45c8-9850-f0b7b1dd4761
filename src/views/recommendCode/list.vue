<template>
  <div class="code-container">
    <a-row class="content">
      <a-form class="search-form" :model="selectForm" layout="vertical">
        <a-form-item :label="`${selectForm.type == 1 ? '活动名称' : '活码id'} `">
          <a-input-group compact class="compact">
            <a-select
              style="width: 140px"
              v-model:value="selectForm.type"
              @change="changeSelectType"
              :options="ruleList"
            />
            <a-input
              :placeholder="selectForm.type == 1 ? '请输入活动名称' : '请输入活码id'"
              v-model:value.trim="selectForm.name"
              allowClear
            />
          </a-input-group>
        </a-form-item>

        <a-form-item label="展码规则">
          <a-select v-model:value="selectForm.activityType" placeholder="请选择展码规则" allowClear>
            <a-select-option v-for="item in ruleType" :key="item.value" :value="item.value">{{
              item.label
            }}</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="创建日期">
          <a-range-picker v-model:value="createDate" format="YYYY/MM/DD" allowClear />
        </a-form-item>

        <a-form-item label="生成状态">
          <a-select
            v-model:value="selectForm.status"
            placeholder="请选择生成状态"
            :options="statusOptions"
            allowClear
          />
        </a-form-item>

        <a-form-item class="is-operation">
          <a-button type="text" @click="handleReset">重置</a-button>
          <a-button @click="handleSearch()">搜索</a-button>
        </a-form-item>
      </a-form>
    </a-row>
    <!-- 创建活码 -->

    <div class="content">
      <a-row class="m-b-24">
        <a-button class="mb2" type="primary" @click="handleCreate"> 创建活动 </a-button>
        <text style="padding-left: 20px; color: #a12117; font-size: 12px; line-height: 32px"
          >说明：此处创建的推优活动规则仅支持API对接使用，不可下载使用</text
        >
      </a-row>
      <!-- 列表 -->
      <a-table
        v-loading="loading"
        :dataSource="codeList"
        :pagination="pagination"
        :columns="columns"
        :scroll="{ x: '100%' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'activityType'">
            {{ ruleTypeObj[record.activityType] }}
          </template>

          <template v-if="column.key === 'staffArrayTotal'">
            <div v-if="[1, 2].includes(record.activityType)">
              {{ record.staffArrayTotal }}
            </div>
            <div v-else>
              {{ record.staffArrayTotal }}
            </div>
          </template>
          <template v-if="column.key === 'status'">
            {{ statusObj[record.status] }}
          </template>

          <template v-if="column.key === 'longTermEffect'">
            <template v-if="record.longTermEffect || record.startTime || record.endTime">
              <template v-if="record.longTermEffect === 1">长期有效</template>
              <template v-else>
                <p style="margin-bottom: 0">{{
                  formatToDateTime(record.startTime * 1000) || '--'
                }}</p>
                ~
                <p>{{ formatToDateTime(record.endTime * 1000) || '--' }}</p>
              </template>
            </template>
          </template>

          <template v-if="column.key === 'modifiedDate'">
            {{ formatToDateTime(record.modifiedDate) || '--' }}
          </template>

          <template v-else-if="column.key === 'action'">
            <a-button type="text" class="active" @click="toDetails(record)"> 详情 </a-button>

            <a-button
              type="text"
              class="active"
              v-if="record.status == 0 || record.status == 1"
              @click="openEdit(record)"
            >
              编辑
            </a-button>

            <a-button
              type="text"
              class="active"
              danger
              v-if="record.status == 0 || record.status == 2 || record.status == 3"
              @click="toDelete(record)"
            >
              删除
            </a-button>

            <a-button
              type="text"
              danger
              class="active"
              v-if="record.status == 1"
              @click="toStop(record)"
            >
              终止
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, toRefs, onMounted, watch, h } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { getActivityListApi, deleteActivityApi, stopActivityApi } from '@/api/sys/recommendCode';

  const { createMessage, createConfirm } = useMessage();
  const { success } = createMessage;
  const state = reactive({
    columns: [
      {
        title: '活动id',
        dataIndex: 'id',
        ellipsis: true,
        width: 300,
      },
      {
        title: '活动名称',
        dataIndex: 'activityName',
        ellipsis: true,
        width: 150,
      },
      {
        title: '展码规则',
        dataIndex: 'activityType',
        key: 'activityType',
        width: 200,
      },
      {
        title: '生成活码数',
        dataIndex: 'qrCodeTotal',
        ellipsis: true,
        width: 120,
        align: 'center',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        align: 'center',
      },
      {
        title: '有效期',
        dataIndex: 'longTermEffect',
        key: 'longTermEffect',
        width: 180,
      },
      {
        title: '更新时间',
        dataIndex: 'modifiedDate',
        key: 'modifiedDate',
        width: 180,
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 200,
      },
    ],
    /** 遮罩层 */
    loading: false,
    /** 下载弹框 */
    syncVisible: false,
    /** 查询条件 */
    selectForm: {
      type: 1,
      name: '',
      activityType: undefined,
      createdAtStart: '',
      createdAtEnd: '',
      status: undefined,
    },
    /** 创建日期范围 */
    createDate: null,
    /** 活动名称或id */
    ruleList: [
      {
        label: '活动名称',
        value: 1,
      },
      {
        label: '活码id',
        value: 2,
      },
    ],
    /** 展码规则 */
    ruleType: [
      {
        label: '剔除已添加成员',
        value: 1,
      },
      {
        label: '优先展示最近添加成员',
        value: 2,
      },
      {
        label: '优先展示最早添加成员',
        value: 3,
      },
      // { label: '优先展示prefer SA', value: 4 },
      {
        label: 'BV定制(附近门店)',
        value: 5,
      },
    ],
    /** 生成状态 */
    statusOptions: [
      {
        label: '未开始',
        value: 0,
      },
      {
        label: '生效中',
        value: 1,
      },
      {
        label: '已结束',
        value: 2,
      },
      {
        label: '已终止',
        value: 3,
      },
    ],
    /** 列表数据 */
    codeList: [],
    /** 活码id */
    codeRuleId: 0,
    /** 下载传递参数 */
    codeInfo: {},
    /** 下载压缩包缓存地址 */
    downPath: {},
    ruleTypeObj: {
      1: '剔除已添加成员',
      2: '优先展示最近添加成员',
      3: '优先展示最早添加成员',
      // 4: '优先展示prefer SA',
      5: 'BV定制(附近门店)',
    },
    statusObj: {
      0: '未开始',
      1: '生效中',
      2: '已结束',
      3: '已终止',
    },
  });
  const {
    selectForm,
    statusOptions,
    ruleList,
    ruleType,
    createDate,
    statusObj,
    ruleTypeObj,
    columns,
    codeList,
    loading,
  } = toRefs(state);
  const pagination = ref({
    total: 0,
    current: 1,
    pageSize: 10,
    onChange: pageChange,
    onShowSizeChange: pageSizeChange,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条数据`,
  });

  watch(
    () => state.createDate,
    () => {
      if (state.createDate && state.createDate[0] && state.createDate[1]) {
        state.selectForm.createdAtStart = (state.createDate[0] as any).startOf('date').unix();
        state.selectForm.createdAtEnd = (state.createDate[1] as any).endOf('date').unix();
      } else {
        state.selectForm.createdAtStart = '';
        state.selectForm.createdAtEnd = '';
      }
      console.log(state.selectForm);
    },
    { deep: true },
  );

  function pageChange(page: number, pageSize: number) {
    pagination.value.current = page;
    pagination.value.pageSize = pageSize;
    getList();
  }

  function pageSizeChange(current: number, size: number) {
    pagination.value.current = 1;
    pagination.value.pageSize = size;
    getList();
  }

  /** 筛选条件 活动名称、活码id切换 */
  function changeSelectType() {
    state.selectForm.name = '';
  }
  function handleSearch() {
    pagination.value.current = 1;
    getList();
  }
  function getList() {
    state.loading = true;
    const params = {
      page: pagination.value.current,
      size: pagination.value.pageSize,
      activityType: state.selectForm.activityType,
      startTime: state.selectForm.createdAtStart,
      endTime: state.selectForm.createdAtEnd,
      status: state.selectForm.status,
    };
    // 活动名称 输入值
    if (state.selectForm.type == 1) {
      params['activityName'] = state.selectForm.name;
    }
    // 活码id 输入值
    if (state.selectForm.type == 2) {
      params['id'] = state.selectForm.name;
    }
    getActivityListApi(params)
      .then((res) => {
        state.codeList = res.list || [];
        state.loading = false;
        pagination.value.total = res.total;
      })
      .catch(() => {
        state.loading = false;
      });
  }
  function handleReset() {
    state.selectForm.name = '';
    state.selectForm.activityType = undefined;
    state.selectForm.status = undefined;
    state.selectForm.createdAtStart = '';
    state.selectForm.createdAtEnd = '';
    state.selectForm.type = 1;
    state.createDate = null;
    pagination.value.current = 1;
    pagination.value.pageSize = 10;
    pagination.value.total = 0;
    handleSearch();
  }
  function handleCreate() {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/recommendCode/create`,
    );
  }
  function toDetails(record) {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/recommendCode/detail?id=${record.id}&name=${encodeURIComponent(record.activityName)}`,
    );
  }
  /** 编辑 */
  function openEdit(record) {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/recommendCode/edit?id=${record.id}`,
    );
  }

  /** 删除 */
  function toDelete(record) {
    const codeId = record.id;
    createConfirm({
      title: '删除提醒',
      iconType: 'warning',
      content: h('div', [
        h('span', '您确认要删除当前活码吗?'),
        h('br'),
        h('span', '删除后，将从系统中移除且无法恢复!'),
      ]),
      okText: '确定',
      cancelText: '取消',
      onOk() {
        deleteActivityApi(codeId).then(() => {
          success('删除成功');
          //页码处理
          if (
            state.codeList.length % pagination.value.pageSize === 1 &&
            pagination.value.current > 1
          ) {
            pagination.value.current--;
          }
          getList();
        });
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
  }
  /**
   * 终止提醒
   *
   * @param record 记录对象
   */
  function toStop(record) {
    const codeId = record.id;
    createConfirm({
      title: '终止提醒',
      iconType: 'warning',
      content: h('div', [
        h('span', '您确认要终止当前活动吗?'),
        h('br'),
        h('span', '终止后，活动将停止且无法重新启用！'),
      ]),
      okText: '确定',
      cancelText: '取消',
      onOk() {
        stopActivityApi(codeId).then(() => {
          success('终止成功');
          getList();
        });
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
  }
  onMounted(() => {
    handleSearch();
  });
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="scss" scoped>
  @import '@/theme/reset.scss';

  .search-form {
    flex-grow: 1;
  }

  .code-container {
    .content {
      margin: 16px 24px;
      padding: 16px 24px;
      border-radius: 0;
      background-color: var(--component-background-color);
    }
  }

  .m-b-24 {
    margin-bottom: 24px;
  }
</style>
