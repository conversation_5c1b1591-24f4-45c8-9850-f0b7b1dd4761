<template>
  <a-spin :spinning="pageLoading" wrapperClassName="channel-list-wrapper">
    <div class="code-container" ref="pageRootRef">
      <div class="search m-b-16">
        <a-form class="search-form" layout="vertical">
          <a-form-item :label="`${searchParams.searchType == 1 ? '渠道分组名称' : '渠道名称'}`">
            <a-input-group compact class="compact">
              <!-- 搜索选项 -->
              <a-select v-model:value="searchParams.searchType" style="width: 35%">
                <a-select-option :value="1">渠道分组名称</a-select-option>
                <a-select-option :value="2">渠道名称</a-select-option>
              </a-select>
              <!-- 搜索值输入框 -->
              <a-input
                v-model:value="searchParams.name"
                :placeholder="`请输入${searchParams.searchType == 1 ? '渠道分组' : '渠道'}名称`"
                allowClear
              />
            </a-input-group>
          </a-form-item>
          <a-form-item class="is-operation">
            <a-button @click="reset" type="text">重置</a-button>
            <a-button @click="search">搜索</a-button>
            <a-button type="primary" @click="exportFn">导出数据</a-button></a-form-item
          >
        </a-form>
      </div>
      <div class="content">
        <a-spin :spinning="channelGroupLoading" wrapperClassName="channel-group-spin-wrapper">
          <div class="left-menu">
            <div class="group-title">
              <span>渠道分组名称</span>
              <div class="add-text" @click="addChannelGroup">
                <PlusCircleOutlined />
                <span class="ml2">新增渠道组</span>
              </div>
            </div>

            <div class="group-list" :class="{ 'flex-center': channelGroupList.length === 0 }">
              <template v-if="channelGroupList.length > 0">
                <div
                  class="group-list-item"
                  :class="{ active: item.id === currentChannelGroupId }"
                  :style="
                    item.id === currentChannelGroupId && colorPrimary
                      ? { backgroundColor: colorPrimary }
                      : {}
                  "
                  v-for="(item, index) in channelGroupList"
                  :key="item.id"
                  @click="handleChannelGroupChange(item)"
                >
                  <span class="group-item-name">{{ item.name }}</span>
                  <DeleteOutlined @click.stop="handleDeleteChannelGroup(item, index)" />
                </div>
              </template>
              <a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
            </div>
          </div>
        </a-spin>
        <div class="table-box">
          <div class="operate">
            <span class="group-title">{{ currentChannelGroupName }}</span>
            <a-button type="primary" :disabled="!currentChannelGroupId" @click="updateChannel(1)"
              >新增渠道</a-button
            >
          </div>
          <a-table :dataSource="list" :columns="columns" :loading="loading">
            <template #headerCell="{ column }">
              <template v-if="column.dataIndex === 'externalCount'">
                <span>
                  渠道人数
                  <a-popover trigger="hover" placement="bottom">
                    <QuestionCircleOutlined />
                    <template #content>
                      <span>记录通过当前渠道关联的所有企微码添加成员的客户数；</span>
                    </template>
                  </a-popover>
                </span>
              </template>
            </template>
            <template #bodyCell="{ column, index, record }">
              <template v-if="column.dataIndex === 'externalCount'">
                <span class="count-text" @click.stop="goCustomer(record.id)">{{
                  record.externalCount
                }}</span>
              </template>
              <template v-else-if="column.dataIndex === 'autoMakeTag'">
                <a-switch
                  v-model:checked="record.autoMakeTag"
                  :checkedValue="1"
                  :unCheckedValue="2"
                  :loading="record.autoMakeTagLoading"
                  @change="(...args) => handleAutoMakeTagChange(index, ...args)"
                />
              </template>
              <template v-else-if="column.dataIndex === 'operation'">
                <a-button type="link" @click="updateChannel(2, record)">编辑</a-button>
                <a-button type="link" danger @click="handleDeleteChannel(record.id)">删除</a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>
      <!-- 新增渠道分组弹框 -->
      <a-modal
        v-model:open="showChannelGroupModal"
        title="新增渠道分组"
        :bodyStyle="{ 'max-height': '60vh', overflow: 'auto', padding: '30px 20px 0' }"
        :maskClosable="false"
        :keyboard="false"
        :getContainer="pageRootRef"
        @ok="channelGroupFinish"
      >
        <a-form
          ref="channelGroupRef"
          :model="channelGroupForm"
          :label-col="{ style: { width: '120px' } }"
        >
          <a-form-item
            label="渠道分组名称"
            name="name"
            :rules="[{ required: true, message: '请输入渠道分组名称' }]"
          >
            <a-input
              v-model:value="channelGroupForm.name"
              :maxlength="15"
              showCount
              placeholder="请输入渠道分组名称"
            />
          </a-form-item>
          <div style="padding: 0 0 10px 120px">
            <span
              class="add-channel"
              style="cursor: pointer"
              @click="
                () => {
                  if (channelGroupForm.channelList.length < 20) {
                    channelGroupForm.channelList.push({ name: '', autoMakeTag: false });
                  } else {
                    message.error('本次最多可新增20个渠道');
                  }
                }
              "
            >
              <PlusCircleOutlined style="margin-right: 2px" />新增渠道
            </span>
            <span>本次最多可新增20个渠道</span>
          </div>
          <template v-for="(item, index) in channelGroupForm.channelList" :key="index">
            <div class="group-form-item">
              <a-form-item
                class="group-form-item-input"
                label="渠道名称"
                :name="['channelList', index, 'name']"
                :rules="[{ required: true, message: '请输入渠道名称' }]"
              >
                <a-input
                  v-model:value="item.name"
                  :maxlength="15"
                  showCount
                  placeholder="请输入渠道名称"
                />
              </a-form-item>
              <DeleteOutlined
                v-if="index > 0"
                class="icon-delete"
                @click="channelGroupForm.channelList.splice(index, 1)"
              />
            </div>
            <a-form-item style="margin-top: -10px; padding-left: 120px" name="autoMakeTag">
              <a-checkbox v-model:checked="item.autoMakeTag">是否自动打企微标签</a-checkbox>
            </a-form-item>
          </template>
        </a-form>
      </a-modal>
      <!-- 新增渠道弹框 -->
      <a-modal
        v-model:open="showChannelModal"
        :title="`${channelId ? '编辑' : '新增'}渠道`"
        :maskClosable="false"
        :keyboard="false"
        :getContainer="pageRootRef"
        @ok="channelFinish"
      >
        <a-form ref="channelRef" :model="channelForm" :label-col="{ style: { width: '120px' } }">
          <a-form-item
            label="渠道分组"
            name="contactChannelGroupId"
            :rules="[{ required: true, message: '请选择渠道分组' }]"
          >
            <a-select v-model:value="channelForm.contactChannelGroupId">
              <a-select-option v-for="item in channelGroupList" :key="item.id" :value="item.id">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            label="渠道名称"
            name="name"
            :rules="[{ required: true, message: '请输入渠道名称' }]"
          >
            <a-input v-model:value="channelForm.name" :maxlength="15" showCount />
          </a-form-item>
          <a-form-item style="margin-top: -10px; padding-left: 120px" name="autoMakeTag">
            <a-checkbox v-model:checked="channelForm.autoMakeTag">是否自动打企微标签</a-checkbox>
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </a-spin>
</template>
<script setup>
  import { ref, reactive, onMounted, nextTick } from 'vue';
  import {
    PlusCircleOutlined,
    DeleteOutlined,
    QuestionCircleOutlined,
  } from '@ant-design/icons-vue';
  import { Modal, message, Empty, theme } from 'ant-design-vue';

  import { downloadByData } from '@/utils/file/download';
  import {
    getChannelGroupsApi,
    createChannelGroupApi,
    deleteChannelGroupApi,
    getChannelListApi,
    createChannelApi,
    deleteChannelApi,
    updateChannelApi,
    exportChannelApi,
  } from '@/api/sys/channel';
  import { formatToDate } from '@/utils/dateUtil';

  /** 页面根节点 */
  const pageRootRef = ref();
  /** 页面主题色配置 */
  const colorPrimary = ref('');
  /** 页面加载状态 */
  const pageLoading = ref(false);
  /** 渠道分组列表 */
  const channelGroupList = ref([]);
  /** 获取渠道分组列表加载状态 */
  const channelGroupLoading = ref(false);
  /** 当前选中渠道分组id */
  const currentChannelGroupId = ref(0);
  /** 当前选中渠道分组名称 */
  const currentChannelGroupName = ref('');
  /** 新增渠道分组弹框展示状态 */
  const showChannelGroupModal = ref(false);
  /** 新增渠道分组弹框实例 */
  const channelGroupRef = ref();
  /** 表格数据 */
  const list = ref([]);
  /** 新增渠道分组表单 */
  const channelGroupForm = reactive({
    name: '',
    channelList: [{ name: '', autoMakeTag: false }],
  });

  /** 新增渠道弹框展示状态 */
  const showChannelModal = ref(false);
  /** 新增渠道弹框实例 */
  const channelRef = ref();
  /** 新增渠道分组表单 */
  const channelForm = ref({
    contactChannelGroupId: undefined,
    name: '',
    autoMakeTag: false,
  });
  /** 正在编辑的渠道id */
  const channelId = ref(null);

  /** 搜索参数 */
  const searchParams = ref({
    /** 搜索类型 1 渠道分组 2 渠道 */
    searchType: 1,
    /** 名称 */
    name: '',
  });
  /** 页面加载状态 */
  const loading = ref(false);
  /**
   * 添加渠道分组
   */
  const addChannelGroup = async () => {
    channelGroupForm.name = '';
    channelGroupForm.channelList = [{ name: '', autoMakeTag: false }];
    showChannelGroupModal.value = true;
    await nextTick();
    channelGroupRef.value.clearValidate();
  };
  /**
   * 删除渠道分组回调
   */
  const handleDeleteChannelGroup = (channelGroup, index) => {
    Modal.confirm({
      title: '删除提醒',
      content: h('div', [
        h('span', '您确定要删除当前渠道组吗？'),
        h('br'),
        h('span', '删除后，将移除当前渠道组及其关联的所有渠道且无法恢复！'),
      ]),
      onOk() {
        deleteChannelGroup(channelGroup, index);
      },
    });
  };
  /**
   * 删除渠道分组
   */
  const deleteChannelGroup = async ({ id }, index) => {
    try {
      await deleteChannelGroupApi(id);
      message.success('操作成功');
      // 删除了唯一一个渠道分组
      if (channelGroupList.value.length === 1) {
        currentChannelGroupId.value = 0;
        currentChannelGroupName.value = '';
        getChannelGroups();
        return;
      }
      // 删除了当前选中的渠道分组
      if (id === currentChannelGroupId.value) {
        channelGroupList.value.splice(index, 1);
        currentChannelGroupId.value = channelGroupList.value[0].id;
        currentChannelGroupName.value = channelGroupList.value[0].name;
        fetchList();
        return;
      }
      channelGroupList.value.splice(index, 1);
    } catch (error) {
      console.error(error);
    }
  };
  /**
   * 删除渠道回调
   */
  const handleDeleteChannel = (id) => {
    Modal.confirm({
      title: '删除提醒',
      content: h('div', [
        h('span', '您确定要删除当前渠道吗？'),
        h('br'),
        h('span', '删除后，将从当前渠道分组下移除当前渠道且无法恢复'),
      ]),
      onOk() {
        deleteChannel(id);
      },
    });
  };
  /**
   * 删除渠道
   */
  const deleteChannel = async (id) => {
    try {
      pageLoading.value = true;
      await deleteChannelApi(id);
      pageLoading.value = false;
      message.success('操作成功');
      fetchList();
    } catch (error) {
      pageLoading.value = false;
      console.error('删除渠道错误: ', error);
    }
  };
  /**
   * 添加渠道分组表单回调
   */
  const channelGroupFinish = () => {
    channelGroupRef.value.validate().then(async () => {
      try {
        pageLoading.value = true;
        await createChannelGroupApi({
          name: channelGroupForm.name,
          channelList: channelGroupForm.channelList.map((item) => {
            return { ...item, source: '自建', autoMakeTag: item.autoMakeTag ? 1 : 2 };
          }),
        });
        pageLoading.value = false;
        showChannelGroupModal.value = false;
        message.success('渠道分组添加成功');
        search();
      } catch (error) {
        pageLoading.value = false;
        console.error('添加渠道分组错误: ', error);
      }
    });
  };

  /**
   * 渠道人数排名方法
   */
  const sorterFn = (a, b) => a.externalCount - b.externalCount;

  /** 表格列配置 */
  const columns = reactive([
    { title: '渠道名称', dataIndex: 'name', align: 'center' },
    { title: '渠道人数', dataIndex: 'externalCount', align: 'center', sorter: sorterFn },
    { title: '渠道ID', dataIndex: 'id', align: 'center' },
    { title: '自动打企微标签', dataIndex: 'autoMakeTag', align: 'center' },
    { title: '来源', dataIndex: 'source', align: 'center' },
    { title: '操作', dataIndex: 'operation', align: 'center' },
  ]);

  /**
   * 获取渠道列表
   */
  const fetchList = async () => {
    try {
      list.value = [];
      loading.value = true;
      const resList = await getChannelListApi({
        name: searchParams.value.searchType === 2 ? searchParams.value.name : '',
        contactChannelGroupId: currentChannelGroupId.value,
      });
      loading.value = false;
      list.value = Array(resList)
        ? resList.map((item) => ({ ...item, autoMakeTagLoading: false }))
        : [];
    } catch (error) {
      list.value = [];
      loading.value = false;
      console.error(error);
    }
  };
  /**
   * 添加/编辑渠道
   * @param {Number} type 1: 添加 2: 编辑
   */
  const updateChannel = async (
    type,
    {
      id = null,
      name = '',
      autoMakeTag,
      brandId = '',
      wecomTagId = '',
      source = '自建',
      contactChannelGroupId = currentChannelGroupId.value,
    } = {},
  ) => {
    channelForm.value = {
      id,
      name,
      autoMakeTag: autoMakeTag == 1,
      brandId,
      wecomTagId,
      source,
      contactChannelGroupId,
    };
    showChannelModal.value = true;
    channelId.value = id;
    await nextTick();
    channelRef.value.clearValidate();
  };
  /**
   * 添加/编辑渠道表单回调
   */
  const channelFinish = () => {
    channelRef.value.validate().then(async () => {
      try {
        pageLoading.value = true;
        const autoMakeTag = channelForm.value.autoMakeTag ? 1 : 2;
        const { name, contactChannelGroupId, source } = channelForm.value;
        await (channelId.value
          ? updateChannelApi(channelId.value, { ...channelForm.value, autoMakeTag })
          : createChannelApi({ name, contactChannelGroupId, source, autoMakeTag }));
        pageLoading.value = false;
        showChannelModal.value = false;
        message.success(`${channelId.value ? '编辑' : '添加'}渠道成功`);
        // 操作成功后重新获取列表
        fetchList();
      } catch (error) {
        pageLoading.value = false;
        console.error('添加/编辑渠道错误: ', error);
      }
    });
  };
  /**
   * 渠道分组改变回调
   */
  const handleChannelGroupChange = ({ id, name }) => {
    if (id === currentChannelGroupId.value) return;
    currentChannelGroupId.value = id;
    currentChannelGroupName.value = name;
    fetchList();
  };

  /**
   * 自动打企微标签	状态开关变更方法
   * @param {Number} index
   * @param {Number} value 1开 2关
   */
  const handleAutoMakeTagChange = async (index, value) => {
    try {
      list.value[index].autoMakeTagLoading = true;
      const { id, name, brandId, wecomTagId, source, contactChannelGroupId } = list.value[index];
      await updateChannelApi(id, {
        id,
        name,
        autoMakeTag: value,
        brandId,
        wecomTagId,
        source,
        contactChannelGroupId,
      });
      list.value[index].autoMakeTagLoading = false;
      message.success('操作成功');
    } catch (error) {
      list.value[index].autoMakeTag = value === 1 ? 2 : 1;
      list.value[index].autoMakeTagLoading = false;
      console.error(error);
    }
  };

  /**
   * 去客户列表
   * @param {String} id 渠道id
   */
  const goCustomer = (id) => {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/customer/list?channelId=${id}`,
    );
  };

  /**
   * 搜索
   */
  const search = () => {
    if (loading.value || channelGroupLoading.value) return;
    currentChannelGroupId.value = 0;
    currentChannelGroupName.value = '';

    getChannelGroups();
  };
  const exportFn = async () => {
    let params = {};
    if (searchParams.value.name) {
      if (searchParams.value.searchType === 1) {
        params.groupName = searchParams.value.name;
      } else {
        params.name = searchParams.value.name;
      }
    }
    try {
      const res = await exportChannelApi(params);
      const fileName = `渠道列表${formatToDate(new Date())}.csv`;
      downloadByData(res.data, fileName);
      console.log(res);
    } catch (error) {
      console.error(error);
    }
  };
  /**
   * 重置
   */
  const reset = () => {
    if (loading.value || channelGroupLoading.value) return;
    currentChannelGroupId.value = 0;
    currentChannelGroupName.value = '';
    searchParams.value.searchType = 1;
    searchParams.value.name = '';
    getChannelGroups();
  };

  /**
   * 获取主题色
   */
  const getColorPrimary = () => {
    try {
      const { useToken } = theme;
      const { token } = useToken();
      colorPrimary.value = token.value.colorPrimary;
    } catch (error) {
      console.error('获取主题色错误: ', error);
    }
  };

  /**
   * 获取渠道分组数据
   */
  const getChannelGroups = async () => {
    try {
      channelGroupList.value = [];
      list.value = [];
      channelGroupLoading.value = true;
      const tempList = await getChannelGroupsApi({
        ...searchParams.value,
      });
      channelGroupLoading.value = false;
      channelGroupList.value = Array.isArray(tempList) ? tempList : [];
      if (channelGroupList.value.length > 0) {
        currentChannelGroupId.value = channelGroupList.value[0].id;
        currentChannelGroupName.value = channelGroupList.value[0].name;
        fetchList();
      }
    } catch (error) {
      channelGroupLoading.value = false;
      console.error('获取渠道分组数据 Error: ', error);
    }
  };

  /**
   * 初始化渠道分组数据
   */
  onMounted(() => {
    getColorPrimary();
    getChannelGroups();
  });
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="scss" scoped>
  @import '@/theme/reset.scss';

  .code-container {
    display: flex;
    flex-wrap: wrap;
    height: 100%;

    .search {
      display: flex;
      flex-shrink: 0;
      flex-wrap: wrap;
      width: calc(100% - 48px);
      margin: 16px 24px;
      padding: 24px;
      border-radius: 0;
      background-color: var(--component-background-color);

      .search-form {
        flex-grow: 1;
      }
    }

    .content {
      display: flex;
      justify-content: space-between;
      width: calc(100% - 48px);
      height: calc(100% - 110px);
      margin: 0 24px;

      .left-menu,
      .table-box {
        background: #fff;
      }

      .left-menu {
        width: 300px;
        height: 100%;
        padding: 0 10px;

        .group-title {
          display: flex;
          justify-content: space-between;
          padding: 15px 5px;
          padding-bottom: 10px;
          border-bottom: 1px solid #f0f0f0;
          font-weight: 500;
          line-height: 26px;

          .add-text {
            display: flex;
            align-items: center;
            cursor: pointer;

            span {
              margin-left: 5px;
              font-weight: 400;
            }
          }
        }

        .group-list {
          height: calc(100% - 56px);
          padding: 5px 0;
          overflow-y: auto;

          &.flex-center {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .group-list-item {
            display: flex;
            justify-content: space-between;
            padding: 5px;
            line-height: 26px;

            &:hover {
              background-color: rgb(0 0 0 / 4%);
              cursor: pointer;

              .wecomicon-delete {
                transform: scale(1) rotate(0deg);
              }
            }

            &.active {
              background-color: $colorBlack1;
              color: #fff;
            }

            .group-item-name {
              flex-shrink: 0;
              width: calc(100% - 35px);
            }

            .wecomicon-delete {
              transform: scale(0) rotate(-360deg);
              transition: transform 150ms;
              color: #ed6f6f;
            }
          }
        }
      }

      .table-box {
        width: calc(100% - 320px);
        max-height: 100%;
        padding: 15px;

        .operate {
          display: flex;
          justify-content: space-between;
          padding-bottom: 16px;

          .group-title {
            font-size: 16px;
            font-weight: 600;
          }
        }
      }
    }
  }

  .add-channel {
    margin-top: 10px;
    margin-right: 5px;
    color: $colorBlack1;
  }

  .group-form-item {
    display: flex;
    justify-content: space-between;

    .group-form-item-input {
      width: calc(100% - 35px);
    }

    .icon-delete {
      height: 32px;

      &:hover {
        color: #ed6f6f;
      }
    }
  }

  .count-text {
    color: $colorBlack1;

    &:hover {
      text-decoration: underline;
      cursor: pointer;
    }
  }

  .channel-list-wrapper {
    height: 100%;

    :deep(.#{$prefix}-spin-container) {
      height: 100%;
    }
  }

  .channel-group-spin-wrapper {
    background: #fff;
  }
</style>
