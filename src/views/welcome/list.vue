<template>
  <div class="code-container">
    <a-card>
      <a-button class="m-b-16" type="primary" @click="handleCreate">新增默认欢迎语</a-button>
      <div class="m-b-16 tips">
        当企业微信官方后台没有设置欢迎语时，默认推送此类欢迎语，此类欢迎语优先发送指定员工范围内的欢迎语，若员工未配置指定欢迎语，默认发送适用全部员工的欢迎语；<br />
        如果一个员工存在多条默认欢迎语时，触发最新配置的欢迎语内容(按照更新时间由近到远)。
      </div>
      <!-- 列表 -->
      <a-table
        v-loading="loading"
        :dataSource="codeList"
        :pagination="false"
        :columns="columns"
        :scroll="{ x: '100%' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'content'">
            <div class="ellipsis" :title="record.normalAnswerTextContent">
              {{ record.normalAnswerTextContent }}</div
            >
            <div
              class="ellipsis"
              :title="record.vipAnswerTextContent"
              v-show="record.vipAnswerTextContent"
              >{{ record.vipAnswerTextContent }}</div
            >
          </template>
          <template v-if="column.key === 'scope'">
            {{ record.scope === 1 ? '全部员工' : '指定员工' }}
          </template>

          <template v-if="column.key === 'createdDate'">
            {{ formatToDateTime(record.createdDate) || '--' }}
          </template>
          <template v-if="column.key === 'modifiedDate'">
            {{ formatToDateTime(record.modifiedDate) || '--' }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-button type="text" @click="openEdit(record)"> 编辑 </a-button>

            <a-button type="text" danger @click="toDelete(record)"> 删除 </a-button>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>
<script setup lang="ts">
  import { ref, onMounted, h } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';

  import { getListApi, deleteApi } from '@/api/sys/welcome';
  import { formatToDateTime } from '@/utils/dateUtil';

  interface Member {
    scope: number;
    id: string;
  }

  const { createMessage, createConfirm } = useMessage();
  const { success } = createMessage;

  const loading = ref(false);
  const codeList = ref([] as Member[]);
  const welcomeId = ref('');

  const columns = [
    {
      title: '欢迎语',
      dataIndex: 'id',
      key: 'content',
      ellipsis: true,
    },
    {
      title: '使用范围',
      dataIndex: 'scope',
      key: 'scope',
    },
    {
      title: '创建时间',
      dataIndex: 'createdDate',
      key: 'createdDate',
    },
    {
      title: '更新时间',
      dataIndex: 'modifiedDate',
      key: 'modifiedDate',
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      align: 'center',
    },
  ];
  /**
   * 渲染内容
   *
   * @param row 行数据
   * @returns 返回一个对象，包含 normalAnswerTextContent 和 vipAnswerTextContent 两个属性，分别对应非会员和会员的欢迎消息内容
   */
  function renderContent(row) {
    let normalAnswerTextContent = '';
    let vipAnswerTextContent = '';

    const normal = JSON.parse(row.uniformWelcomeMessageContent);
    normalAnswerTextContent = (normal[0].text && normal[0].text.content) || '';

    if (row.welcomeMessageContent) {
      const vip = JSON.parse(row.welcomeMessageContent);
      vipAnswerTextContent = (vip[0].text && vip[0].text.content) || '';
    }

    normalAnswerTextContent = `非会员：${normalAnswerTextContent}`;
    vipAnswerTextContent = vipAnswerTextContent ? `会员：${vipAnswerTextContent}` : '';

    return {
      normalAnswerTextContent: !vipAnswerTextContent
        ? normalAnswerTextContent.substring(4)
        : normalAnswerTextContent,
      vipAnswerTextContent,
    };
  }
  /**
   * 获取列表数据
   *
   * @returns 无返回值
   */
  function getList() {
    loading.value = true;
    //  重新获取列表时重置welcomeId
    welcomeId.value = '';
    getListApi({})
      .then((res: Member[]) => {
        codeList.value = (res || []).map((item) => {
          const { normalAnswerTextContent, vipAnswerTextContent } = renderContent(item);
          return {
            ...item,
            normalAnswerTextContent,
            vipAnswerTextContent,
          };
        });
        if (codeList.value.length > 0) {
          const allMember: Member | undefined = codeList.value.find(
            (item: Member) => item.scope === 1,
          );
          if (allMember) {
            welcomeId.value = allMember.id;
          }
        }
      })
      .catch((error) => {
        console.error('Failed to fetch list:', error);
      })
      .finally(() => {
        loading.value = false;
      });
  }
  /**
   * 处理创建操作
   *
   * @returns 无返回值，执行后会跳转到qyCodeManage/welcome/add页面
   */
  function handleCreate() {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/qyWelcome/add?welcomeId=${welcomeId.value || ''}`,
    );
  }
  /**
   * 打开编辑页面
   *
   * @param record 记录对象
   */
  function openEdit(record) {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/qyWelcome/edit?welcomeId=${welcomeId.value || ''}&id=${record.id}`,
    );
  }
  /**
   * 删除欢迎语
   *
   * @param record 要删除的欢迎语记录
   */
  function toDelete(record) {
    const codeId = record.id;
    createConfirm({
      title: '删除提醒',
      iconType: 'warning',
      content: h('div', [
        h('span', '您确认要删除当前欢迎语吗?'),
        h('br'),
        h('span', '删除后，将从系统中移除且无法恢复!'),
      ]),
      class: 'test',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        deleteApi(codeId)
          .then(() => {
            success('删除成功');
            getList();
          })
          .catch((error) => {
            console.error('删除失败:', error);
            // 可以添加更多的错误处理逻辑，如重新获取列表或显示错误信息
          });
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }
  onMounted(() => {
    getList();
  });
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="less" scoped>
  .m-b-16 {
    margin-bottom: 16px;
  }
  .code-container {
    margin: 16px 24px;
    .tips {
      color: rgba(0 0 0 / 85%);
      font-size: 12px;
    }
  }

  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: keep-all;
    white-space: nowrap;
  }
</style>
