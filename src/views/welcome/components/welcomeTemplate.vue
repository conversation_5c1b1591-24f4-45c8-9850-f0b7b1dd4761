<template>
  <a-card class="code-create-container">
    <a-page-header style="padding: 0 0 24px" :title="title" @back="handleCancel" />
    <a-row>
      <a-col :span="20">
        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          :label-col="{ span: 3 }"
          :wrapper-col="{ span: 21 }"
        >
          <!-- 规则类型 -->
          <a-form-item label="使用范围" name="scope">
            <a-radio-group
              v-model:value="formData.scope"
              :disabled="!!welcomeId && welcomeId != route.query.id"
            >
              <a-radio v-for="item in typeGroup" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-radio>
            </a-radio-group>
            <div class="tips"> 适用公司全部人员的欢迎语仅可创建一条</div>
          </a-form-item>
          <!-- 选择员工 -->
          <a-form-item
            :wrapper-col="{ offset: 3, span: 21 }"
            v-if="[2].includes(formData.scope)"
            name="userList"
            key="userList"
          >
            <a-button class="m-r-16" type="primary" @click="selectPerson.visible = true">
              {{ formData.userList.length === 0 ? '选择员工' : '修改员工' }}
            </a-button>
            <a-button v-if="formData.userList.length > 0" @click="clearStaff">
              清空已选员工
            </a-button>
            <div v-if="formData.userList.length" class="m-t-16">
              <a-table :dataSource="[formData.userList]" :pagination="false" :scroll="{ y: 400 }">
                <a-table-column>
                  <template #title> 已选员工: {{ formData.userList.length }} </template>
                  <template #default="{ record }">
                    <div class="item-staff" v-for="item in record" :key="item.id">
                      <UserOutlined class="staff-icon" />
                      <span>{{ item.name }}</span>
                    </div>
                  </template>
                </a-table-column>
              </a-table>
            </div>
          </a-form-item>
          <a-form-item label="触发统一欢迎语" name="uniformType">
            <a-switch
              v-model:checked="formData.uniformType"
              :checked-value="1"
              :un-checked-value="2"
            />
            <div class="tips">
              关闭后，可针对会员或非会员客人配置不同的欢迎语内容；开启后将推送统一的回复内容。
            </div>
            <!-- 回复内容配置 -->
            <template v-if="formData.uniformType == 1">
              <div class="header">统一欢迎语内容配置</div>
              <div class="material-wrap">
                <auto-answer-comp v-model:answer="sameAnswer" />
              </div>
            </template>
            <template v-if="formData.uniformType == 2">
              <!-- 非会员扫码添加导购后触发欢迎语内容配置 -->
              <div class="header"> 非会员扫码添加导购后触发欢迎语内容配置 </div>
              <div class="material-wrap mb20">
                <auto-answer-comp v-model:answer="normalAnswer" />
              </div>
              <!-- 会员扫码添加导购后触发欢迎语内容配置 -->
              <div class="header">会员扫码添加导购后触发欢迎语内容配置</div>
              <div class="material-wrap">
                <auto-answer-comp v-model:answer="vipAnswer" />
              </div>
            </template>
          </a-form-item>
        </a-form>
      </a-col>
    </a-row>
    <a-divider style="margin-top: 100px" />
    <div class="footer-box">
      <a-button :loading="loading" :disabled="isUpload" type="primary" @click="submitForm">
        保存
      </a-button>
      <a-button @click="handleCancel">取消</a-button>
    </div>
  </a-card>

  <!-- 选择员工 -->
  <select-person-comp
    :selectedData="selectPerson.selectedData"
    :visible="selectPerson.visible"
    @submit="submitFn"
    @close="selectPerson.visible = false"
  />
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted, watch } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useRoute } from 'vue-router';
  import { UserOutlined } from '@ant-design/icons-vue';
  import selectPersonComp from '@/components/common-select-person/index.vue';
  import autoAnswerComp from '@/components/common-auto-answer/index.vue';
  import { addApi, getDetailApi, updateApi } from '@/api/sys/welcome';
  import { buildUUID } from '@/utils/uuid';

  const { createMessage } = useMessage();
  const { warning, success } = createMessage;

  const route = useRoute();
  const title = ref('');
  const loading = ref(false);
  const isUpload = ref(false);

  const selectPerson = reactive({
    visible: false,
    selectedData: {
      member: [],
      dept: [],
    },
  });
  const formRef = ref();
  // 全局默认欢迎语
  const welcomeId = ref('');
  const typeGroup = reactive([
    { label: '指定员工', value: 2 },
    { label: '全部员工', value: 1 },
  ]);
  const sameAnswer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });

  const vipAnswer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });

  const normalAnswer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });

  const formData = reactive({
    scope: 2, // 使用范围
    userList: [], // 关联员工
    uniformType: 2, // 统一欢迎语？ 默认2
  });

  const userValidator = (rule, value) => {
    if ([2].includes(formData.scope)) {
      if (Array.isArray(value) && value.length > 0) {
        return Promise.resolve();
      } else {
        return Promise.reject('请选择员工');
      }
    } else {
      return Promise.resolve();
    }
  };
  const rules = reactive({
    scope: [{ required: true, trigger: ['change'] }],
    userList: [{ required: true, validator: userValidator, type: 'array', trigger: ['change'] }],
  });

  const submitFn = (data) => {
    const { member, dept } = data.rawData;
    selectPerson.selectedData = { member, dept };
    formData.userList = data.list;
    selectPerson.visible = false;
  };

  const clearStaff = () => {
    selectPerson.selectedData = { member: [], dept: [] };
    formData.userList = [];
  };

  function submitForm() {
    validateSave();
  }

  /** 单独检验选择员工 */
  watch(
    () => formData.userList,
    () => {
      formRef.value.validateFields('userList');
    },
  );

  const validateAnswer = () => {
    if (formData.uniformType === 1) {
      // 校验sameAnswer
      const contentFlag = sameAnswer.textContent !== '';
      const mateFlag = sameAnswer.materialList.length > 0;
      if (!mateFlag && !contentFlag) {
        warning('请填写消息内容或配置消息素材!');
        return false;
      }
      return true;
    } else {
      // 校验vipAnswer、normalAnswer
      const contentFlag = vipAnswer.textContent !== '' && normalAnswer.textContent !== '';
      const mateFlag = vipAnswer.materialList.length > 0 && normalAnswer.materialList.length > 0;
      if (!mateFlag && !contentFlag) {
        warning('请填写消息内容或配置消息素材!');
        return false;
      }
      return true;
    }
  };
  const validateSave = () => {
    formRef.value
      .validate()
      .then(() => {
        if (validateAnswer()) {
          const params: any = {
            ...formData,
            welcomeMessageContent: '',
            uniformWelcomeMessageContent: '',
          };
          delete params.userList;
          // 指定员工
          if ([2].includes(formData.scope)) {
            // 关联员工指定员工时的员工userid
            params.params = formData.userList.map((item: { id: string }) => item.id).join(',');

            // 选择的部门数据
            params.departmentIds = selectPerson.selectedData.dept.map(
              (item: { id: string }) => item.id,
            );

            const selectPersonData = JSON.parse(JSON.stringify(selectPerson));
            selectPersonData.userList = formData.userList;
            delete selectPersonData.visible;
            delete selectPersonData.submit;

            params.selectContent = JSON.stringify(selectPersonData);
          } else {
            // 全部员工
            params.params = '';
            params.departmentIds = [];
            params.selectContent = '{}';
          }

          // 统一
          if (formData.uniformType === 1) {
            const newMaterialList: any = sameAnswer.materialList.map((item: any) => {
              if (item.msgtype === 'miniprogram' || item.msgtype === 'link') {
                let temp = {
                  msgtype: item.msgtype,
                  [item.msgtype]: {
                    ...item,
                  },
                };
                delete temp[item.msgtype].msgtype;
                delete temp[item.msgtype].fileList;
                return temp;
              } else {
                //图片、视频、文件
                let temp = {
                  msgtype: item.msgtype,
                  [item.msgtype]: {
                    ...item,
                  },
                };
                delete temp[item.msgtype].msgtype;
                return temp;
              }
            });

            newMaterialList.unshift({
              msgtype: 'text',
              text: { content: sameAnswer.textContent },
            });
            params.uniformWelcomeMessageContent = JSON.stringify(newMaterialList);
          } else {
            // 会员
            const vipMaterialList: any = vipAnswer.materialList.map((item: any) => {
              if (item.msgtype === 'miniprogram' || item.msgtype === 'link') {
                let temp = {
                  msgtype: item.msgtype,
                  [item.msgtype]: {
                    ...item,
                  },
                };
                delete temp[item.msgtype].msgtype;
                delete temp[item.msgtype].fileList;
                return temp;
              } else {
                //图片、视频、文件
                let temp = {
                  msgtype: item.msgtype,
                  [item.msgtype]: {
                    ...item,
                  },
                };
                delete temp[item.msgtype].msgtype;
                return temp;
              }
            });
            vipMaterialList.unshift({
              msgtype: 'text',
              text: { content: vipAnswer.textContent },
            });
            params.welcomeMessageContent = JSON.stringify(vipMaterialList);
            // 非会员
            const normalMaterialList: any = normalAnswer.materialList.map((item: any) => {
              if (item.msgtype === 'miniprogram' || item.msgtype === 'link') {
                let temp = {
                  msgtype: item.msgtype,
                  [item.msgtype]: {
                    ...item,
                  },
                };
                delete temp[item.msgtype].msgtype;
                delete temp[item.msgtype].fileList;
                return temp;
              } else {
                //图片、视频、文件
                let temp = {
                  msgtype: item.msgtype,
                  [item.msgtype]: {
                    ...item,
                  },
                };
                delete temp[item.msgtype].msgtype;
                return temp;
              }
            });
            normalMaterialList.unshift({
              msgtype: 'text',
              text: { content: normalAnswer.textContent },
            });
            params.uniformWelcomeMessageContent = JSON.stringify(normalMaterialList);
          }
          handleSave(params);
        }
      })
      .catch((error) => {
        console.log('error', error);
      });
  };

  const handleSave = async (params) => {
    loading.value = true;
    try {
      if (!route.query.id) {
        await addApi(params);
        success('创建成功');
        handleCancel();
      } else {
        await updateApi(route.query.id, params);
        success('编辑成功');
        handleCancel();
      }
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  function handleCancel() {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/qyWelcome/list`,
      true,
    );
  }

  const getDetails = async (id) => {
    try {
      const res = await getDetailApi(id);

      if (res) {
        const {
          uniformWelcomeMessageContent,
          welcomeMessageContent,
          selectContent,
          uniformType,
          scope,
        } = res;
        formData.scope = scope;
        formData.uniformType = uniformType;

        if ([2].includes(scope)) {
          // 关联员工
          const selectPersonData = JSON.parse(selectContent);
          formData.userList = selectPersonData.userList;
          selectPerson.selectedData = selectPersonData.selectedData;
        }

        if (formData.uniformType === 1) {
          // 统一欢迎语
          const same = JSON.parse(uniformWelcomeMessageContent);

          sameAnswer.textContent = (same[0].text && same[0].text.content) || '';
          sameAnswer.materialList = dealMaterialList(same);
        } else {
          // 区分不同的欢迎语
          const normal = JSON.parse(uniformWelcomeMessageContent);

          normalAnswer.textContent = (normal[0].text && normal[0].text.content) || '';
          normalAnswer.materialList = dealMaterialList(normal);

          const vip = JSON.parse(welcomeMessageContent);

          vipAnswer.textContent = (vip[0].text && vip[0].text.content) || '';
          vipAnswer.materialList = dealMaterialList(vip);
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  function dealMaterialList(list) {
    return list.slice(1).map((item) => {
      if (['link', 'miniprogram'].includes(item.msgtype)) {
        let temp = {
          ...item[item.msgtype],
          msgtype: item.msgtype,
        };
        temp.fileList = [
          {
            uid: buildUUID(),
            name: item[item.msgtype].name,
            status: 'done',
            url: item[item.msgtype].picurl || item[item.msgtype].pic_url,
          },
        ];
        return temp;
      } else {
        return {
          ...item[item.msgtype],
          msgtype: item.msgtype,
        };
      }
    });
  }
  onMounted(() => {
    if (route.query.id) {
      // 编辑
      title.value = '编辑默认欢迎语';
      getDetails(route.query.id);
    } else {
      //新增
      title.value = '新增默认欢迎语';
    }
    // 获取全局欢迎语id
    console.log(welcomeId.value);

    welcomeId.value = route.query.welcomeId as string;
  });
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="scss" scoped>
  .code-create-container {
    margin: 16px 24px;
  }

  .m-r-16 {
    margin-right: 16px;
  }

  .m-t-16 {
    margin-top: 16px;
  }

  .tips {
    margin-top: 16px;
    color: rgba(0 0 0 / 85%);
    font-size: 12px;
  }

  .item-staff {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 87px;
    height: 28px;
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 0 10px;
    border: solid 1px #d6dffd;
    background-color: #f0f3fe;

    .staff-icon {
      width: 14px;
      height: 14px;
      margin-right: 5px;
    }
  }

  /** 底部按钮区域 */
  .footer-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .#{$prefix}-btn {
      margin-right: 24px;
      margin-bottom: 10px;
    }
  }

  /** 内容配置 */
  .header {
    margin-top: 16px;
    padding: 10px 15px;
    background-color: #ddd;
    font-weight: 700;
  }

  .material-wrap {
    padding: 20px 20px 20px 0;
    border: 1px solid #ddd;
  }
</style>
