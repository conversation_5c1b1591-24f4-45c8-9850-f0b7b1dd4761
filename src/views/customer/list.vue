<template>
  <div class="code-container">
    <a-card class="m-b-16">
      <a-form class="search-form" layout="vertical">
        <template
          v-for="(
            {
              type,
              currentScarchOption,
              searchOptions,
              placeholder,
              options,
              searchName,
              showSearch,
            },
            index
          ) in searchGroup"
          :key="index"
        >
          <a-form-item
            :label="
              searchName
                ? `${searchName}`
                : `${searchOptions[searchGroup[index].currentScarchOption].name}`
            "
          >
            <!-- 输入框组合 -->
            <a-input-group v-if="type === 'input-group'" compact class="compact">
              <!-- 搜索选项 -->
              <a-select
                v-model:value="searchGroup[index].currentScarchOption"
                style="width: 140px"
                @change="(...args) => handleSearchOptionChange(index, ...args)"
              >
                <a-select-option v-for="(option, key) in searchOptions" :key="key" :value="key">
                  {{ option.name }}
                </a-select-option>
              </a-select>
              <!-- 搜索值输入框 -->
              <a-input
                v-if="searchOptions[currentScarchOption].type === 'input'"
                v-model:value="searchOptions[currentScarchOption].value"
                :placeholder="searchOptions[currentScarchOption].placeholder"
                allowClear
              />
              <!-- 搜索值选择框 -->
              <a-select
                v-if="searchOptions[currentScarchOption].type === 'select'"
                v-model:value="searchOptions[currentScarchOption].value"
                :placeholder="searchOptions[currentScarchOption].placeholder"
                allowClear
                :showSearch="searchOptions[currentScarchOption].showSearch"
              >
                <a-select-option
                  v-for="option in searchOptions[currentScarchOption].options"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.name }}
                </a-select-option>
              </a-select>
            </a-input-group>
            <!-- 选择框 -->
            <div v-if="type === 'select'" class="input-box">
              <a-select
                v-model:value="searchGroup[index].value"
                :placeholder="placeholder"
                allowClear
                :showSearch="showSearch"
              >
                <a-select-option
                  v-for="option in options"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.name }}
                </a-select-option>
              </a-select>
            </div>
          </a-form-item>
        </template>
        <a-form-item class="is-operation"
          ><a-button @click="reset" type="text">重置</a-button>
          <a-button @click="search">搜索</a-button>
        </a-form-item>
      </a-form>
    </a-card>
    <div class="content">
      <div class="operate">
        <a-button type="primary" @click="exportFn">导出数据</a-button>
      </div>
      <a-table
        :dataSource="list"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'wechat'">
            <div class="wechat-info-box">
              <img v-if="record.avatar" :src="record.avatar" alt="wechat_avatar" />
              <img v-else src="@/assets/images/default_avatar.png" alt="wechat_avatar" />
              <span>{{ record.nickName }}</span>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'isMember'">
            <span>{{
              record.isMember === 1 ? '会员' : record.isMember === 0 ? '非会员' : '--'
            }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <a-button type="link" @click="goDetail(record.wecomExternalId)">查看详情</a-button>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script setup>
  import { ref, computed, reactive, watch, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import {
    getCustomerListApi,
    getChannelSelectApi,
    getQycodeListApi,
    getActivityListApi,
    exportCustomerListApi,
  } from '@/api/sys/customer';

  import { formatToDate } from '@/utils/dateUtil';
  import { downloadByData } from '@/utils/file/download';

  const route = useRoute();

  /** 渠道下拉列表 */
  const channelSelect = ref([]);
  /** 企微活码列表 */
  const qyCodeSelect = ref([]);
  /** 推优活码列表 */
  const activityList = ref([]);
  /**
   * 获取渠道下拉列表
   */
  const getChannelSelect = async () => {
    try {
      const resData = await getChannelSelectApi();
      if (Array.isArray(resData)) {
        resData.forEach((item) => {
          channelSelect.value.push({ ...item, value: item.id });
        });
      }
    } catch (error) {
      console.error('获取渠道下拉列表错误: ', error);
    }
  };
  /**
   * 获取企微活码列表
   */
  const getQyCodeList = async () => {
    try {
      const { list } = await getQycodeListApi({ page: 1, size: 999999 });
      if (Array.isArray(list)) {
        list.forEach((item) => {
          qyCodeSelect.value.push({ ...item, name: item.qrCodeName, value: item.id });
        });
      }
    } catch (error) {
      console.error('获取企微活码列表错误: ', error);
    }
  };

  /**
   * 获取推优活码列表
   */
  const getActivityList = async () => {
    try {
      const { list } = await getActivityListApi({ page: 1, size: 999999 });
      if (Array.isArray(list)) {
        list.forEach((item) => {
          activityList.value.push({ ...item, name: item.activityName, value: item.id });
        });
      }
    } catch (error) {
      console.error('获取推优活码列表错误: ', error);
    }
  };

  /**
   * 初始化筛选条件列表
   */
  const initAllSelect = () => {
    getChannelSelect();
    getQyCodeList();
    getActivityList();
  };

  /** 搜索参数分组列表 */
  const searchGroup = ref([
    {
      type: 'input-group',
      /** 当前搜索选项 */
      currentScarchOption: 'nickName',
      /** 搜索选项列表 */
      searchOptions: {
        nickName: { name: '客人昵称', type: 'input', placeholder: '请输入客人昵称', value: '' },
        // memCode: { name: '会员卡号', type: 'input', placeholder: '请输入会员卡号', value: '' },
        unionId: { name: 'Unionid', type: 'input', placeholder: '请输入Unionid', value: '' },
        // mobile: { name: '客人手机号', type: 'input', placeholder: '请输入客人手机号', value: '' },
      },
    },
    // {
    //   type: 'select',
    //   searchName: '会员等级',
    //   key: 'memberGrade',
    //   value: undefined,
    //   placeholder: '请选择会员等级',
    //   showSearch: false,
    //   options: [],
    // },
    {
      type: 'input-group',
      /** 当前搜索选项 */
      currentScarchOption: 'userName',
      /** 搜索选项列表 */
      searchOptions: {
        userName: { name: '导购姓名', type: 'input', placeholder: '请输入导购姓名', value: '' },
        userId: { name: 'userid', type: 'input', placeholder: '请输入userid', value: '' },
      },
    },
    {
      type: 'input-group',
      /** 当前搜索选项 */
      currentScarchOption: 'qwCode',
      /** 搜索选项列表 */
      searchOptions: {
        qwCode: {
          name: '企微活码',
          type: 'select',
          placeholder: '请选择企微活码',
          showSearch: true,
          options: qyCodeSelect.value,
          value: undefined,
        },
        activity: {
          name: '推优活动',
          type: 'select',
          placeholder: '请选择活动',
          showSearch: true,
          options: activityList.value,
          value: undefined,
        },
      },
    },
    {
      type: 'select',
      searchName: '渠道',
      key: 'channelId',
      value: undefined,
      placeholder: '请选择渠道',
      showSearch: true,
      options: channelSelect.value,
    },
    // {
    //   type: 'select',
    //   searchName: '是否为会员',
    //   key: 'isMember',
    //   value: undefined,
    //   placeholder: '请选择是否为会员',
    //   showSearch: false,
    //   options: [
    //     { name: '会员', value: 1 },
    //     { name: '非会员', value: 0 },
    //   ],
    // },
  ]);
  /** 搜索参数（由 searchGroup 转化而来） */
  const searchParams = computed(() => {
    // 参数平铺模式
    return searchGroup.value.reduce((params, item) => {
      if (item.type === 'input-group') {
        Object.keys(item.searchOptions).forEach((key) => {
          params[key] = item.currentScarchOption === key ? item.searchOptions[key].value || '' : '';
        });
      }
      if (item.type === 'select') {
        params[item.key] = item.value || '';
      }
      return params;
    }, {});

    // key, value 模式
    // return searchGroup.value.reduce((params_arr, item) => {
    //   if (item.type === 'input-group') {
    //     params_arr.push({
    //       key: item.currentScarchOption,
    //       value: item.searchOptions[item.currentScarchOption].value || '',
    //     });
    //   }
    //   if (item.type === 'select') {
    //     params_arr.push({
    //       key: item.key,
    //       value: item.value || '',
    //     });
    //   }
    //   return params_arr;
    // }, []);
  });
  /** 页面加载状态 */
  const loading = ref(false);
  /** 页码对象 */
  const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条数据`,
  });

  /**
   * 搜索参数分组更改回调
   * @param {Number} index 位于搜索参数分组列表中的索引值
   */
  const handleSearchOptionChange = (index) => {
    const searchOptions = searchGroup.value[index].searchOptions; // 获取当前搜索选项列表
    // 清空同组搜索参数值
    for (let option_key in searchOptions) {
      searchOptions[option_key].value =
        searchOptions[option_key].type === 'select' ? undefined : '';
    }
  };

  /** 表格列配置 */
  const columns = reactive([
    { title: '微信', dataIndex: 'wechat' },
    { title: 'unionid', dataIndex: 'unionid', align: 'center' },
    // { title: '会员卡号', dataIndex: 'memberCode', align: 'center' },
    // { title: '是否为会员', dataIndex: 'isMember', align: 'center' },
    // { title: '会员等级', dataIndex: 'memberGrade', align: 'center' },
    // { title: '添加导购数', dataIndex: 'relationNum', align: 'center' },
    { title: '操作', dataIndex: 'operation', align: 'center' },
  ]);
  /** 表格数据 */
  const list = ref([]);

  /**
   * 表格页码变化
   */
  const handleTableChange = ({ current, pageSize }) => {
    pagination.current = current;
    pagination.pageSize = pageSize;
  };

  /**
   * 获取列表数据
   */
  const fetchList = async ({ current: page, pageSize: size }) => {
    try {
      loading.value = true;
      const { list: resList, total } = await getCustomerListApi({
        ...searchParams.value,
        page,
        size,
      });
      loading.value = false;
      list.value = Array.isArray(resList) ? resList : [];
      pagination.total = total;
    } catch (error) {
      loading.value = false;
      console.error(error);
    }
  };

  /**
   * 搜索
   */
  const search = () => {
    if (loading.value) return;
    const { current, pageSize } = pagination;
    if (current !== 1) {
      pagination.current = 1;
    }
    fetchList({ current, pageSize });
  };
  const exportFn = async () => {
    try {
      const { current: page, pageSize: size } = pagination;
      const res = await exportCustomerListApi({
        ...searchParams.value,
        page,
        size,
      });
      const fileName = `客人列表${formatToDate(new Date())}.csv`;
      downloadByData(res.data, fileName);
    } catch (error) {
      console.error(error);
    }
  };
  /**
   * 重置
   */
  const reset = () => {
    if (loading.value) return;
    searchGroup.value.forEach((item) => {
      if (item.type === 'input-group') {
        item.currentScarchOption = Object.keys(item.searchOptions)[0];
        for (let option_key in item.searchOptions) {
          const option = item.searchOptions[option_key];
          option.value = option.type === 'select' ? undefined : '';
        }
      } else {
        item.value = item.type === 'select' ? undefined : '';
      }
    });
    list.value = [];
    const { current, pageSize } = pagination;
    if (current === 1 && pageSize === 10) {
      fetchList({ current, pageSize });
    } else {
      pagination.current = 1;
      pagination.pageSize = 10;
    }
  };

  /**
   * 去详情
   * @param {Number} id 客户id
   */
  const goDetail = (id) => {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/customer/detail?id=${id}`,
    );
  };

  /**
   * 页码变更，重新获取数据
   */
  watch([() => pagination.current, () => pagination.pageSize], ([current, pageSize]) => {
    fetchList({ current, pageSize });
  });

  onMounted(() => {
    try {
      if (route.query.channelId) {
        // 从渠道跳转进该页面，携带渠道id参数
        const searchGroupIndex = searchGroup.value.findIndex((item) => item.key === 'channelId');
        if (searchGroupIndex > -1) {
          searchGroup.value[searchGroupIndex].value = route.query.channelId;
        }
      }
      if (route.query.userid) {
        // 从企微码推优管理 - 详情 - 访问数据跳转进该页面，携带用户id参数
        // userId 和 userName 在同一分组，所以这里查找 userName
        const searchUserNameIndex = searchGroup.value.findIndex(
          (item) => item.currentScarchOption === 'userName',
        );
        if (searchUserNameIndex > -1) {
          searchGroup.value[searchUserNameIndex]['currentScarchOption'] = 'userId';
          searchGroup.value[searchUserNameIndex]['searchOptions']['userId'].value =
            route.query.userid;
        }
      }
      if (route.query.activity) {
        // 从企微码推优管理 - 详情 - 访问数据跳转进该页面，携带推优活动参数id
        // activity 和 qwCode 在同一分组，所以这里查找 qwCode
        const searchQwCodeIndex = searchGroup.value.findIndex(
          (item) => item.currentScarchOption === 'qwCode',
        );
        if (searchQwCodeIndex > -1) {
          searchGroup.value[searchQwCodeIndex]['currentScarchOption'] = 'activity';
          searchGroup.value[searchQwCodeIndex]['searchOptions']['activity'].value =
            route.query.activity;
        }
      }
    } catch (error) {
      console.log(error);
    }
    const { current, pageSize } = pagination;
    fetchList({ current, pageSize });
    initAllSelect();
  });
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="scss" scoped>
  @import '@/theme/reset.scss';

  .code-container {
    margin: 16px 24px;

    .m-b-16 {
      margin-bottom: 16px;
    }

    .content {
      padding: 24px;
      background-color: var(--component-background-color);

      .wechat-info-box {
        display: flex;
        align-items: center;

        img {
          width: 50px;
          height: 50px;
          margin-right: 10px;
          border-radius: 50%;
        }
      }

      .operate {
        margin-bottom: 16px;
        text-align: right;
      }
    }
  }
</style>
