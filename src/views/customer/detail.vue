<template>
  <div class="code-create-container">
    <a-page-header style="padding: 0" title="客人详情" @back="backList" />
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="客户详情">
        <template v-if="detail">
          <a-descriptions title="基础信息" :column="2">
            <a-descriptions-item label="">
              <div class="flex-row-center">
                <img v-if="detail.avatar" :src="detail.avatar" alt="avatar" />
                <img v-else src="@/assets/images/default_avatar.png" alt="avatar" />
                <div style="margin-left: 10px">微信昵称：{{ detail.nickName || '--' }}</div>
              </div>
            </a-descriptions-item>
            <a-descriptions-item
              label="微信性别"
              :labelStyle="{ height: '80px', alignItems: 'center' }"
              :contentStyle="{ height: '80px', alignItems: 'center' }"
            >
              {{ detail.gender === 2 ? '女' : detail.gender === 1 ? '男' : '未知' }}
            </a-descriptions-item>
          </a-descriptions>
          <a-divider />
          <div class="gray-title"
            >企微标签数据 <p class="tips">每天凌晨3:00同步一次标签数据</p></div
          >

          <div v-if="detail.tagNames && detail.tagNames.length > 0">
            <a-tag class="m-b-2" v-for="(item, index) in detail.tagNames" :key="index"
              >{{ item }}
            </a-tag>
          </div>
        </template>
        <a-skeleton v-else active />
        <a-divider />
        <a-descriptions title="企业微信" :column="1" />
        <a-tabs v-model:activeKey="activeSubKey">
          <a-tab-pane key="1" tab="添加的导购">
            <a-table
              :dataSource="list"
              :columns="columns"
              :loading="loading"
              :pagination="pagination"
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'addWay'">
                  {{ record.addWay || '--' }}
                </template>
                <template v-if="column.dataIndex === 'channel'">
                  {{ record.channel || '--' }}
                </template>
                <template v-if="column.dataIndex === 'remark'">
                  {{ record.remark || '--' }}
                </template>
                <template v-if="column.dataIndex === 'joinTime'">
                  <span>{{ formatToDateTime(record.joinTime * 1000) || '--' }}</span>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </a-tab-pane>
      <a-tab-pane key="2" tab="客户足迹">
        <div v-if="footprintList.length > 0" class="time-line-box">
          <a-timeline>
            <a-timeline-item v-for="item in footprintList" :key="item.id">
              <div class="timeline-title">
                <span v-if="item.action == 1">添加客户</span>
                <span v-else-if="item.action == 2">导购删除客户</span>
                <span v-else-if="item.action == 3">客户删除导购</span>
              </div>
              <div class="timeline-content">
                <span>员工：{{ item.name }}</span>
                <span v-if="item.userid">（{{ item.userid }}）</span>
              </div>
              <div class="timeline-time">{{ formatToDateTime(item.actionDate) }}</div>
            </a-timeline-item>
          </a-timeline>
        </div>
        <template v-if="footprintList.length === 0 && footprintLoading">
          <a-skeleton
            v-for="item in 6"
            :key="item"
            active
            avatar
            :title="false"
            :paragraph="{ rows: 3, width: [120, 200, 180] }"
          />
        </template>
        <a-empty v-if="footprintList.length === 0 && !footprintLoading" style="padding: 200px 0" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script setup>
  import { useRoute } from 'vue-router';
  import { formatToDateTime } from '@/utils/dateUtil';
  import {
    getCustomerDetailApi,
    getSalesmanListApi,
    getFootprintListApi,
  } from '@/api/sys/customer';

  const route = useRoute();

  /** 激活的 tab key */
  const activeKey = ref('1');
  const activeSubKey = ref('1');

  /** 客户详情 */
  const detail = ref(null);
  const backList = () => {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/customer/list`,
      true,
    );
  };
  /**
   * 获取客户详情
   */
  const getCustomerDetail = async () => {
    try {
      const data = await getCustomerDetailApi(route.query.id);
      detail.value = data || {};
    } catch (error) {
      detail.value = {};
      console.error(error);
    }
  };

  /** 表格列配置 */
  const columns = reactive([
    { title: '所属员工', dataIndex: 'name', align: 'center' },
    { title: '员工所在部门', dataIndex: 'department', align: 'center' },
    { title: '来源', dataIndex: 'addWay', align: 'center' },
    { title: '渠道', dataIndex: 'channel', align: 'center' },
    { title: '添加好友时间', dataIndex: 'joinTime', align: 'center' },
    // { title: '添加群数', dataIndex: 'joinChatGroupNums', align: 'center' },
    { title: '备注', dataIndex: 'remark', align: 'center' },
  ]);
  /** 表格数据 */
  const list = ref([]);

  /** 列表加载状态 */
  const loading = ref(false);
  /** 页码对象 */
  const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
  });

  /**
   * 表格页码变化
   */
  const handleTableChange = ({ current, pageSize }) => {
    pagination.current = current;
    pagination.pageSize = pageSize;
  };

  /**
   * 获取添加的导购列表
   */
  const fetchList = async ({ current: page, pageSize: size }) => {
    try {
      loading.value = true;
      const { list: resList, total } = await getSalesmanListApi({
        id: route.query.id,
        page,
        size,
      });
      loading.value = false;
      list.value = Array.isArray(resList) ? resList : [];
      pagination.total = total;
    } catch (error) {
      loading.value = false;
      console.error(error);
    }
  };

  const footprintList = ref([]);
  const footprintLoading = ref(false);

  /**
   * 获取用户足迹列表
   */
  const fetchFootprintList = async () => {
    try {
      footprintLoading.value = true;
      const { list: resList } = await getFootprintListApi({
        id: route.query.id,
        page: 1,
        size: 999999,
      });
      footprintLoading.value = false;
      footprintList.value = Array.isArray(resList) ? resList : [];
    } catch (error) {
      footprintLoading.value = false;
      console.error(error);
    }
  };

  onMounted(() => {
    getCustomerDetail();
    fetchFootprintList();
  });

  /**
   * 页码变更，重新获取数据
   */
  watch(
    [() => pagination.current, () => pagination.pageSize],
    ([current, pageSize]) => {
      fetchList({ current, pageSize });
    },
    { immediate: true },
  );
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="less" scoped>
  .code-create-container {
    margin: 16px 24px;
    padding: 24px;
    background-color: var(--component-background-color);

    .flex-row-center {
      display: flex;
      align-items: center;

      img {
        width: 80px;
        height: 80px;
        border-radius: 50%;
      }
    }

    .gray-title {
      display: flex;
      align-items: baseline;
      margin-bottom: 15px;
      color: rgb(0 0 0 / 88%);
      font-size: 16px;
      font-weight: 600;
      line-height: 1.5;
    }

    .tips {
      margin: 16px;
      color: rgba(0 0 0 / 85%);
      font-size: 12px;
      font-weight: normal;
    }

    .form-title {
      font-size: 16px;
      margin-bottom: 20px;
      font-weight: 700;
    }

    .time-line-box {
      padding: 30px 20px 0;

      .timeline-title {
        font-size: 16px;
        font-weight: 500;
      }

      .timeline-content {
        font-size: 14px;
        font-weight: 400;
      }

      .timeline-time {
        color: rgb(0 0 0 / 45%);
        font-size: 12px;
        font-weight: 400;
      }
    }
  }
</style>
