<template>
  <a-card class="code-create-container">
    <a-page-header
      style="padding: 0 0 24px"
      :title="isEditCode ? '编辑活码' : '创建活码'"
      @back="backList"
    />
    <a-row>
      <a-col :span="20">
        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          :label-col="{ span: 3 }"
          :wrapper-col="{ span: 21 }"
        >
          <div class="form-title">基础信息</div>
          <!-- 活码规则名称 -->
          <a-form-item label="活码规则名称" name="qrCodeName">
            <a-input
              v-model:value="formData.qrCodeName"
              placeholder="请输入内容"
              :maxlength="50"
              showCount
              allowClear
            />
          </a-form-item>
          <!-- 是否自动通过 -->
          <a-form-item label="自动通过" name="autoPass">
            <a-popover placement="rightBottom">
              <template #content> 默认为开启，开启后客户添加时无需经过确认自动成为好友 </template>
              <ExclamationCircleOutlined class="mr-2" />
            </a-popover>
            <a-switch
              v-model:checked="formData.autoPass"
              :checked-value="1"
              :un-checked-value="2"
            />
            <div class="a-switch__tip"> </div>
          </a-form-item>
          <!-- 规则类型 -->
          <a-form-item label="规则类型" name="qrCodeType">
            <a-popover placement="rightBottom">
              <template #content>
                <div>
                  成员专属码：为选择的每一个成员独立生成专属的企业微信联系我活码（当员工被离职，再次进入企微不会被自动更新到规则里，需重新手动添加）；
                </div>
                <div>
                  聚合活码：为多个成员创建一个共同的企业微信活码，每个活码可以绑定多个成员（当员工被离职，再次进入企微不会被自动更新到规则里，需重新手动添加）；
                </div>
                <div>
                  门店活码：根据门店信息创建企业微信活码，每个活码绑定去除店经理和副店经理之后的成员；
                </div>
              </template>
              <ExclamationCircleOutlined class="mr-2" />
            </a-popover>
            <a-radio-group v-model:value="formData.qrCodeType" :disabled="isEditCode">
              <a-radio v-for="item in ruleGroup" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <!-- 选择员工 -->
          <a-form-item
            v-if="[1, 2].includes(formData.qrCodeType)"
            label="员工"
            name="userList"
            key="userList"
          >
            <a-button class="mr2" type="primary" @click="selectPerson.visible = true">
              {{ formData.userList.length === 0 ? '选择员工' : '修改员工' }}
            </a-button>
            <a-button v-if="formData.userList.length > 0" @click="clearStaff">
              清空已选员工
            </a-button>
            <div v-if="formData.userList.length" class="mt5">
              <a-table :dataSource="[formData.userList]" :pagination="false" :scroll="{ y: 400 }">
                <a-table-column>
                  <template #title> 已选员工: {{ formData.userList.length }} </template>
                  <template #default="{ record }">
                    <div class="item-staff" v-for="item in record" :key="item.id">
                      <UserOutlined class="staff-icon" />
                      <span>{{ item.name }}</span>
                    </div>
                  </template>
                </a-table-column>
              </a-table>
            </div>
          </a-form-item>
          <!-- 选择门店 -->
          <a-form-item
            v-if="formData.qrCodeType === 3"
            label="门店"
            name="storeList"
            key="storeList"
          >
            <a-button class="m-r-16" type="primary" @click="selectStore.visible = true">
              {{ formData.storeList.length === 0 ? '选择门店' : '修改门店' }}
            </a-button>
            <a-button v-if="formData.storeList.length > 0" @click="formData.storeList = []">
              清空已选门店
            </a-button>
            <div v-if="formData.storeList.length" class="mt5">
              <a-table :dataSource="[formData.storeList]" :pagination="false" :scroll="{ y: 400 }">
                <a-table-column>
                  <template #title> 已选门店: {{ formData.storeList.length }} </template>
                  <template #default="{ record }">
                    <div class="item-staff" v-for="item in record" :key="item.wecomStoreId">
                      <TeamOutlined class="staff-icon" />
                      <span>{{ item.name }}</span>
                    </div>
                  </template>
                </a-table-column>
              </a-table>
            </div>
          </a-form-item>
          <!-- 关联渠道 -->
          <a-form-item label="关联渠道">
            <a-popover placement="rightBottom">
              <template #content>
                添加渠道后，通过当前活码添加的外部联系人都将会关联已选择的渠道。
              </template>
              <ExclamationCircleOutlined class="mr-2" />
            </a-popover>
            <a-button class="mr2" type="primary" @click="selectChannel.visible = true">
              {{ selectChannel.list.length === 0 ? '选择渠道' : '修改渠道' }}
            </a-button>

            <a-button v-if="selectChannel.list.length > 0" @click="selectChannel.list = []">
              清空已选渠道
            </a-button>
            <div v-if="selectChannel.list.length" class="mt5">
              <a-table :dataSource="[selectChannel.list]" :pagination="false" :scroll="{ y: 400 }">
                <a-table-column>
                  <template #title> 已选渠道: {{ selectChannel.list.length }} </template>
                  <template #default="{ record }">
                    <div class="item-staff" v-for="item in record" :key="item.id">
                      <span>{{ item.name }}</span>
                    </div>
                  </template>
                </a-table-column>
              </a-table>
            </div>
          </a-form-item>

          <div class="form-title">欢迎语配置</div>
          <!-- 是否配置欢迎语 -->
          <a-form-item label="是否配置欢迎语" name="isWelcome">
            <a-switch
              v-model:checked="formData.autoAnswer"
              :checked-value="1"
              :un-checked-value="0"
            />
            <div class="tips">
              开启后，客人通过扫描企业微信二维码添加导购后，系统会向客户推送配置的欢迎语内容。
            </div>
          </a-form-item>
          <template v-if="formData.autoAnswer == 1">
            <a-form-item label="触发统一欢迎语" name="uniformType">
              <a-switch
                v-model:checked="formData.uniformType"
                :checked-value="1"
                :un-checked-value="2"
              />
              <div class="tips">
                关闭后，可针对会员或非会员客人配置不同的欢迎语内容；开启后将推送统一的回复内容。
              </div>
              <!-- 回复内容配置 -->
              <template v-if="formData.uniformType == 1">
                <div class="header">统一欢迎语内容配置</div>
                <div class="material-wrap">
                  <auto-answer-comp v-model:answer="sameAnswer" />
                </div>
              </template>
              <template v-if="formData.uniformType == 2">
                <!-- 非会员扫码添加导购后触发欢迎语内容配置 -->
                <div class="header"> 非会员扫码添加导购后触发欢迎语内容配置 </div>
                <div class="material-wrap mb20">
                  <auto-answer-comp v-model:answer="normalAnswer" />
                </div>
                <!-- 会员扫码添加导购后触发欢迎语内容配置 -->
                <div class="header">会员扫码添加导购后触发欢迎语内容配置</div>
                <div class="material-wrap">
                  <auto-answer-comp v-model:answer="vipAnswer" />
                </div>
              </template>
            </a-form-item>
          </template>
        </a-form>
      </a-col>
    </a-row>
    <a-divider style="margin-top: 100px" />
    <div class="footer-box">
      <a-button :loading="loading" :disabled="isUpload" type="primary" @click="submitForm">
        保存
      </a-button>
      <a-button @click="handleCancel">取消</a-button>
    </div>
  </a-card>

  <!-- 选择渠道 -->
  <select-channel-comp
    :chose="selectChannel.list"
    :visible="selectChannel.visible"
    @submit="submitChannelFn"
    @close="selectChannel.visible = false"
  />
  <!-- 选择员工 -->
  <select-person-comp
    :selectedData="selectPerson.selectedData"
    :visible="selectPerson.visible"
    @submit="submitFn"
    @close="selectPerson.visible = false"
  />
  <!-- 选择门店 -->
  <select-store-comp
    :selectedData="formData.storeList"
    :visible="selectStore.visible"
    @submit="submitStoreFn"
    @close="selectStore.visible = false"
  />
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, watch } from 'vue';
  import { useRoute } from 'vue-router';
  import { ExclamationCircleOutlined, TeamOutlined, UserOutlined } from '@ant-design/icons-vue';
  import selectPersonComp from '@/components/common-select-person/index.vue';
  import selectStoreComp from '@/components/common-select-store/index.vue';
  import selectChannelComp from '@/components/common-select-channel/index.vue';
  import autoAnswerComp from '@/components/common-auto-answer/index.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { addCodeApi, getCodeDetailApi, updateCodeApi } from '@/api/sys/qyCode';
  import { cloneDeep } from 'lodash-es';
  import { buildUUID } from '@/utils/uuid';

  const { createMessage } = useMessage();
  const { warning, success } = createMessage;

  const route = useRoute();

  const loading = ref(false);
  const isUpload = ref(false);
  const isEditCode = ref(false);

  const selectPerson = reactive({
    visible: false,
    selectedData: {
      member: [],
      dept: [],
    },
  });

  const selectStore = reactive({
    visible: false,
  });

  const selectChannel = reactive({
    visible: false,
    list: [],
  });

  const formRef = ref();

  const formData = reactive({
    qrCodeName: '',
    autoPass: 1, // 自动通过
    qrCodeType: 1, // 企微码类型
    userList: [], //关联员工
    storeList: [], //关联门店
    autoAnswer: 1,
    uniformType: 2, // 默认2
  });

  const ruleGroup = reactive([
    { label: '成员专属码', value: 1 },
    { label: '聚合活码', value: 2 },
    { label: '门店活码', value: 3 },
  ]);
  const backList = () => {
    window[import.meta.env.VITE_PREFIX].jumpTo(`/${import.meta.env.VITE_PREFIX}/qyCode/list`, true);
  };
  const storeValidator = (rule, value) => {
    console.log('storeValidator', value);
    if (formData.qrCodeType === 3) {
      if (Array.isArray(value) && value.length > 0) {
        return Promise.resolve();
      } else {
        return Promise.reject('请选择门店');
      }
    } else {
      return Promise.resolve();
    }
  };

  const userValidator = (rule, value) => {
    if ([1, 2].includes(formData.qrCodeType)) {
      if (Array.isArray(value) && value.length > 0) {
        return Promise.resolve();
      } else {
        return Promise.reject('请选择员工');
      }
    } else {
      return Promise.resolve();
    }
  };

  const rules = reactive({
    qrCodeName: [{ required: true, message: '活码规则名称不能为空', trigger: 'blur' }],
    userList: [{ required: true, validator: userValidator, type: 'array', trigger: ['change'] }],
    storeList: [{ required: true, validator: storeValidator, type: 'array', trigger: ['change'] }],
  });

  const sameAnswer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });

  const vipAnswer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });

  const normalAnswer = reactive({
    textContent: '',
    materialList: [],
    isUpload: false,
    isChecking: false,
  });

  const getDetails = async (id) => {
    try {
      const res = await getCodeDetailApi(id);
      if (!res) return;
      const {
        qrCodeName,
        autoPass,
        qrCodeType,
        uniformWecomeMessageContent,
        wecomeMessageContent,
        selectContent,
        qrCodeChannel,
        uniformType,
      } = res;
      formData.qrCodeName = qrCodeName;
      formData.autoPass = autoPass;
      formData.qrCodeType = qrCodeType;
      formData.uniformType = uniformType;
      formData.autoAnswer = uniformWecomeMessageContent.length > 0 ? 1 : 0;
      if (qrCodeChannel && qrCodeChannel.length) {
        selectChannel.list = qrCodeChannel.map((item) => {
          return {
            id: item.channelId,
            name: item.channelName,
            channelId: item.channelId,
          };
        });
      }
      if ([1, 2].includes(qrCodeType)) {
        // 关联员工
        const selectPersonData = JSON.parse(selectContent);
        formData.userList = selectPersonData.userList;
        selectPerson.selectedData = selectPersonData.selectedData;
      } else {
        // 关联门店
        const storeListData = JSON.parse(selectContent).storeList.map((store) => {
          return {
            wecomStoreId: store.wecomStoreId,
            name: store.name,
          };
        });
        formData.storeList = storeListData;
      }
      // 开启欢迎语
      if (formData.autoAnswer === 1) {
        setAnswerAndMaterialList(
          uniformType == 1 ? sameAnswer : normalAnswer,
          uniformWecomeMessageContent,
        );
        if (uniformType !== 1) {
          setAnswerAndMaterialList(vipAnswer, wecomeMessageContent);
        }
      }
    } catch (error) {
      console.error(error);
    }
  };
  // 抽象解析和设置文本内容及材料列表的逻辑
  const setAnswerAndMaterialList = (answer, messageContent) => {
    const messageData = JSON.parse(messageContent);
    answer.textContent = messageData[0]?.text?.content || '';
    answer.materialList = dealMaterialList(messageData);
  };

  watch(
    () => formData.storeList,
    () => {
      formRef.value.validate('storeList');
    },
  );
  watch(
    () => formData.userList,
    () => {
      formRef.value.validate('userList');
    },
  );

  function dealMaterialList(list) {
    return list.slice(1).map((item) => {
      if (['link', 'miniprogram'].includes(item.msgtype)) {
        let temp = {
          ...item[item.msgtype],
          msgtype: item.msgtype,
        };
        temp.fileList = [
          {
            uid: buildUUID(),
            name: item[item.msgtype].name,
            status: 'done',
            url: item[item.msgtype].picurl || item[item.msgtype].pic_url,
          },
        ];
        return temp;
      } else {
        return {
          ...item[item.msgtype],
          msgtype: item.msgtype,
        };
      }
    });
  }
  const submitFn = (data) => {
    const { member, dept } = data.rawData;
    selectPerson.selectedData = { member, dept };
    formData.userList = data.list;
    selectPerson.visible = false;
  };

  const clearStaff = () => {
    selectPerson.selectedData = { member: [], dept: [] };
    formData.userList = [];
  };

  const submitStoreFn = (data) => {
    formData.storeList = cloneDeep(data);
    selectStore.visible = false;
  };

  const submitChannelFn = (data) => {
    console.log('submitChannelFn', data);
    selectChannel.list = data.map((item) => {
      return {
        channelId: item.id,
        id: item.id,
        name: item.name,
      };
    });
    selectChannel.visible = false;
  };

  const validateAnswer = () => {
    // 未开启欢迎语配置
    if (formData.autoAnswer !== 1) return true;

    // 开启欢迎语
    if (formData.uniformType === 1) {
      // 校验sameAnswer
      const contentFlag = sameAnswer.textContent !== '';
      const mateFlag = sameAnswer.materialList.length > 0;
      if (!mateFlag && !contentFlag) {
        warning('请填写消息内容或配置消息素材!');
        return false;
      }
      return true;
    } else {
      // 校验vipAnswer、normalAnswer
      const contentFlag = vipAnswer.textContent !== '' && normalAnswer.textContent !== '';
      const mateFlag = vipAnswer.materialList.length > 0 && normalAnswer.materialList.length > 0;
      if (!mateFlag && !contentFlag) {
        warning('请填写消息内容或配置消息素材!');
        return false;
      }
      return true;
    }
  };

  const validateSave = () => {
    formRef.value
      .validate()
      .then(() => {
        if (validateAnswer()) {
          const params: any = {
            ...formData,
            wecomeMessageContent: '',
            uniformWecomeMessageContent: '',
            wecomeMessageType: formData.autoAnswer,
          };
          delete params.userList;
          delete params.storeList;
          delete params.autoAnswer;

          if ([1, 2].includes(formData.qrCodeType)) {
            // 关联员工
            params.qrCodeParamList = formData.userList.map((item: { id: string }) => {
              return {
                param: item.id,
              };
            });
            // 选择的部门数据
            params.departmentIds = selectPerson.selectedData.dept.map(
              (item: { id: string }) => item.id,
            );

            const selectPersonData = JSON.parse(JSON.stringify(selectPerson));
            selectPersonData.userList = formData.userList;
            delete selectPersonData.visible;
            delete selectPersonData.submit;

            params.selectContent = JSON.stringify(selectPersonData);
          } else {
            //关联门店
            const selectStoreData = {
              storeList: formData.storeList.map((store: any) => ({
                wecomStoreId: store.wecomStoreId,
                name: store.name,
              })),
            };
            params.qrCodeParamList = formData.storeList.map((store: any) => {
              return {
                param: store.wecomStoreId,
              };
            });

            params.selectContent = JSON.stringify(selectStoreData);
          }

          // 关联渠道
          if (selectChannel.list.length > 0) {
            params.qrCodeChannel = selectChannel.list;
          }

          // 开启欢迎语
          if (formData.autoAnswer === 1) {
            // 统一
            if (formData.uniformType === 1) {
              const newMaterialList: any = sameAnswer.materialList.map((item: any) => {
                if (item.msgtype === 'miniprogram' || item.msgtype === 'link') {
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  delete temp[item.msgtype].fileList;
                  return temp;
                } else {
                  //图片、视频、文件
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  return temp;
                }
              });

              newMaterialList.unshift({
                msgtype: 'text',
                text: { content: sameAnswer.textContent },
              });
              params.uniformWecomeMessageContent = JSON.stringify(newMaterialList);
            } else {
              // 会员
              const vipMaterialList: any = vipAnswer.materialList.map((item: any) => {
                if (item.msgtype === 'miniprogram' || item.msgtype === 'link') {
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  delete temp[item.msgtype].fileList;
                  return temp;
                } else {
                  //图片、视频、文件
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  return temp;
                }
              });
              vipMaterialList.unshift({
                msgtype: 'text',
                text: { content: vipAnswer.textContent },
              });
              params.wecomeMessageContent = JSON.stringify(vipMaterialList);
              // 非会员
              const normalMaterialList: any = normalAnswer.materialList.map((item: any) => {
                if (item.msgtype === 'miniprogram' || item.msgtype === 'link') {
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  delete temp[item.msgtype].fileList;
                  return temp;
                } else {
                  //图片、视频、文件
                  let temp = {
                    msgtype: item.msgtype,
                    [item.msgtype]: {
                      ...item,
                    },
                  };
                  delete temp[item.msgtype].msgtype;
                  return temp;
                }
              });
              normalMaterialList.unshift({
                msgtype: 'text',
                text: { content: normalAnswer.textContent },
              });
              params.uniformWecomeMessageContent = JSON.stringify(normalMaterialList);
            }
          }
          handleSave(params);
        }
      })
      .catch((error) => {
        console.log('error', error);
      });
  };

  const handleSave = async (params) => {
    loading.value = true;
    try {
      if (!route.query.id || route.query.type === 'copy') {
        await addCodeApi(params);
        success('创建成功');
        handleCancel();
      } else {
        params.id = route.query.id;
        await updateCodeApi(params);
        success('编辑成功');
        handleCancel();
      }
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const submitForm = () => {
    validateSave();
  };

  const handleCancel = () => {
    formData.qrCodeName = '';
    window[import.meta.env.VITE_PREFIX].jumpTo(`/${import.meta.env.VITE_PREFIX}/qyCode/list`, true);
  };
  onMounted(() => {
    if (route.query.id) {
      getDetails(route.query.id);
      // 仅编辑-不能切换展码规则
      isEditCode.value = true;
    }
  });
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="scss" scoped>
  .m-l-16 {
    margin-left: 16px;
  }

  .m-r-16 {
    margin-right: 16px;
  }

  .mt5 {
    margin-top: 25px;
  }

  .mr2 {
    margin-right: 10px;
  }

  .code-create-container {
    margin: 16px 24px;

    /** 基础信息、欢迎语配置-标题 */
    .form-title {
      margin-bottom: 20px;
      font-size: 14px;
      font-weight: 700;
    }

    .tips {
      margin-top: 16px;
      color: rgba(0 0 0 / 85%);
      font-size: 12px;
    }

    .item-staff {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      min-width: 87px;
      height: 28px;
      margin-right: 10px;
      margin-bottom: 10px;
      padding: 0 10px;
      border: solid 1px #d6dffd;
      background-color: #f0f3fe;

      .staff-icon {
        width: 14px;
        height: 14px;
        margin-right: 5px;
      }
    }

    /** 底部按钮区域 */
    .footer-box {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .#{$prefix}-btn {
        margin-right: 24px;
        margin-bottom: 10px;
      }
    }

    /** 内容配置 */
    .header {
      margin-top: 16px;
      padding: 10px 15px;
      background-color: #ddd;
      font-weight: 700;
    }

    .material-wrap {
      padding: 20px 20px 20px 0;
      border: 1px solid #ddd;
    }
  }
</style>
