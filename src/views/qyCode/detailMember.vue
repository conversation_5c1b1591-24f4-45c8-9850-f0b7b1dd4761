<template>
  <div class="container-detail">
    <a-card>
      <a-page-header style="padding: 0 0 24px" title="企微活码详情" @back="backList" />
      <a-form v-if="route.query.type != '2'" layout="inline">
        <a-form-item>
          <a-input-group compact class="compact">
            <a-select
              style="width: 40%"
              v-model:value="searchParams.searchType"
              :options="searchParams.labelList"
            />
            <a-input
              v-model:value="searchParams.searchValue"
              :placeholder="
                searchParams.searchType == 1
                  ? '请输入员工姓名'
                  : searchParams.searchType == 2
                    ? '请输入Userid'
                    : '请输入活码id'
              "
              style="width: 60%"
            />
          </a-input-group>
        </a-form-item>

        <a-form-item label="活码生成状态">
          <a-select
            style="width: 180px"
            v-model:value="searchParams.status"
            :options="searchParams.statusList"
            placeholder="请选择活码生成状态"
          />
        </a-form-item>

        <a-form-item>
          <a-button @click="handleReset">重置</a-button>
          <a-button class="m-l-16" type="primary" @click="handleSearch">搜索</a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <div class="operate">
      <a-button v-if="route.query.type != '2'" type="primary" @click="exportEx">导出数据</a-button>
      <a-button
        :class="route.query.type != '2' ? 'm-l-16' : ''"
        type="primary"
        @click="exportCustom"
        >导出添加客人数据
      </a-button>
    </div>
    <a-table
      class="m-t-16"
      :loading="loading"
      :columns="columns"
      :pagination="pagination"
      :data-source="dataList"
      :scroll="{ x: '100%' }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'userName'">
          {{ record.userName || '--' }}
        </template>
        <template v-if="column.dataIndex === 'userId'">
          {{ record.userId || '--' }}
        </template>
        <template v-if="column.dataIndex === 'storeCode'">
          {{ record.storeCode || '--' }}
        </template>
        <template v-if="column.dataIndex === 'storeName'">
          {{ record.storeName || '--' }}
        </template>
        <template v-if="column.dataIndex === 'reason'">
          {{ record.reason || '--' }}
        </template>
        <template v-if="column.dataIndex === 'status'">
          {{ statusObj[record.status] }}
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <a-button type="text" v-if="record.status !== 1" @click="rebuild(record)"
            >重新生成</a-button
          >
          <a-button type="text" v-if="record.status == 1" @click="download(record)">下载</a-button>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { useMessage } from '@/hooks/web/useMessage';
  import { downloadByData, downloadByUrl } from '@/utils/file/download';
  import { generateCodeApi, getDetailListApi, exportApi, exportCustomApi } from '@/api/sys/qyCode';
  import { formatToDate } from '@/utils/dateUtil';

  const route = useRoute();
  const { createMessage } = useMessage();
  const { success, error } = createMessage;
  const codeRuleId = ref('');
  const codeName = ref('');
  const searchParams = reactive({
    labelList: [
      {
        label: '员工姓名',
        value: 1,
      },
      {
        label: 'userid',
        value: 2,
      },
      {
        label: '活码ID',
        value: 3,
      },
    ],
    statusList: [
      {
        label: '已生成',
        value: 1,
      },
      {
        label: '生成失败',
        value: 2,
      },
    ],
    searchType: 1,
    searchValue: '',
    status: undefined,
  });
  const statusObj = {
    1: '已生成',
    2: '生成失败',
  };
  const dataList = ref([]);
  const columns = ref([
    {
      title: '活码id',
      dataIndex: 'id',
      key: 'id',
      ellipsis: true,
      width: 280,
    },
    {
      title: '员工姓名',
      dataIndex: 'userName',
      key: 'userName',
      ellipsis: true,
      width: 180,
    },
    {
      title: 'Userid',
      dataIndex: 'userId',
      key: 'userId',
      ellipsis: true,
      width: 180,
    },
    {
      title: 'StoreCode',
      dataIndex: 'storeCode',
      key: 'storeCode',
      ellipsis: true,
      width: 200,
    },
    {
      title: '柜台名称',
      dataIndex: 'storeName',
      key: 'storeName',
      ellipsis: true,
      width: 180,
    },
    {
      title: '累计添加人数',
      dataIndex: 'addCount',
      key: 'addCount',
      ellipsis: true,
      width: 180,
    },
    {
      title: '活码生成状态',
      dataIndex: 'status',
      key: 'status',
      width: 180,
    },
    {
      title: '失败原因',
      dataIndex: 'reason',
      key: 'reason',
      width: 450,
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 150,
      align: 'center',
    },
  ]);
  const combineColumns = [
    {
      title: '活码id',
      dataIndex: 'wecomConfigId',
      key: 'wecomConfigId',
      ellipsis: true,
      width: 280,
    },
    {
      title: '累计添加人数',
      dataIndex: 'addCount',
      key: 'addCount',
      ellipsis: true,
      width: 180,
    },
    {
      title: '活码生成状态',
      dataIndex: 'status',
      key: 'status',
      width: 180,
    },
    {
      title: '失败原因',
      dataIndex: 'reason',
      key: 'reason',
      width: 450,
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 150,
      align: 'center',
    },
  ];
  const pagination = ref({
    total: 0,
    current: 1,
    pageSize: 10,
    onChange: pageChange,
    onShowSizeChange: pageSizeChange,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条数据`,
  });
  const backList = () => {
    window[import.meta.env.VITE_PREFIX].jumpTo(`/${import.meta.env.VITE_PREFIX}/qyCode/list`, true);
  };

  const loading = ref(false);
  function pageChange(page, size) {
    pagination.value.current = page;
    pagination.value.pageSize = size;
    getList();
  }
  function pageSizeChange(current, size) {
    pagination.value.current = 1;
    pagination.value.pageSize = size;
    handleSearch();
  }
  function handleSearch() {
    pagination.value.current = 1;
    getList();
  }
  function handleReset() {
    searchParams.searchType = 1;
    searchParams.searchValue = '';
    searchParams.status = undefined;
    handleSearch();
  }
  /** 导出数据 */
  function exportEx() {
    const params = {
      status: searchParams.status,
      qrCodeRuleId: codeRuleId.value,
      codeType: 'member',
    };
    if (searchParams.searchValue) {
      if (searchParams.searchType === 1) {
        params['baName'] = searchParams.searchValue;
      } else if (searchParams.searchType === 2) {
        params['userid'] = searchParams.searchValue;
      } else {
        params['id'] = searchParams.searchValue;
      }
    }
    exportApi(params)
      .then((res) => {
        const fileName = `${codeName.value}_${formatToDate(new Date())}.csv`;
        downloadByData(res.data, fileName);
      })
      .catch(() => {
        loading.value = false;
      });
  }
  /** 导出添加客人数据 */
  function exportCustom() {
    const params = {
      status: searchParams.status,
      qrCodeRuleId: codeRuleId.value,
      codeType: 'member',
    };
    if (searchParams.searchValue) {
      if (searchParams.searchType === 1) {
        params['baName'] = searchParams.searchValue;
      } else if (searchParams.searchType === 2) {
        params['userid'] = searchParams.searchValue;
      } else {
        params['id'] = searchParams.searchValue;
      }
    }
    exportCustomApi(params)
      .then((res) => {
        const fileName = `${codeName.value}_${formatToDate(new Date())}添加客人数据.csv`;
        downloadByData(res.data, fileName);
      })
      .catch(() => {
        loading.value = false;
      });
  }
  function getList() {
    loading.value = true;
    const params = {
      status: searchParams.status,
      qrCodeRuleId: codeRuleId.value,
      codeType: 'member',
      page: pagination.value.current,
      size: pagination.value.pageSize,
    };
    if (searchParams.searchValue) {
      if (searchParams.searchType === 1) {
        params['baName'] = searchParams.searchValue;
      } else if (searchParams.searchType === 2) {
        params['userid'] = searchParams.searchValue;
      } else {
        params['id'] = searchParams.searchValue;
      }
    }

    getDetailListApi(params)
      .then((res) => {
        // debugger
        dataList.value = res.list || [];
        loading.value = false;
        pagination.value.total = res.total;
      })
      .catch(() => {
        loading.value = false;
      });
  }
  function rebuild(record) {
    const params = {
      id: record.id,
      qrCodeRuleId: codeRuleId.value,
    };
    generateCodeApi(params).then((res) => {
      if (res) {
        getList();
        success('重新生成中，请稍后刷新页面查看');
      } else {
        error('重新生成失败，请稍后重试');
      }
    });
  }
  function download(record) {
    downloadByUrl({ url: record.qrCodeUrl });
  }
  onMounted(() => {
    codeRuleId.value = route.query.id as string;
    codeName.value = decodeURIComponent(route.query.name as string);
    if (route.query.type == '2') {
      //不展示、员工姓名、Userid、StoreCode、柜台名称
      columns.value = combineColumns;
    }
    handleSearch();
  });
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="scss" scoped>
  @import '@/theme/reset.scss';

  .container-detail {
    margin: 16px 24px;
  }

  .m-t-16 {
    margin-top: 16px;
  }

  .m-l-16 {
    margin-left: 16px;
  }

  .operate {
    padding: 16px 0;
    text-align: left;
  }
</style>
