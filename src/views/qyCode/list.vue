<template>
  <div class="code-container">
    <a-card class="m-b-16">
      <a-form class="search-form" :model="selectForm" layout="vertical">
        <a-form-item :label="`${ruleList[selectForm.type - 1].label} `">
          <a-input-group compact class="compact">
            <a-select
              style="width: 140px"
              v-model:value="selectForm.type"
              @change="changeSelectType"
              :options="ruleList"
            />
            <a-input
              :placeholder="selectForm.type == 1 ? '请输入活码规则名称' : '请输入活码规则id'"
              v-model:value.trim="selectForm.name"
              allowClear
            />
          </a-input-group>
        </a-form-item>

        <a-form-item label="规则类型">
          <a-select v-model:value="selectForm.qrCodeType" placeholder="请选择规则类型" allowClear>
            <a-select-option v-for="item in ruleType" :key="item.value" :value="item.value">{{
              item.label
            }}</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="创建日期">
          <a-range-picker v-model:value="createDate" format="YYYY/MM/DD" allowClear />
        </a-form-item>

        <a-form-item label="生成状态">
          <a-select
            v-model:value="selectForm.qrCodeCreateStatus"
            placeholder="请选择生成状态"
            :options="statusOptions"
            allowClear
          />
        </a-form-item>

        <a-form-item class="is-operation">
          <a-button @click="handleReset" type="text">重置</a-button>
          <a-button @click="handleSearch()">搜索</a-button>
        </a-form-item>
      </a-form>
    </a-card>
    <a-card>
      <a-button class="m-b-16" type="primary" @click="handleCreate"> 创建活码 </a-button>
      <!-- 列表 -->
      <a-table
        v-loading="loading"
        :dataSource="codeList"
        :pagination="pagination"
        :columns="columns"
        :scroll="{ x: '100%' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'qrCodeType'">
            {{ ruleTypeObj[record.qrCodeType] }}
          </template>

          <template v-if="column.key === 'staffArrayTotal'">
            <div v-if="[1, 2].includes(record.qrCodeType)">
              {{ record.staffArrayTotal }}
            </div>
            <div v-else>
              {{ record.staffArrayTotal }}
            </div>
          </template>
          <template v-if="column.key === 'qrCodeCreateStatus'">
            {{ statusObj[record.qrCodeCreateStatus] }}
          </template>

          <template v-if="column.key === 'createdDate'">
            {{ formatToDateTime(record.createdDate) || '--' }}
          </template>
          <template v-if="column.key === 'modifiedDate'">
            {{ formatToDateTime(record.modifiedDate) || '--' }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-button
              type="text"
              v-if="record.qrCodeCreateStatus == 3 || record.qrCodeCreateStatus == 4"
              :class="
                record.qrCodeCreateStatus == 3 || record.qrCodeCreateStatus == 4 ? 'active' : ''
              "
              @click="handleRebuild(record)"
            >
              重新生成
            </a-button>
            <a-button
              v-if="record.qrCodeCreateStatus !== 0 && record.qrCodeCreateStatus !== 2"
              type="text"
              class="active"
              @click="handleDetails(record)"
            >
              详情
            </a-button>
            <a-button
              type="text"
              :class="
                record.qrCodeCreateStatus !== 0 && record.qrCodeCreateStatus !== 2 ? 'active' : ''
              "
              v-if="record.qrCodeCreateStatus !== 0 && record.qrCodeCreateStatus !== 2"
              @click="handleEdit(record)"
            >
              编辑
            </a-button>
            <a-button
              type="text"
              :class="
                record.qrCodeCreateStatus == 1 || record.qrCodeCreateStatus == 4 ? 'active' : ''
              "
              v-if="record.qrCodeCreateStatus == 1 || record.qrCodeCreateStatus == 4"
              @click="handleDownload(record)"
            >
              下载
            </a-button>
            <a-button
              type="text"
              danger
              v-if="record.qrCodeCreateStatus !== 0 && record.qrCodeCreateStatus !== 2"
              :class="
                record.qrCodeCreateStatus !== 0 && record.qrCodeCreateStatus !== 2 ? 'active' : ''
              "
              @click="handleDelete(record)"
            >
              删除
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>
    <!-- 二维码下载 -->
    <download-code
      :syncVisible="syncVisible"
      :codeRuleId="codeRuleId"
      :downPath="downPath"
      @close-download="closeDownload"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, toRefs, onMounted, watch, createVNode } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { getCodeListApi, deleteCodeApi, generateRuleApi } from '@/api/sys/qyCode';

  /** 下载弹框 */
  import downloadCode from './components/download-code.vue';

  const { createMessage, createConfirm } = useMessage();
  const { success, warning } = createMessage;
  const state = reactive({
    columns: [
      {
        title: '活码规则id',
        dataIndex: 'id',
        ellipsis: true,
        width: 300,
      },
      {
        title: '活码规则名称',
        dataIndex: 'qrCodeName',
        ellipsis: true,
        width: 150,
      },
      {
        title: '规则类型',
        dataIndex: 'qrCodeType',
        key: 'qrCodeType',
        align: 'center',
        width: 120,
      },
      {
        title: '员工数/门店数',
        dataIndex: 'staffArrayTotal',
        key: 'staffArrayTotal',
        ellipsis: true,
        width: 150,
        align: 'center',
      },
      {
        title: '生成活码数',
        dataIndex: 'qrCodeTotal',
        ellipsis: true,
        width: 120,
        align: 'center',
      },
      {
        title: '生成状态',
        dataIndex: 'qrCodeCreateStatus',
        key: 'qrCodeCreateStatus',
        width: 120,
        align: 'center',
      },
      {
        title: '创建时间',
        dataIndex: 'createdDate',
        key: 'createdDate',
        width: 180,
      },
      {
        title: '更新时间',
        dataIndex: 'modifiedDate',
        key: 'modifiedDate',
        width: 180,
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 200,
      },
    ],
    /** 遮罩层 */
    loading: false,
    /** 下载弹框 */
    syncVisible: false,
    /** 查询条件 */
    selectForm: {
      type: 1,
      name: '',
      qrCodeType: undefined,
      startTime: '',
      endTime: '',
      qrCodeCreateStatus: undefined,
    },
    /** 创建日期范围 */
    createDate: null,
    /** 活码规则名称或id */
    ruleList: [
      {
        label: '活码规则名称',
        value: 1,
      },
      {
        label: '活码规则id',
        value: 2,
      },
    ],
    /** 规则类型 */
    ruleType: [
      {
        label: '成员专属码',
        value: 1,
      },
      {
        label: '聚合活码',
        value: 2,
      },
      {
        label: '门店活码',
        value: 3,
      },
    ],
    /** 生成状态 */
    statusOptions: [
      {
        label: '未开始',
        value: 0,
      },
      {
        label: '已生成',
        value: 1,
      },
      {
        label: '生成中',
        value: 2,
      },
      {
        label: '生成失败',
        value: 3,
      },
      {
        label: '部分失败',
        value: 4,
      },
    ],
    /** 列表数据 */
    codeList: [],
    /** 活码规则id */
    codeRuleId: 0,
    /** 下载传递参数 */
    codeInfo: {},
    /** 下载压缩包缓存地址 */
    downPath: {},
    ruleTypeObj: {
      1: '成员专属码',
      2: '聚合活码',
      3: '门店活码',
    },
    statusObj: {
      0: '未开始',
      1: '已生成',
      2: '生成中',
      3: '生成失败',
      4: '部分失败',
    },
  });
  const {
    selectForm,
    statusOptions,
    ruleList,
    ruleType,
    createDate,
    syncVisible,
    codeRuleId,
    downPath,
    statusObj,
    ruleTypeObj,
    columns,
    codeList,
    loading,
  } = toRefs(state);
  const pagination = ref({
    total: 0,
    current: 1,
    pageSize: 10,
    onChange: pageChange,
    onShowSizeChange: sizeChange,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条数据`,
  });

  watch(
    () => state.createDate,
    () => {
      if (state.createDate && state.createDate[0] && state.createDate[1]) {
        state.selectForm.startTime = (state.createDate[0] as any).startOf('date').unix();
        state.selectForm.endTime = (state.createDate[1] as any).endOf('date').unix();
      } else {
        state.selectForm.startTime = '';
        state.selectForm.endTime = '';
      }
      console.log(state.selectForm);
    },
    { deep: true },
  );

  function pageChange(page: number, size: number) {
    pagination.value.current = page;
    pagination.value.pageSize = size;
    getList();
  }

  function sizeChange(current: number, size: number) {
    pagination.value.current = 1;
    pagination.value.pageSize = size;
    getList();
  }

  /** 筛选条件 活码规则名称、活码规则id切换 */
  function changeSelectType() {
    state.selectForm.name = '';
  }
  function handleSearch() {
    pagination.value.current = 1;
    getList();
  }
  function getList() {
    state.loading = true;
    const params = {
      page: pagination.value.current,
      size: pagination.value.pageSize,
      qrCodeType: state.selectForm.qrCodeType,
      startTime: state.selectForm.startTime,
      endTime: state.selectForm.endTime,
      qrCodeCreateStatus: state.selectForm.qrCodeCreateStatus,
    };
    // 活码规则名称 输入值
    if (state.selectForm.type == 1) {
      params['qrCodeName'] = state.selectForm.name;
    }
    // 活码规则id 输入值
    if (state.selectForm.type == 2) {
      params['id'] = state.selectForm.name;
    }

    getCodeListApi(params)
      .then((res) => {
        state.codeList = (res && res.list) || [];
        state.loading = false;
        pagination.value.total = res.total;
      })
      .catch(() => {
        state.loading = false;
      });
  }
  function handleReset() {
    state.selectForm.name = '';
    state.selectForm.qrCodeType = undefined;
    state.selectForm.qrCodeCreateStatus = undefined;
    state.selectForm.startTime = '';
    state.selectForm.endTime = '';
    state.selectForm.type = 1;
    pagination.value.current = 1;
    pagination.value.pageSize = 10;
    pagination.value.total = 0;
    state.createDate = null;
    handleSearch();
  }
  function handleCreate() {
    window[import.meta.env.VITE_PREFIX].jumpTo(`/${import.meta.env.VITE_PREFIX}/qyCode/create`);
  }
  function handleDetails(record) {
    if (record.qrCodeType === 3) {
      window[import.meta.env.VITE_PREFIX].jumpTo(
        `/${import.meta.env.VITE_PREFIX}/qyCode/detailStore?id=${record.id}&name=${encodeURIComponent(record.qrCodeName)}`,
      );
    } else {
      window[import.meta.env.VITE_PREFIX].jumpTo(
        `/${import.meta.env.VITE_PREFIX}/qyCode/detailMember?id=${record.id}&type=${record.qrCodeType}&name=${encodeURIComponent(record.qrCodeName)}`,
      );
    }
  }
  /** 重新生成 */
  function handleRebuild(record) {
    const ruleId = record.id;
    generateRuleApi(ruleId).then(() => {
      success('重新生成中，请稍后刷新页面查看');
      getList();
    });
  }
  /** 编辑 */
  function handleEdit(record) {
    window[import.meta.env.VITE_PREFIX].jumpTo(
      `/${import.meta.env.VITE_PREFIX}/qyCode/edit?id=${record.id}`,
    );
  }

  /** 下载 */
  function handleDownload(record) {
    if (record.qrCodeDownloadUrl) {
      state.downPath = JSON.parse(record.qrCodeDownloadUrl);
      state.codeRuleId = record.id;
      state.syncVisible = true;
    } else {
      warning('当前活码还未生成二维码，请先生成二维码');
    }
  }
  /** 关闭下载弹框 */
  function closeDownload(val) {
    state.syncVisible = val.flag;
    state.codeRuleId = 0;
  }
  /** 删除 */
  function handleDelete(record) {
    const codeId = record.id;
    createConfirm({
      title: '删除提醒',
      iconType: 'warning',
      content: createVNode('div', {
        innerHTML: '您确认要删除当前活码吗?<br/>删除后，将从系统中移除且无法恢复!',
      }),
      okText: '确定',
      cancelText: '取消',
      onOk() {
        deleteCodeApi(codeId).then(() => {
          success('删除成功');
          //页码处理
          if (
            state.codeList.length % pagination.value.pageSize === 1 &&
            pagination.value.current > 1
          ) {
            pagination.value.current--;
          }
          getList();
        });
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
  }
  onMounted(() => {
    handleSearch();
  });
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="scss" scoped>
  @import '@/theme/reset.scss';

  .code-container {
    margin: 16px 24px;
  }

  .m-l-16 {
    margin-left: 16px;
  }

  .m-b-16 {
    margin-bottom: 16px;
  }
</style>
