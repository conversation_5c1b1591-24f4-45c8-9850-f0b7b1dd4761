<template>
  <div class="download-code-log">
    <a-modal
      v-model:open="dialogVisible"
      width="550px"
      @cancel="closeDialog"
      :footer="null"
      :confirm-loading="isModal"
    >
      <template #title> 下载联系我二维码 </template>
      <div class="code-box">
        <div class="item-code" v-for="(item, index) in typeList" :key="index">
          <div class="code-type">{{ item.name }}</div>
          <div class="type-tip">
            <div class="mb-2">{{ item.tip1 }}</div>
            <div>{{ item.tip2 }}</div>
          </div>
          <DownloadOutlined
            :style="{ fontSize: '25px', color: '#3779b5' }"
            @click="handledDownload(item.type, item.width)"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
  import { ref, watch, defineComponent } from 'vue';
  import { message } from 'ant-design-vue';
  import { DownloadOutlined } from '@ant-design/icons-vue';
  import { downloadByUrl } from '@/utils/file/download';

  export default defineComponent({
    components: { DownloadOutlined },
    props: {
      syncVisible: {
        type: Boolean,
        default: false,
      },
      codeRuleId: {
        type: Number,
        default: 0,
      },
      downPath: {
        type: Object,
        default: () => ({}),
      },
    },
    setup(props, { emit }) {
      const dialogVisible = ref(props.syncVisible);
      const isModal = ref(false);

      const typeList = [
        {
          type: 1,
          name: '小尺寸',
          tip1: '适用于屏幕类，宣传册类',
          tip2: '边长8cm(258 * 258px)',
          width: 258,
          height: 258,
        },
        {
          type: 2,
          name: '中尺寸',
          tip1: '适用于海报，展架等',
          tip2: '边长15cm(430 * 430px)',
          width: 430,
          height: 430,
        },
        {
          type: 3,
          name: '大尺寸',
          tip1: '适用于幕布，大型广告等',
          tip2: '边长50cm(1417 * 1417px)',
          width: 1417,
          height: 1417,
        },
      ];

      const handledDownload = async (type, width) => {
        isModal.value = true;
        try {
          let downloadUrl;
          if (props.downPath) {
            downloadUrl = props.downPath[width];
          } else {
            message.error('下载失败');
            return;
          }

          downloadByUrl({ url: downloadUrl });
          message.success('下载成功');
          closeDialog();
        } catch (error) {
          message.error('下载失败');
        } finally {
          isModal.value = false;
        }
      };

      const closeDialog = () => {
        emit('closeDownload', { flag: false });
      };

      watch(
        () => props.syncVisible,
        (newVal) => {
          dialogVisible.value = newVal;
        },
      );

      return {
        dialogVisible,
        isModal,
        typeList,
        handledDownload,
        closeDialog,
      };
    },
  });
</script>

<style scoped lang="less">
  .code-box {
    .item-code {
      display: flex;
      align-items: center;
      height: 90px;
      margin-bottom: 20px;
      padding: 20px 30px;
      border-radius: 10px;
      background-color: #f5f6fa;

      .code-type {
        color: #606266;
        font-size: 18px;
        font-weight: 600;
      }

      .type-tip {
        width: 320px;
        margin-left: 30px;
        color: #606266;
        font-size: 14px;
        line-height: 20px;
      }

      .down-img {
        width: 25px;
        height: 25px;
        cursor: pointer;
      }
    }

    .item-code:last-child {
      margin-bottom: 10px;
    }
  }
</style>
