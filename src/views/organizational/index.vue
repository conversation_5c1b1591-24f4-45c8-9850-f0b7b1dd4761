<template>
  <div class="organizational">
    <a-card class="m-b-16">
      <a-form class="search-form" layout="vertical" :model="queryParams">
        <a-form-item :label="`${queryParams.staffType == 1 ? '员工姓名' : 'userid'}`">
          <a-input-group compact class="compact">
            <a-select
              style="width: 140px"
              v-model:value="queryParams.staffType"
              @change="changeSelectType"
            >
              <a-select-option v-for="item in memberType" :key="item.value" :value="item.value">{{
                item.label
              }}</a-select-option>
            </a-select>
            <a-input
              v-model:value.lazy="queryParams.staffValue"
              :placeholder="queryParams.staffType == 1 ? '请输入员工姓名' : '请输入userid'"
              @keyup.enter="handleSearch"
              allowClear
            />
          </a-input-group>
        </a-form-item>

        <a-form-item label="加入状态" name="status">
          <a-select
            v-model:value="queryParams.status"
            placeholder="请选择加入状态"
            allowClear
            :options="activeStatus"
          />
        </a-form-item>

        <a-form-item class="is-operation">
          <a-button @click="resetQuery" type="text">重置</a-button>
          <a-button @click="handleSearch">搜索</a-button>
        </a-form-item>
      </a-form>
    </a-card>
    <a-card>
      <a-row>
        <a-col :span="4">
          <div class="scroll-wrap">
            <a-tree
              :tree-data="deptOptions"
              v-model:selectedKeys="queryParams.id"
              v-model:expandedKeys="ids"
              :blockNode="true"
              :fieldNames="defaultProps"
              :autoExpandParent="true"
              ref="tree"
              node-key="id"
              @select="handleNodeClick"
            />
          </div>
        </a-col>
        <a-col :span="20">
          <a-table
            v-loading="loading"
            :dataSource="empList"
            :columns="columns"
            :pagination="pagination"
            :scroll="{ x: '100%' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <img
                  :src="
                    record.avatar
                      ? record.avatar
                      : 'https://wesociastg.blob.core.chinacloudapi.cn/wesocial-static/manage/2.48.3/icon_avatar.png'
                  "
                  alt=""
                  class="table-text-img"
                />
                <span class="hover-name">{{ record.name }}</span>
              </template>
              <template v-if="column.key === 'position'">
                {{ record.position || '- -' }}
              </template>
              <template v-if="column.key === 'status'">
                {{ statusObj[record.status] }}
              </template>

              <template v-if="column.key === 'extAttrHiredate'">
                <span>{{
                  record.extAttrHiredate ? formatToDateTime(record.extAttrHiredate) : '--'
                }}</span>
              </template>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted, toRefs } from 'vue';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { getEmpListApi, getDeptApi } from '@/api/sys/organizational';

  const state = reactive({
    columns: [
      {
        title: '员工姓名',
        dataIndex: 'name',
        key: 'name',
        ellipsis: true,
        width: 210,
      },
      {
        title: 'Userid',
        dataIndex: 'userid',
        key: 'userid',
        width: 160,
      },
      {
        title: '门店Code',
        dataIndex: 'extAttrStoreId',
        key: 'extAttrStoreId',
        width: 140,
      },
      {
        title: '职务',
        dataIndex: 'position',
        key: 'position',
        ellipsis: true,
        width: 140,
      },
      {
        title: '部门',
        dataIndex: 'departmentName',
        key: 'departmentName',
        ellipsis: true,
        width: 200,
      },
      {
        title: '加入状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
      },
      {
        title: '入职时间',
        dataIndex: 'extAttrHiredate',
        key: 'extAttrHiredate',
        width: 180,
      },
    ],
    rootId: '',
    memberType: [
      {
        label: '员工姓名',
        value: 1,
      },
      {
        label: 'userid',
        value: 2,
      },
    ],
    /**员工列表数据 */
    empList: [],
    showSearch: true,
    /**查询参数 */
    queryParams: {
      staffType: 1,
      staffValue: '',
      status: undefined,
      id: [], //部门id
    },
    /**员工激活状态映射表 */
    activeStatus: [
      { label: '已激活', value: 1 },
      { label: '已禁用', value: 2 },
      { label: '未激活', value: 4 },
      { label: '退出企业', value: 5 },
    ],
    defaultProps: { title: 'departmentName', key: 'id' },
    /**部门列表数据 */
    deptOptions: [],
    /**树结构默认展开的id集合 */
    ids: [] as string[],
    statusObj: {
      1: '已激活',
      2: '已禁用',
      4: '未激活',
      5: '退出企业',
    },
  });
  const {
    queryParams,
    memberType,
    activeStatus,
    statusObj,
    columns,
    empList,
    defaultProps,
    deptOptions,
    ids,
    // updateTime,
  } = toRefs(state);
  const loading = ref(false);
  const pagination = ref({
    total: 0,
    current: 1,
    pageSize: 10,
    onChange: pageChange,
    onShowSizeChange: pageSizeChange,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条数据`,
  });

  function pageChange(page, pageSize) {
    pagination.value.current = page;
    pagination.value.pageSize = pageSize;
    getList();
  }

  function pageSizeChange(current, size) {
    pagination.value.current = 1;
    pagination.value.pageSize = size;
    getList();
  }

  /** 查询所有部门 */
  async function getAllDept() {
    try {
      loading.value = true;
      const res = await getDeptApi();
      state.deptOptions = deptDataHandler(state.rootId, res, []);
      console.log(state.deptOptions);
      loading.value = false;
    } catch (e) {
      console.error(e);
    }
  }
  /** 筛选条件 姓名、userid */
  function changeSelectType() {
    state.queryParams.staffValue = '';
  }

  /**部门数据处理 */
  function deptDataHandler(parentId, arr, newTree) {
    // 遍历输入的数组
    for (let i = arr.length - 1; i >= 0; i--) {
      // 如果当前元素的 parentId 与输入的 parentId 相等
      if (arr[i].parentId === parentId) {
        // 将当前元素添加到结果数组中
        newTree.push(arr[i]);
        // 从原数组中移除当前元素
        arr.splice(i, 1);
      }
    }

    // 对结果数组中的每个元素进行处理
    newTree.map((r) => {
      // 为当前元素添加一个空数组作为children属性
      r.children = [];
      // 递归调用deptDataHandler函数，处理当前元素的children
      deptDataHandler(r.id, arr, r.children);
      // 如果当前元素的children数组长度为0，则删除该属性
      if (r.children.length === 0) {
        delete r.children;
      }
    });
    // 查询1级部门id集合
    getFirstIds(newTree);

    // 返回结果数组
    return newTree;
  }

  /**
   * 找到一级部门的id集合
   * @param arr
   */
  function getFirstIds(arr) {
    arr.forEach((item) => {
      if (item.parentId === state.rootId) state.ids.push(item.id);
    });
  }
  /** 查询用户列表 */
  async function getList() {
    try {
      loading.value = true;
      const params = {
        page: pagination.value.current,
        size: pagination.value.pageSize,
      };
      state.queryParams.id && (params['departmentId'] = state.queryParams.id[0]);
      if (state.queryParams.staffValue) {
        if (state.queryParams.staffType === 1) {
          params['name'] = state.queryParams.staffValue;
        } else {
          params['userid'] = state.queryParams.staffValue;
        }
      }
      state.queryParams.status && (params['status'] = state.queryParams.status);
      const res = await getEmpListApi(params);
      state.empList = res.list || [];
      pagination.value.total = res.total;
      loading.value = false;
    } catch (e) {
      console.log(e);
    }
  }

  /** 点击查询按钮 */
  function handleSearch() {
    pagination.value.current = 1;
    getList();
  }
  /** 点击重置按钮 */
  function resetQuery() {
    state.queryParams.id = [];
    state.queryParams.staffValue = '';
    state.queryParams.staffType = 1;
    state.queryParams.status = undefined;
    handleSearch();
  }
  /** 树节点筛选 */
  function handleNodeClick(selectedKeys) {
    state.queryParams.id = selectedKeys;
    handleSearch();
  }

  onMounted(() => {
    getAllDept();
    getList();
  });
</script>
<style lang="scss">
  @import '@/theme/index.scss';
</style>
<style lang="scss" scoped>
  @import '@/theme/reset.scss';

  ::v-deep {
    .#{$prefix}-tree.#{$prefix}-tree-block-node
      .#{$prefix}-tree-list-holder-inner
      .#{$prefix}-tree-node-content-wrapper,
    .tree-node-content-wrapper {
      flex-shrink: 0;
      white-space: nowrap;
    }
  }

  .organizational {
    margin: 16px 24px;
  }

  .update-time {
    margin-right: 16px;
    color: gray;
    font-size: 12px;
  }

  .m-b-16 {
    margin-bottom: 16px;
  }

  .scroll-wrap {
    width: 100%;
    max-height: 589px;
    overflow: auto;
  }

  .table-text-img {
    width: 25px;
    height: 25px;
    margin-right: 10px;
    border-radius: 50%;
    vertical-align: middle;
  }
</style>
