import { describe, it, expect } from 'vitest';
import { sleep, setObjToUrlParams, deepMerge } from '@/utils';

describe('Utility Functions', () => {
  describe('sleep function', () => {
    beforeAll(() => {
      vi.useFakeTimers();
    });

    afterAll(() => {
      vi.useRealTimers();
    });

    it('should resolve after the specified time', async () => {
      const promise = sleep(100);
      vi.advanceTimersByTime(100);
      await expect(promise).resolves.toBeUndefined();
    });

    it('should resolve immediately when 0 ms is passed', async () => {
      const promise = sleep(0);
      vi.advanceTimersByTime(0);
      await expect(promise).resolves.toBeUndefined();
    });
  });

  describe('setObjToUrlParams', () => {
    it('should correctly add object parameters to a URL', () => {
      const url = 'https://portal-dev.cus-nonprod.kering.cn';
      const obj = { a: '3', b: '4' };
      const result = setObjToUrlParams(url, obj);
      expect(result).toBe('https://portal-dev.cus-nonprod.kering.cn?a=3&b=4');
    });

    it('should handle an empty object', () => {
      const url = 'https://portal-dev.cus-nonprod.kering.cn';
      const obj = {};
      const result = setObjToUrlParams(url, obj);
      expect(result).toBe('https://portal-dev.cus-nonprod.kering.cn?');
    });

    it('should handle a URL that already ends with a question mark', () => {
      const url = 'https://portal-dev.cus-nonprod.kering.cn?';
      const obj = { a: '3', b: '4' };
      const result = setObjToUrlParams(url, obj);
      expect(result).toBe('https://portal-dev.cus-nonprod.kering.cn?a=3&b=4');
    });
  });

  describe('deepMerge', () => {
    it('should merge two objects deeply', () => {
      const source = { a: { b: 1, c: 2 } };
      const target = { a: { b: 2, d: 3 } };
      const result = deepMerge(source, target);
      expect(result).toEqual({ a: { b: 2, c: 2, d: 3 } });
    });

    it('should handle null and undefined values', () => {
      const source = null;
      const target = { a: 1 };
      const result = deepMerge(source, target);
      expect(result).toEqual(target);
    });

    it('should handle different array merge strategies', () => {
      const source = { a: [1, 2, 3] };
      const target = { a: [2, 3, 4] };
      const unionResult = deepMerge(source, target, 'union');
      expect(unionResult).toEqual({ a: [1, 2, 3, 4] });

      const intersectionResult = deepMerge(source, target, 'intersection');
      expect(intersectionResult).toEqual({ a: [2, 3] });

      const concatResult = deepMerge(source, target, 'concat');
      expect(concatResult).toEqual({ a: [1, 2, 3, 2, 3, 4] });

      const replaceResult = deepMerge(source, target, 'replace');
      expect(replaceResult).toEqual({ a: [2, 3, 4] });
    });
  });
});
