import { message } from 'ant-design-vue';

// `navigator.clipboard` 可能因浏览器设置或浏览器兼容而造成兼容问题
export function copyText(text: string, prompt: string | null = '已成功复制到剪切板!') {
  if (navigator.clipboard) {
    return navigator.clipboard
      .writeText(text)
      .then(() => {
        prompt && message.success(prompt);
      })
      .catch((error) => {
        message.error('复制失败!' + error.message);
        return error;
      });
  }

  return Promise.reject(new Error(`"navigator.clipboard" 中存在API错误, 拷贝失败!`));
}
