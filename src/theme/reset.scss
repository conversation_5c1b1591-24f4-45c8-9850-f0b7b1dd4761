::v-deep {
  .search-form {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0;
    padding: 0;
    gap: 16px;
    .#{$prefix}-select {
      // 修复输入框组中的 select 组件边框覆盖问题
      border-inline-end-width: 0;
    }
    .#{$prefix}-btn {
      margin-right: 20px;

      &:last-child {
        margin-right: 0;
      }
    }
    .#{$prefix}-form-item {
      width: calc((100% - 32px) / 3);
      margin-bottom: 0;
    }
    .#{$prefix}-picker {
      width: 100%;
    }

    .is-operation {
      flex: 1;
      align-content: flex-end;
      text-align: right;
    }

    .compact {
      display: flex;
      .#{$prefix}-select {
        width: 100%;
      }
    }
  }
  .#{$prefix}-tree {
    .#{$prefix}-tree-node-content-wrapper.#{$prefix}-tree-node-selected {
      color: #fff;
    }
  }
}
