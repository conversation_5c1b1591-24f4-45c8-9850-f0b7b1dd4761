const CUS_THEME: { [key: string]: string | number } = {
  colorPrimary: '#BDABA0',
  colorSecondary: '#E3C8B6',
  colorTertiary: '#F1E4DB',
  colorBlack1: '#000000',
  colorBlack2: '#323232',
  colorBlack3: '#575757',
  colorWhite: '#FFFFFF',
  colorGrey1: '#818181',
  colorGrey2: '#B3B3B3',
  colorGrey3: '#CACACA',
  colorGrey4: '#E6E6E6',
  colorGrey5: '#EDEDED',
  colorGrey6: '#F2F2F2',
  colorInfo: '#3779B5',
  colorSuccess: '#2CC001',
  colorWarning: '#FCC55E',
  colorError: '#A12117',
  fontSize: 12,
  fontSizeMD: 14,
  fontSizeLG: 16,
  lineHeight: 1.8,
  borderRadius: 2,
};

export const getScssVariables = (prefix: string): string => {
  CUS_THEME.prefix = prefix;

  let scssVariables = '';
  for (const key in CUS_THEME) {
    const unit = ['fontSize', 'fontSizeMD', 'fontSizeLG', 'borderRadius'].includes(key) ? 'px' : '';
    scssVariables += `$${key}: ${CUS_THEME[key]}${unit};\n`;
  }
  return scssVariables;
};

export default {
  token: {
    colorPrimary: CUS_THEME.colorBlack1,
    colorPrimaryHover: CUS_THEME.colorBlack2,
    colorBorder: CUS_THEME.colorGrey3,
    colorBgContainerDisabled: CUS_THEME.colorGrey6,
    colorError: CUS_THEME.colorError,
    colorText: CUS_THEME.colorBlack2,
    colorTextDisabled: CUS_THEME.colorGrey1,
    fontSize: CUS_THEME.fontSize,
    lineHeight: CUS_THEME.lineHeight,
    borderRadius: CUS_THEME.borderRadius,
  },
  components: {
    Input: {
      colorTextPlaceholder: CUS_THEME.colorGrey2,
      colorPrimaryHover: CUS_THEME.colorGrey1,
      colorTextDisabled: CUS_THEME.colorGrey2,
    },
    Button: {},
    Checkbox: {
      paddingXS: 4,
    },
    Radio: {
      fontSizeLG: 16,
      paddingXS: 4,
    },
    Switch: {
      colorTextTertiary: CUS_THEME.colorGrey1,
      colorTextQuaternary: CUS_THEME.colorGrey1,
    },
    Select: {
      colorTextPlaceholder: '#999999',
      colorSplit: 'transparent',
      colorFillSecondary: CUS_THEME.colorGrey6,
      controlItemBgActive: CUS_THEME.colorTertiary,
      colorIcon: CUS_THEME.colorBlack3,
      colorIconHover: CUS_THEME.colorBlack3,
    },
  },
};
