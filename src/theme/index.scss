.#{$prefix}-input:focus,
.#{$prefix}-input-focused {
  border-color: $colorBlack2;
  box-shadow: none;
}

.#{$prefix}-btn-default {
  box-shadow: none;
}

.#{$prefix}-btn-primary {
  box-shadow: none;

  &:not(:disabled):hover {
    background-color: $colorBlack1;
  }
}

.#{$prefix}-btn-primary:disabled {
  border-color: transparent;
  background-color: $colorBlack3;
  color: $colorWhite;
}

.#{$prefix}-btn-default:disabled {
  background-color: $colorWhite;
  color: $colorGrey1;
}

.#{$prefix}-checkbox .#{$prefix}-checkbox-inner::after {
  width: 5px;
  height: 9px;
}

.#{$prefix}-checkbox-indeterminate .#{$prefix}-checkbox-inner {
  border: 1px solid transparent;
  background-color: $colorBlack2;

  &::after {
    width: 8px;
    height: 2px;
    background-color: $colorWhite;
  }
}

.#{$prefix}-radio-wrapper {
  .#{$prefix}-radio-checked {
    .#{$prefix}-radio-inner::after {
      transform: scale(0.5);
    }

    &.#{$prefix}-radio-disabled {
      .#{$prefix}-radio-inner {
        background-color: $colorGrey3;

        &::after {
          background-color: #fff;
        }
      }
    }
  }
}

.#{$prefix}-switch.#{$prefix}-switch-disabled {
  background: $colorGrey3;
}

.#{$prefix}-select:not(.#{$prefix}-select-disabled):not(.#{$prefix}-select-customize-input):not(
    .#{$prefix}-pagination-size-changer
  ) {
  &:hover {
    .#{$prefix}-select-selector {
      border-color: $colorGrey1;
    }
  }
  &.#{$prefix}-select-focused {
    .#{$prefix}-select-selector {
      border-color: $colorBlack2;
    }
  }
}

.#{$prefix}-select-multiple .#{$prefix}-select-selection-item-content {
  margin-inline-end: 8px;
}

.#{$prefix}-select-dropdown {
  .#{$prefix}-select-item-option-selected:not(.#{$prefix}-select-item-option-disabled)
    .#{$prefix}-select-item-option-state {
    color: $colorBlack2;
  }

  // .#{$prefix}-select-item-option-active:not(.#{$prefix}-select-item-option-disabled) {
  //   background-color: $colorGrey6;
  // }

  // .#{$prefix}-select-item-option-selected:not(.#{$prefix}-select-item-option-disabled) {
  //   background-color: $colorTertiary;
  // }
}

.#{$prefix}-select-focused.#{$prefix}-select {
  &:not(.#{$prefix}-select-disabled):not(.#{$prefix}-select-customize-input):not(
      .#{$prefix}-pagination-size-changer
    )
    .#{$prefix}-select-selector {
    box-shadow: none;
  }
}

.#{$prefix}-select-dropdown .rc-virtual-list-scrollbar-thumb {
  width: 6px !important;
  background: rgba($color: #000, $alpha: 40%) !important;
}

.#{$prefix}-tree-select-dropdown {
  .#{$prefix}-select-tree {
    .#{$prefix}-select-tree-node-content-wrapper.#{$prefix}-select-tree-node-selected,
    .#{$prefix}-select-tree-checkbox + span.#{$prefix}-select-tree-node-selected {
      color: #fff;
    }
  }
}

.#{$prefix}-modal {
  $modal-header-height: 48px;
  $modal-footer-height: 64px;
  $modal-padding: 24px;
  $modal-body-padding: 16px;

  .#{$prefix}-modal-content {
    padding: 0;
  }

  .#{$prefix}-modal-close {
    top: 12px;
    right: 20px;
  }

  .#{$prefix}-modal-close-x {
    color: $colorBlack2;
    font-size: 16px;
  }

  .#{$prefix}-modal-header {
    display: flex;
    align-items: center;
    height: $modal-header-height;
    margin-bottom: 0;
    padding: 0 $modal-padding;
    border-bottom: 1px solid $colorGrey5;
  }

  .#{$prefix}-modal-body {
    padding: $modal-body-padding $modal-padding;
  }

  .#{$prefix}-modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: $modal-footer-height;
    margin-top: 0;
    padding: 0 $modal-padding;

    .#{$prefix}-btn-default {
      border-color: transparent;
      color: #666;
    }

    .#{$prefix}-btn-primary {
      margin-inline-start: 4px;
    }
  }
}

.#{$prefix}-table-cell {
  border-inline-end: 1px solid #e8e8e8;

  &:last-child {
    border-inline-end: unset;
  }
}

.#{$prefix}-table-thead {
  .#{$prefix}-table-cell {
    padding: 11px 16px !important;
    border-bottom-color: #e8e8e8 !important;
    background: #f1f1f1 !important;
    border-inline-end: 1px solid #e8e8e8;

    &:last-child {
      border-inline-end: unset;
    }

    .table-header-cell {
      color: #323232;
      font-size: 12px;
      font-weight: 600;
      line-height: 18px;
    }
  }
}

.#{$prefix}-table-tbody {
  .#{$prefix}-table-row {
    .#{$prefix}-table-cell {
      color: #323232;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      word-break: break-all;
    }
  }
}

.#{$prefix}-table-wrapper .#{$prefix}-table-tbody > tr > td {
  // 操作列
  .action-cell {
    %action-btn {
      margin-right: 12px;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      cursor: pointer;
    }

    .btn-primary {
      @extend %action-btn;

      color: #3779b5;
    }

    .btn-danger {
      @extend %action-btn;

      color: #a12117;
    }

    > span:last-child {
      margin-right: 0 !important;
    }
  }
}

.#{$prefix}-picker-focused {
  box-shadow: none;
}

.#{$prefix}-picker-dropdown {
  .#{$prefix}-picker-time-panel-column
    > li.#{$prefix}-picker-time-panel-cell-selected
    .#{$prefix}-picker-time-panel-cell-inner,
  .#{$prefix}-picker-cell-in-view.#{$prefix}-picker-cell-in-range {
    color: #fff;
  }

  .wecom-picker-time-panel-column
    > li.wecom-picker-time-panel-cell
    .wecom-picker-time-panel-cell-inner:hover {
    background-color: #404040;
  }
}

.#{$prefix}-picker-range {
  .#{$prefix}-picker-active-bar {
    bottom: 0;
    height: 1px;
    opacity: 0 !important;
  }
}

.#{$prefix}-table-pagination-right {
  .#{$prefix}-pagination-options-size-changer {
    position: absolute;
    left: 0;
  }
  .#{$prefix}-pagination-total-text {
    order: 1;
    margin-right: 16px;
    line-height: 32px;
  }
  .#{$prefix}-pagination-options {
    order: 2;
    margin-left: 0;
    .#{$prefix}-pagination-options-quick-jumper {
      margin-left: 0;
    }
  }
  .#{$prefix}-pagination-prev {
    order: 3;
  }
  .#{$prefix}-pagination-item {
    order: 4;
  }
  .#{$prefix}-pagination-next {
    order: 5;
  }
}
