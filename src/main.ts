import type { App as typeApp } from 'vue';
import { createApp } from 'vue';
import { renderWithQiankun, qiankunWindow } from 'vite-plugin-qiankun/dist/helper';
import { setupStore } from '@/stores';
import { setupI18n } from '@/locales/setupI18n';
import { setupRouter } from '@/router';
import { setupGlobDirectives } from '@/directives';
import Antd, { ConfigProvider } from 'ant-design-vue';
import { setupQiankun } from '@/plugins/qiankun';

import App from './App.vue';

let app: typeApp;

const mount = async (props: any) => {
  const { container } = props;

  app = createApp(App);

  setupStore(app);

  const router = setupRouter(app, props);

  setupGlobDirectives(app);

  await setupQiankun(app, props, router);

  await setupI18n(app);

  app.use(Antd);
  app.use(ConfigProvider);

  app.mount(container ? container.querySelector('#app') : '#app');
};

renderWithQiankun({
  mount(props) {
    mount(props);
  },
  bootstrap() {
    console.log('%c%s', 'color: green;', 'vite app bootstraped');
  },
  unmount(props) {
    if (!props.keepAlive) {
      app.unmount();
    }
  },
  update(props: any) {
    console.log('vite update: ', props);
  },
});

if (!qiankunWindow.__POWERED_BY_QIANKUN__) {
  mount({});
}
