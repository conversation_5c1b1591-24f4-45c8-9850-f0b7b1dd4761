import { defHttp } from '@/utils/http/axios';

export enum Api {
  ACTIVITYS = '/contactus-qrcode/v1/welcomes',
}

/** 欢迎语列表 */
export const getListApi = (params) => defHttp.get({ url: Api.ACTIVITYS, params });

/** 欢迎语详情 */
export const getDetailApi = (id) => defHttp.get({ url: `${Api.ACTIVITYS}/${id}` });

/** 新建欢迎语 */
export const addApi = (params) => defHttp.post({ url: Api.ACTIVITYS, params });

/** 编辑欢迎语 */
export const updateApi = (id, params) => defHttp.put({ url: `${Api.ACTIVITYS}/${id}`, params });

/** 删除欢迎语 */
export const deleteApi = (id) => defHttp.delete({ url: `${Api.ACTIVITYS}/${id}` });
