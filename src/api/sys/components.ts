import { defHttp } from '@/utils/http/axios';

export enum Api {
  DEPT_LIST = '/sa-relationship/v1/departments',
  EMP_LIST = '/sa-relationship/v1/depEmployees',
  STORE_LIST = '/contactus-qrcode/v1/stores',
  TRANS_EMP_LIST = '/sa-relationship/v1/employees',
}

// 选择员工弹窗 -查部门
export const getDeptApi = () => defHttp.get({ url: Api.DEPT_LIST });
// 选择员工弹窗 -查部门员工
export const getEmpListApi = (params) => defHttp.get({ url: Api.EMP_LIST, params });
// 去重并查员工
export const transEmpListApi = (params) => defHttp.post({ url: Api.TRANS_EMP_LIST, data: params });

// 选择门店弹窗
export const storeListApi = (params) => defHttp.get({ url: Api.STORE_LIST, params });

//选择渠道
export const getChannelListApi = (params) =>
  defHttp.get({ url: '/contactus/v1/contact-channels', params });
// 选择渠道组
export const getGroupListApi = (params) =>
  defHttp.get({ url: '/contactus-qrcode/v1/contact-channel-groups', params });
// 上传文件
export const uploadFileApi = (data) => {
  const formData = new FormData();
  formData.append('file', data.file);
  return defHttp.post({
    url: '/contactus-qrcode/v1/upload',
    timeout: 1000 * 60 * 60,
    headers: { 'Content-Type': 'multipart/form-data' },
    params: formData,
  });
};
