import { defHttp } from '@/utils/http/axios';

export enum Api {
  ACTIVITYS = '/contactus-qrcode/v1/activitys',
  STOP = '/contactus-qrcode/v1/activitys/stop',
  STATISTIC = '/contactus-qrcode/v1/activitys/dataInfo',
  STATISTIC_LIST = '/contactus-qrcode/v1/activitys/data-params-list',
  EXPORT_LIST = '/contactus-qrcode/v1/activitys/download/activity-data',
  EXPORT_DAY = '/contactus-qrcode/v1/activitys/download/activity-detail',
}

/** 推优活码列表 */
export const getActivityListApi = (params) => defHttp.get({ url: Api.ACTIVITYS, params });

/** 推优活码详情 */
export const getActivityDetailApi = (id) => defHttp.get({ url: `${Api.ACTIVITYS}/detail/${id}` });

/** 新建推优活码 */
export const addActivityApi = (params) => defHttp.post({ url: Api.ACTIVITYS, params });

/** 编辑推优活码 */
export const updateActivityApi = (id, params) =>
  defHttp.put({ url: `${Api.ACTIVITYS}/${id}`, params });

/** 删除推优活码 */
export const deleteActivityApi = (id) => defHttp.delete({ url: `${Api.ACTIVITYS}/${id}` });

/** 终止推优活码 */
export const stopActivityApi = (id) => defHttp.post({ url: `${Api.STOP}/${id}` });

/** 访问统计数据 */
export const getStaticsApi = (params) => defHttp.get({ url: Api.STATISTIC, params });

/** 访问统计列表数据 */
export const getStaticsListApi = (params) => defHttp.get({ url: Api.STATISTIC_LIST, params });

export const exportListApi = (params) =>
  defHttp.get(
    { url: Api.EXPORT_LIST, params, responseType: 'blob' },
    { isReturnNativeResponse: true },
  );

export const exportDayApi = (params) =>
  defHttp.get(
    { url: Api.EXPORT_DAY, params, responseType: 'blob' },
    { isReturnNativeResponse: true },
  );
