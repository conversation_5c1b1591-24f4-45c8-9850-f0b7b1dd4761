import { defHttp } from '@/utils/http/axios';

export enum Api {
  COMMON = '/contactus-qrcode/v1/code_rules',
  COMMON_OTHER = '/contactus-qrcode/v1/code_rules/',
  COMMON_DETAIL = '/contactus-qrcode/v1/code_rules/detail/',
  GENERATERULE = '/contactus-qrcode/v1/code_rules/regenerate',
  DETAIL_LIST = '/contactus-qrcode/v1/code_rules/codes',
  GENERATE = '/contactus-qrcode/v1/qrcode-list/regenerate',
}

// Get personal center-basic settings
export const getCodeListApi = (params) => defHttp.get({ url: Api.COMMON, params });

export const deleteCodeApi = (codeId) => defHttp.delete({ url: Api.COMMON_OTHER + codeId });

export const generateRuleApi = (codeId) =>
  defHttp.post({ url: Api.GENERATERULE, params: { id: codeId } });

export const addCodeApi = (params) => defHttp.post({ url: Api.COMMON, params });

export const updateCodeApi = (params) => defHttp.put({ url: Api.COMMON_OTHER + params.id, params });

export const getCodeDetailApi = (codeId) => defHttp.get({ url: Api.COMMON_DETAIL + codeId });

//企微活码详情列表
export const getDetailListApi = (params) => defHttp.get({ url: Api.DETAIL_LIST, params });

export const exportApi = (params) =>
  defHttp.get(
    { url: Api.COMMON + '/download/qrCode-data', params, timeout: 1000 * 60, responseType: 'blob' },
    { isReturnNativeResponse: true },
  );

export const exportCustomApi = (params) =>
  defHttp.get(
    {
      url: Api.COMMON + '/download/qrCode-detail',
      params,
      timeout: 1000 * 60,
      responseType: 'blob',
    },
    { isReturnNativeResponse: true },
  );

//企微活码详情-重新生成
export const generateCodeApi = (params) => defHttp.put({ url: Api.GENERATE, params });
