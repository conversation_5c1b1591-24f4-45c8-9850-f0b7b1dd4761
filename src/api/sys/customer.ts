import { defHttp } from '@/utils/http/axios';

export enum Api {
  CUSTOMER = '/sa-relationship/v1/externals',
  CUSTOMER_DETAIL = '/sa-relationship/v1/externals/detail',
  SALESMAN = '/sa-relationship/v1/externals/belong-to-members?',
  FOOTPRINT = '/sa-relationship/v1/externals/footprints',
  CHANNELS_SELECT = '/contactus-qrcode/v1/contact-channels',
  QYCODE_LIST = '/contactus-qrcode/v1/code_rules',
  ACTIVITYS = '/contactus-qrcode/v1/activitys',
  EXPORT = '/sa-relationship/v1/externals/export',
}

/** 获取客户列表 */
export const getCustomerListApi = (params) => defHttp.get({ url: Api.CUSTOMER, params });

/** 获取客户详情 */
export const getCustomerDetailApi = (id, params) =>
  defHttp.get({ url: `${Api.CUSTOMER_DETAIL}/${id}`, params });

/** 导出客户列表 */
export const exportCustomerListApi = (params) =>
  defHttp.get(
    { url: Api.EXPORT, params, timeout: 60 * 1000, responseType: 'blob' },
    { isReturnNativeResponse: true },
  );

/** 获取导购列表 */
export const getSalesmanListApi = (params) => defHttp.get({ url: Api.SALESMAN, params });

/** 获取足迹列表 */
export const getFootprintListApi = (params) => defHttp.get({ url: Api.FOOTPRINT, params });

/** 获取渠道下拉列表 */
export const getChannelSelectApi = (params) => defHttp.get({ url: Api.CHANNELS_SELECT, params });

/** 获取企微活码列表 */
export const getQycodeListApi = (params) => defHttp.get({ url: Api.QYCODE_LIST, params });

/** 推优活码列表 */
export const getActivityListApi = (params) => defHttp.get({ url: Api.ACTIVITYS, params });
