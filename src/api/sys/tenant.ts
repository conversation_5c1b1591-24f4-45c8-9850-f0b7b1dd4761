import { defHttp } from '@/utils/http/axios';
import type { TenantsParams, TenantUsersModel, TenantsModel } from './model/tenantModel';

enum Api {
  GetTenantUsers = '/auth-permission/v1/tenant-users',
  GetTenants = '/tenant-mgmt/v1/tenants',
}

export const getTenantUsers = () => {
  return defHttp.get<TenantUsersModel>({ url: Api.GetTenantUsers });
};

export const getTenants = (params: TenantsParams) => {
  return defHttp.get<TenantsModel>({ url: Api.GetTenants, params });
};
