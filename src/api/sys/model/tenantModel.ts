export interface TenantInfo {
  id: string;
  tenantId: string;
  name: string;
  tenantCode: string;
  tenantName: string;
  logo: string;
}

export type TenantInfoIdOnly = Pick<TenantInfo, 'tenantId'>;

export interface TenantUsersModel {
  userName: string;
  loginName: string;
  tenantList: TenantInfoIdOnly[];
}

export interface TenantsModel {
  totalCount: number;
  totalPages: number;
  pageNumber: number;
  pageSize: number;
  data: TenantInfo[];
}

export interface TenantsParams {
  tenantId?: string;
  tenantName?: string;
  tenantIds?: string;
  pageNumber?: string;
  pageSize?: string;
}
