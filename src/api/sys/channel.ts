import { defHttp } from '@/utils/http/axios';

export enum Api {
  CHANNEL_GROUPS = '/contactus-qrcode/v1/contact-channel-groups',
  CHANNEL = '/contactus/v1/contact-channels',
  EXPORT = '/contactus/v1/contact-channels/export',
}

/** 获取渠道分组列表 */
export const getChannelGroupsApi = (params) => defHttp.get({ url: Api.CHANNEL_GROUPS, params });

/** 创建渠道分组 */
export const createChannelGroupApi = (params) => defHttp.post({ url: Api.CHANNEL_GROUPS, params });

/** 删除渠道分组 */
export const deleteChannelGroupApi = (id, params) =>
  defHttp.delete({ url: `${Api.CHANNEL_GROUPS}/${id}`, params });

/** 获取渠道列表 */
export const getChannelListApi = (params) => defHttp.get({ url: Api.CHANNEL, params });

/** 新增渠道 */
export const createChannelApi = (params) => defHttp.post({ url: Api.CHANNEL, params });

/** 删除渠道 */
export const deleteChannelApi = (id, params) =>
  defHttp.delete({ url: `${Api.CHANNEL}/${id}`, params });

/** 修改渠道 */
export const updateChannelApi = (id, params) =>
  defHttp.put({ url: `${Api.CHANNEL}/${id}`, params });

// 导出渠道
export const exportChannelApi = (params) =>
  defHttp.get(
    { url: Api.EXPORT, params, timeout: 60 * 1000, responseType: 'blob' },
    { isReturnNativeResponse: true },
  );
