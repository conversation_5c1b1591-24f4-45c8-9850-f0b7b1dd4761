<template>
  <a-popover v-model:open="showDialog" placement="bottom" trigger="click">
    <template #content>
      <div class="emj-boxs">
        <a-tabs tab-position="top" style="height: 200px">
          <a-tab-pane v-for="(item, index) in emojiList" :key="index" :tab="item.type">
            <div class="emj-box">
              <span class="emjs" v-for="(v, i) in item.list" :key="i" @click="addEmj(v)">
                {{ v }}
              </span>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </template>
    <slot name="reference">
      <img class="image" src="@/assets/images/face_icon.png" alt="" />
    </slot>
  </a-popover>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import emoji from './emoji.json';

  interface EmojiItem {
    type: string;
    list: string[];
  }
  const emit = defineEmits(['select']);

  const emojiList = ref<EmojiItem[]>([]);

  const showDialog = ref(false);

  const addEmj = (emj: string) => {
    emit('select', emj);
    showDialog.value = false;
  };

  onMounted(() => {
    for (const key in emoji) {
      const arr: string[] = [];
      for (const em of emoji[key]) {
        arr.push(em);
      }
      const item = {
        type: key,
        list: arr,
      };
      emojiList.value.push(item);
    }
  });
</script>

<style lang="less" scoped>
  .emj-boxs {
    width: 400px;
    height: 300px;
  }

  .emj-box {
    height: 250px;
    overflow: auto;
    font-size: 16px;
    user-select: none;
  }

  .emjs {
    display: inline-block;
    width: 28px;
    height: 28px;
    padding: 3px;
    text-align: center;
    cursor: pointer;
  }

  .text-head-button {
    color: rgb(96 98 102 / 100%);
    font-family: PingFangSC-Regular, 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
  }

  .image {
    width: 14px;
    height: 14px;
    cursor: pointer;
  }
</style>
