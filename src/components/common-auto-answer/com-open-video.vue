<template>
  <div class="com-open-video">
    <div class="img-view">
      <span class="close-btn" @click="closeModel">
        <CloseOutlined />
      </span>
      <div class="video-size">
        <video controls>
          <source :src="videoUrl" type="video/mp4" />
        </video>

        <!-- 视频字幕 -->
        <track kind="subtitles" src="" srclang="en" label="English" default />
        <!-- 视频描述 -->
        <track kind="descriptions" src="" srclang="en" label="English Descriptions" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { CloseOutlined } from '@ant-design/icons-vue';

  defineProps({
    videoUrl: {
      type: String,
      default: '',
    },
  });

  //暴露内部方法
  const emits = defineEmits(['closeModel']);

  const closeModel = () => {
    emits('closeModel');
  };
</script>

<style lang="less" scoped>
  .com-open-video {
    .img-view {
      position: fixed;
      z-index: 9999;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      background: rgb(0 0 0 / 70%);
    }

    .close-btn {
      position: fixed;
      z-index: 99991;
      top: 12px;
      right: 20px;
      width: 40px;
      height: 40px;
      color: #bbb;
      font-size: 20px;
      line-height: 40px;
      text-align: center;
      cursor: pointer;
    }

    .video-size {
      width: 610px;
      height: 346px;
      margin: 0 auto;
    }

    video {
      width: 100%;
      height: 100%;
      margin: 200px 0;
    }
  }
</style>
