<template>
  <a-modal
    :title="dialogTitle"
    v-model:open="dialogVisible"
    :maskClosable="false"
    :keyboard="false"
    width="700px"
    @cancel="closeDialog"
  >
    <a-form ref="materialFormRef" :model="materialForm" :rules="rules" layout="vertical">
      <!-- 链接标题 -->
      <a-form-item v-if="type == 'link'" label="链接标题" name="title">
        <a-input v-model:value="materialForm.title" placeholder="请输入链接标题" />
      </a-form-item>
      <!-- 链接描述 -->
      <a-form-item v-if="type == 'link'" label="链接描述" name="desc">
        <a-input v-model:value="materialForm.desc" placeholder="请输入链接描述" />
      </a-form-item>
      <!-- Appid -->
      <a-form-item v-if="type == 'miniprogram'" label="Appid" name="appid">
        <a-input v-model:value="materialForm.appid" placeholder="请输入小程序Appid" />
      </a-form-item>
      <!-- 卡片标题 -->
      <a-form-item v-if="type == 'miniprogram'" label="卡片标题" name="title">
        <a-input v-model:value="materialForm.title" placeholder="请输入卡片标题" />
      </a-form-item>
      <!-- 封面图 -->
      <a-form-item label="封面图" name="fileList">
        <a-upload
          class="custom"
          :class="materialForm.fileList.length > 0 ? 'hide' : ''"
          v-model:file-list="materialForm.fileList"
          :max-count="1"
          accept=".jpg,.png"
          :show-upload-list="true"
          :before-upload="beforeUpload"
          :custom-request="customRequest"
          :on-remove="handleRemove"
          list-type="picture-card"
        >
          <PlusOutlined />
        </a-upload>
      </a-form-item>
      <!-- URL -->
      <a-form-item v-if="type == 'link'" label="URL" name="url">
        <a-input v-model:value="materialForm.url" placeholder="请输入URL" />
      </a-form-item>
      <!-- 页面路径 -->
      <a-form-item v-if="type == 'miniprogram'" label="页面路径" name="page">
        <a-input v-model:value="materialForm.page" placeholder="请输入页面路径" />
        <div>
          <span class="ant-input__tip">小程序打开后的路径。</span>
        </div>
      </a-form-item>
      <!-- 拼接员工参数 -->
      <a-form-item label="拼接员工参数" name="appendParams" v-if="type == 'miniprogram'">
        <a-checkbox-group v-model:value="appendParamsList">
          <template v-for="item in paramOptions" :key="item.value">
            <div class="row">
              <a-row justify="start" align="middle">
                <a-col :span="6">
                  <a-checkbox :value="item.value">{{ item.label }}</a-checkbox>
                </a-col>
                <a-col :span="10">
                  <span class="custom-key">自定义key:</span>
                  <a-input
                    :disabled="!appendParamsList.includes(item.value)"
                    class="custom"
                    v-model:value="appendParams[item.value]"
                  />
                </a-col>
              </a-row>
            </div>
          </template>
        </a-checkbox-group>
        <div class="ant-checkbox__tip"> 勾选后系统会自动在URL后拼接自定义参数。 </div>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="closeDialog">取消</a-button>
      <a-button type="primary" @click="handleConfirm">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, defineProps, defineEmits, reactive, onMounted, watch } from 'vue';
  import { FormInstance, Upload } from 'ant-design-vue';
  import { stringToCharts } from '@/utils/index';
  import { uploadFileApi } from '@/api/sys/components';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const { error, success } = createMessage;

  const props = defineProps<{
    addDialogVisible: boolean;
    type: string;
    isEdit: boolean;
    rowData: Record<string, any>;
  }>();
  console.log(props.rowData);
  const emits = defineEmits(['updateMaterial']);

  const dialogVisible = ref(props.addDialogVisible);
  const dialogTitle = ref(props.type === 'link' ? '链接' : '微信小程序');

  const materialFormRef = ref<FormInstance>();

  interface IMaterialForm {
    msgtype: string;
    title: string;
    desc?: string;
    appid?: string;
    picurl: string;
    url?: string;
    page?: string;
    appendParams: any;
    fileList: any[];
  }

  const materialForm: IMaterialForm = reactive({
    msgtype: props.type,
    title: '',
    desc: '',
    appid: '',
    picurl: '',
    url: '',
    page: '',
    appendParams: {},
    fileList: [],
  });

  const paramOptions = [
    {
      label: 'Userid',
      value: 'user_id',
    },
    // {
    //   label: 'BaCode',
    //   value: 'ba_code',
    // },
    {
      label: '门店Code',
      value: 'store_code',
    },
  ];

  const appendParamsList = ref<string[]>([]);
  const appendParams = reactive({
    user_id: 'saCode',
    // ba_code: 'ba_code',
    store_code: 'storeNo',
  });

  const rules = reactive({
    title: [{ required: true, validator: validTitle, trigger: ['blur', 'change'] }],
    desc: [{ required: true, validator: validDesc, trigger: ['blur', 'change'] }],
    fileList: [{ type: 'array', required: true, message: '图片不能为空', trigger: 'blur' }],
    appid: [{ required: true, message: 'Appid不能为空', trigger: 'blur' }],
    url: [{ required: true, validator: validUrl, trigger: ['blur', 'change'] }],
    page: [{ required: true, validator: validPageUrl, trigger: ['blur', 'change'] }],
  });
  watch(
    () => props.addDialogVisible,
    (newValue) => {
      dialogVisible.value = newValue;
    },
  );
  function validTitle(rule: any, value: string) {
    const maxLength = props.type === 'miniprogram' ? 64 : 128;
    const message = props.type === 'miniprogram' ? '卡片标题' : '链接标题';
    if (!value) {
      return Promise.reject(`${message}不能为空`);
    }
    const length = stringToCharts(value).length;
    if (length > maxLength) {
      return Promise.reject(`${message}过长`);
    }
    return Promise.resolve();
  }

  function validDesc(rule: any, value: string) {
    if (!value) {
      return Promise.reject('链接描述不能为空');
    } else if (stringToCharts(value).length > 512) {
      return Promise.reject('链接描述过长');
    } else {
      return Promise.resolve();
    }
  }

  function validUrl(rule: any, value: string) {
    if (!value) {
      return Promise.reject('URL不能为空');
    }
    const match = /^(?:https?:\/\/)?[a-zA-Z0-9.-]+(?:\/[^ ]*)?$/;

    const flag = match.test(value);
    if (flag) {
      return Promise.resolve();
    } else {
      return Promise.reject('请输入正确的URL');
    }
  }

  function validPageUrl(rule: any, value: string) {
    const result = value.trim();
    if (!result) {
      return Promise.reject('页面路径不能为空');
    } else if (result.length > 200) {
      return Promise.reject('已超出200个字符，请重新编辑');
    } else {
      return Promise.resolve();
    }
  }

  function beforeUpload(file: File) {
    const isRightType = file.type === 'image/jpeg' || file.type === 'image/png';
    const isRightSize = file.size / 1024 / 1024 < 2;
    const name = file.name.substring(file.name.lastIndexOf('.') + 1).toLocaleLowerCase();
    if (!isRightType || (name !== 'jpg' && name !== 'png')) {
      error('封面图只能是 JPG或PNG 格式!');
      return Upload.LIST_IGNORE;
    }
    if (!isRightSize) {
      error('封面图大小不能超过 2MB!');
      return Upload.LIST_IGNORE;
    }
    return true;
  }

  async function customRequest({ file, onSuccess, onError }) {
    try {
      const data = {
        file: file,
      };

      const res = await uploadFileApi(data);
      materialForm.picurl = res.url || '';
      if (res.url) onSuccess();
      if (!res.url) onError();
    } catch (e) {
      console.error('上传校验失败');
      onError();
      handleRemove();
    }
  }

  function handleRemove() {
    materialForm.fileList.length = 0;
    materialForm.picurl = '';
  }

  function closeDialog() {
    emits('updateMaterial', { flag: false });
  }

  function handleConfirm() {
    let flag = true;
    let sameKey = false;
    const customMap = new Map();
    const tempAppendParams = {};
    for (const value of appendParamsList.value) {
      if (!appendParams[value]) {
        flag = false;
        continue; // 如果 appendParams 中没有当前 value，设置 flag 并继续下一次循环
      }
      if (!customMap.has(appendParams[value])) {
        customMap.set(appendParams[value], '1');
      } else {
        sameKey = true; // 如果 customMap 中已经存在 appendParams[value]，则设置 sameKey
      }
      // 已勾选，传参
      tempAppendParams[value] = appendParams[value];
    }
    if (!flag) {
      error('已选拼接员工参数，不能为空！');
      return;
    }
    if (sameKey) {
      error('已选拼接员工参数自定义key不能重复！');
      return;
    }
    materialFormRef.value
      ?.validate()
      .then(() => {
        if (!flag || sameKey) return;
        success('设置成功！');
        materialForm.appendParams = { ...tempAppendParams };
        if (props.type === 'link') {
          delete materialForm.appid;
          delete materialForm.page;
        }
        if (props.type === 'miniprogram') {
          delete materialForm.url;
          delete materialForm.desc;
        }
        emits('updateMaterial', { data: materialForm, flag: false });
      })
      .catch(() => {
        console.log('表单校验失败');
      });
  }

  onMounted(() => {
    if (props.isEdit) {
      Object.assign(materialForm, props.rowData);
      const appendParamsListArr: string[] = [];
      const appendParamsObj: Record<string, string> = { ...appendParams };
      for (const key in materialForm.appendParams) {
        if (['user_id', 'store_code'].includes(key)) {
          appendParamsListArr.push(key);
        }
        appendParamsObj[key] = materialForm.appendParams[key];
      }
      appendParamsList.value = appendParamsListArr;
      Object.assign(appendParams, appendParamsObj);
    }
  });
</script>

<style lang="scss" scoped>
  /** 字段下方提示 */
  .ant-checkbox__tip,
  .ant-input__tip {
    color: #aaa;
  }

  .row {
    flex-grow: 1;
    margin-bottom: 10px;
  }

  .custom-key {
    margin-right: 10px;
    font-size: 14px;
  }

  .custom {
    ::v-deep {
      &.hide {
        .#{$prefix}-upload {
          display: none;
        }
      }

      .#{$prefix}-upload-list-item-actions a {
        display: none;
      }
    }
  }
</style>
