<template>
  <a-form-item label="消息内容" name="text" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
    <div class="publish-content-box">
      <!-- 表情选择区域-->
      <div class="select-box-insert">
        <add-emoji-comp @select="addEmoji" class="add-emoji">
          <template #reference>
            <a-button class="insert-btn" :disabled="disabled">
              <img class="btn-img" src="@/assets/images/face_icon.png" alt="" />
              插入表情
            </a-button>
          </template>
        </add-emoji-comp>
      </div>
      <!--多行文本区域-->
      <a-textarea
        class="textarea-box"
        v-model:value="answer.textContent"
        :rows="9"
        :maxlength="maxLength"
        placeholder="请输入消息内容"
        show-count
      />
    </div>
  </a-form-item>
  <a-form-item label="消息素材" name="material" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
    <!-- 图片、视频、链接、小程序选择区域-->
    <div class="material-select-box">
      <div v-if="!disabled" class="select-btn">
        <a-popover placement="topLeft">
          <template #content>
            <div class="select-material-list">
              <div class="item-select" v-for="(item, index) in materialSelectList" :key="index">
                <!-- 图片 -->
                <a-upload
                  v-if="item.type == 'image'"
                  v-model:file-list="item.fileList"
                  :show-upload-list="false"
                  :accept="item.accept"
                  :before-upload="(file) => beforeUploadImg(file)"
                  :custom-request="customRequestImg"
                >
                  <div class="img-icon"></div>
                </a-upload>
                <!-- 链接、小程序-->
                <div v-else-if="item.type == 'link'" @click="setMaterial(item)">
                  <div class="link-icon"></div>
                </div>
                <div v-else-if="item.type == 'miniprogram'" @click="setMaterial(item)">
                  <div class="mini-icon"></div>
                </div>
                <!-- 视频 -->
                <a-upload
                  v-else-if="item.type == 'video'"
                  v-model:file-list="item.fileList"
                  :show-upload-list="false"
                  :accept="item.accept"
                  :before-upload="(file) => beforeUploadVideo(file)"
                  :custom-request="customRequestVideo"
                >
                  <div class="video-icon"></div>
                </a-upload>
                <!-- 文件 -->
                <a-upload
                  v-else
                  v-model:file-list="item.fileList"
                  :show-upload-list="false"
                  :accept="item.accept"
                  :before-upload="(file) => beforeUploadFile(file)"
                  :custom-request="customRequestFile"
                >
                  <div class="file-icon"></div>
                </a-upload>
                <div class="item-tip">{{ item.tip }}</div>
              </div>
            </div>
          </template>
          <a-button type="primary" :disabled="disabled">
            <span>新增素材</span>
          </a-button>
        </a-popover>
        <p class="tips"> 支持最多{{ maxMaterialLength }}个不同类型的素材。 </p>
      </div>
      <div :style="{ marginTop: disabled ? '5px' : '' }">
        <div class="material-title">
          <span>
            已选
            <span class="material-number"
              >{{ answer.materialList.length }}/{{ maxMaterialLength }}</span
            >
            个素材
          </span>
          <a-popconfirm
            v-if="!disabled"
            title="你确认移除全部素材吗？"
            ok-text="确认"
            cancel-text="取消"
            @confirm="answer.materialList.length = 0"
          >
            <a-button type="text" class="material-remove-all"> 全部移除 </a-button>
          </a-popconfirm>
        </div>
        <div class="material-list">
          <a-table
            :loading="answer.isUpload || answer.isChecking"
            :data-source="answer.materialList"
            :pagination="false"
            :row-key="(record) => record.id"
            :bordered="true"
          >
            <a-table-column title="素材编号" align="center" width="80">
              <template #default="{ record }">
                {{ findIndex(record) + 1 }}
              </template>
            </a-table-column>
            <!-- 素材名称 -->
            <a-table-column title="素材名称" align="center" ellipsis>
              <template #default="{ record }">
                {{ record.name || record.title || '未配置' }}
              </template>
            </a-table-column>
            <a-table-column title="素材类型" align="center" data-index="msgtype" ellipsis>
              <template #default="{ record }">
                {{ typeNameFilter[record.msgtype] }}
              </template>
            </a-table-column>
            <a-table-column title="操作" align="center" min-width="100">
              <template #default="{ record }">
                <div class="cell-operate">
                  <!-- 预览 -->
                  <a-button
                    v-if="record.msgtype == 'image' || record.msgtype == 'video'"
                    type="text"
                    class="operate-text"
                    @click="openPreview(record)"
                  >
                    预览
                  </a-button>
                  <!-- 编辑 -->
                  <a-button
                    v-if="
                      !disabled && (record.msgtype == 'link' || record.msgtype == 'miniprogram')
                    "
                    type="text"
                    class="operate-text"
                    @click="openEdit(record, findIndex(record))"
                  >
                    编辑
                  </a-button>
                  <!-- 图片替换 -->
                  <a-upload
                    v-if="!disabled && record.msgtype == 'image'"
                    accept=".jpg,.png"
                    :max-count="1"
                    :show-upload-list="false"
                    :before-upload="
                      (e) => {
                        return beforeUploadImg(e, findIndex(record));
                      }
                    "
                    :custom-request="
                      (e) => {
                        customRequestImg(e, findIndex(record));
                      }
                    "
                  >
                    <template #default>
                      <a-button type="text" class="operate-text"> 替换 </a-button>
                    </template>
                  </a-upload>
                  <!-- 视频替换 -->
                  <a-upload
                    v-if="!disabled && record.msgtype == 'video'"
                    accept=".mp4"
                    :max-count="1"
                    :show-upload-list="false"
                    :before-upload="
                      (e) => {
                        return beforeUploadVideo(e, findIndex(record));
                      }
                    "
                    :custom-request="
                      (e) => {
                        customRequestVideo(e, findIndex(record));
                      }
                    "
                  >
                    <template #default>
                      <a-button type="text" class="operate-text"> 替换 </a-button>
                    </template>
                  </a-upload>
                  <!-- 文件替换 -->
                  <a-upload
                    v-if="!disabled && record.msgtype == 'file'"
                    accept=".txt, .pdf, .ppt, .pptx, .doc, .docx, .xls, .xlsx"
                    :show-upload-list="false"
                    :max-count="1"
                    :before-upload="
                      (e) => {
                        return beforeUploadFile(e, findIndex(record));
                      }
                    "
                    :custom-request="
                      (e) => {
                        customRequestFile(e, findIndex(record));
                      }
                    "
                  >
                    <template #default>
                      <a-button type="text" class="operate-text"> 替换 </a-button>
                    </template>
                  </a-upload>
                  <!-- 移除 -->
                  <a-popconfirm
                    v-if="!disabled && !record.parentType"
                    title="你确认删除该素材吗？"
                    ok-text="确认"
                    cancel-text="取消"
                    @confirm="deleteMaterial(findIndex(record))"
                  >
                    <a-button danger type="text" class="operate-text"> 移除 </a-button>
                  </a-popconfirm>
                </div>
              </template>
            </a-table-column>
          </a-table>
        </div>
      </div>
    </div>
  </a-form-item>
  <!-- 预览视频 -->
  <open-video-comp v-if="is_open_video" :videoUrl="videoUrl" @close-model="is_open_video = false" />
  <!-- 添加链接、小程序弹框 -->
  <add-material-comp
    v-if="addDialogVisible"
    :add-dialog-visible="addDialogVisible"
    :type="type"
    :row-data="rowData"
    :is-edit="isEditMaterial"
    @update-material="updateMaterial"
  />
</template>

<script setup lang="ts">
  // computed
  import { ref, reactive, nextTick } from 'vue';
  import { Upload } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { createImgPreview } from '@/components/Preview';
  import AddEmojiComp from './add-emoji.vue';
  import OpenVideoComp from './com-open-video.vue';
  import AddMaterialComp from './add-material.vue';
  import { uploadFileApi } from '@/api/sys/components';

  const { createMessage } = useMessage();
  const { error, warning } = createMessage;
  interface Answer {
    textContent: string;
    materialList: Array<any>;
    isUpload: boolean;
    isChecking: boolean;
  }
  const props = withDefaults(
    defineProps<{
      maxMaterialLength?: number;
      disabled?: boolean;
    }>(),
    {
      maxMaterialLength: 9,
      /** 全局禁用表示标识 */
      disabled: false,
    },
  );
  const answer = defineModel<Answer>('answer', {
    required: true,
    default: () => {
      return {
        textContent: '',
        materialList: [],
        isUpload: false,
        isChecking: false,
      };
    },
  });

  const maxLength = 500;
  const isEditMaterial = ref(false);
  const emojiShow = ref(false);

  let rowData = reactive<any>({});
  const addDialogVisible = ref(false);
  const type = ref<string>('');
  const materialName = ref('');
  const is_open_video = ref(false);
  const videoUrl = ref('');
  const rowIndex = ref<number | null>(null);

  const typeNameFilter = {
    image: '图片',
    link: '链接',
    miniprogram: '小程序',
    video: '视频',
    file: '文件',
  };

  const materialSelectList = [
    {
      type: 'image',
      tip: '图片',
      dialog: 'pictureDialog',
      accept: '.jpg,.png',
      fileList: [],
    },
    {
      type: 'link',
      tip: '链接',
      dialog: 'urlDialog',
    },
    {
      type: 'miniprogram',
      tip: '小程序',
      dialog: 'appletDialog',
    },
    {
      type: 'video',
      tip: '视频',
      dialog: 'videoDialog',
      accept: '.mp4',
      fileList: [],
    },
    {
      type: 'file',
      tip: '文件',
      dialog: 'fileDialog',
      accept: '.txt, .pdf, .ppt, .pptx, .doc, .docx, .xls, .xlsx',
      fileList: [],
    },
  ];

  const addEmoji = async (emj: string) => {
    const myField = ref<HTMLTextAreaElement | null>(null);
    let strTem = emj || '';
    const surplus = maxLength - answer.value.textContent.length;

    if (surplus - strTem.length < 0) {
      strTem = strTem.substring(0, surplus);
      warning(`消息内容长度不能超过${maxLength}字符，超出部分则被截断。`);
    }
    if (myField.value?.selectionStart || myField.value?.selectionStart === 0) {
      const startPos = myField.value.selectionStart;
      const endPos = myField.value.selectionEnd;
      answer.value.textContent =
        myField.value.value.substring(0, startPos) +
        strTem +
        myField.value.value.substring(endPos, myField.value.value.length);
      await nextTick();
      myField.value.focus();
      myField.value.setSelectionRange(endPos + strTem.length, endPos + strTem.length);
    } else {
      answer.value.textContent += strTem;
    }
    emojiShow.value = false;
  };

  const beforeUploadImg = (file: File, index?: number) => {
    if (file.name && file.name.substring(0, file.name.indexOf('.')).length > 15) {
      error('素材名称限制15个字');
      return Upload.LIST_IGNORE;
    }
    materialName.value = file.name;
    const isRightType = file.type === 'image/jpeg' || file.type === 'image/png';
    const isRightSize = file.size / 1024 / 1024 < 10;
    const name = file.name
      .substring(file.name.lastIndexOf('.') + 1, file.name.length)
      .toLocaleLowerCase();

    // 素材个数
    if (index == undefined && answer.value.materialList.length > props.maxMaterialLength - 1) {
      warning(`最多新增${props.maxMaterialLength}个不同类型的素材`);
      return Upload.LIST_IGNORE;
    }

    if (index === undefined && !isRightType) {
      error('图片仅支持jpg、png格式');
      return Upload.LIST_IGNORE;
    }
    if (!isRightSize) {
      error('图片大小不能超过10M');
      return Upload.LIST_IGNORE;
    }
    if (index !== undefined && name !== 'jpeg' && name !== 'jpg' && name !== 'png') {
      answer.value.materialList[index].uploadFail = true;
      answer.value.materialList[index].isUploading = false;
      error('图片仅支持jpg、png格式');
      return Upload.LIST_IGNORE;
    }
    if (index !== undefined && !isRightSize) {
      answer.value.materialList[index].uploadFail = true;
      answer.value.materialList[index].isUploading = false;
      error('图片大小不能超过10M');
      return Upload.LIST_IGNORE;
    }

    return true;
  };

  const beforeUploadVideo = (file: File, index?: number) => {
    if (file.name && file.name.substring(0, file.name.indexOf('.')).length > 15) {
      error('素材名称限制15个字');
      return Upload.LIST_IGNORE;
    }
    materialName.value = file.name;
    const isRightType = file.type === 'video/mp4';
    const isRightSize = file.size / 1024 / 1024 < 10;
    const name = file.name
      .substring(file.name.lastIndexOf('.') + 1, file.name.length)
      .toLocaleLowerCase();
    // 素材个数
    if (index == undefined && answer.value.materialList.length > props.maxMaterialLength - 1) {
      warning(`最多新增${props.maxMaterialLength}个不同类型的素材`);
      return Upload.LIST_IGNORE;
    }

    if (index === undefined && !isRightType) {
      error('视频仅支持mp4格式');
      return Upload.LIST_IGNORE;
    }
    if (!isRightSize) {
      error('视频大小不能超过10M');
      return Upload.LIST_IGNORE;
    }
    if (index !== undefined && name !== 'mp4') {
      answer.value.materialList[index].uploadFail = true;
      answer.value.materialList[index].isUploading = false;
      error('视频仅支持mp4格式');
      return Upload.LIST_IGNORE;
    }
    if (index !== undefined && !isRightSize) {
      answer.value.materialList[index].uploadFail = true;
      answer.value.materialList[index].isUploading = false;
      error('视频大小不能超过10M');
      return Upload.LIST_IGNORE;
    }

    return true;
  };

  const beforeUploadFile = (file: File, index?: number) => {
    materialName.value = file.name;
    const name = file.name
      .substring(file.name.lastIndexOf('.') + 1, file.name.length)
      .toLocaleLowerCase();
    const reg = /^txt$|^pdf$|^ppt$|^pptx$|^doc$|^docx$|^xls$|^xlsx$/;
    const isRightType = reg.test(name);
    const isRightSize = file.size / 1024 / 1024 < 20;
    // 素材个数
    if (index == undefined && answer.value.materialList.length > props.maxMaterialLength - 1) {
      warning(`最多新增${props.maxMaterialLength}个不同类型的素材`);
      return Upload.LIST_IGNORE;
    }
    if (index === undefined && !isRightType) {
      error('文件仅支持txt、pdf、ppt、pptx、doc、docx、xls、xlsx格式');
      return Upload.LIST_IGNORE;
    }
    if (!isRightSize) {
      error('文件大小不能超过20M');
      return Upload.LIST_IGNORE;
    }
    if (index !== undefined && !isRightType) {
      answer.value.materialList[index].uploadFail = true;
      answer.value.materialList[index].isUploading = false;
      error('文件仅支持txt、pdf、ppt、pptx、doc、docx、xls、xlsx格式');
      return Upload.LIST_IGNORE;
    }
    if (index !== undefined && !isRightSize) {
      answer.value.materialList[index].uploadFail = true;
      answer.value.materialList[index].isUploading = false;
      error('文件大小不能超过20M');
      return Upload.LIST_IGNORE;
    }

    return true;
  };
  const customRequestImg = (req: any, index: number) => {
    customRequest(req, index, 'image');
  };

  const customRequestVideo = (req: any, index: number) => {
    customRequest(req, index, 'video');
  };

  const customRequestFile = (req: any, index: number) => {
    customRequest(req, index, 'file');
  };

  const customRequest = async (req: any, index: number, type: string) => {
    answer.value.isUpload = true;
    const data = {
      file: req.file,
    };

    try {
      const res = await uploadFileApi(data);
      const obj: any = {
        msgtype: type,
        name: materialName.value,
      };
      if (type === 'image') {
        obj.pic_url = res.url;
      } else {
        obj.url = res.url;
      }
      if (index !== undefined) {
        answer.value.materialList.splice(index, 1, obj);
      } else {
        answer.value.materialList.push(obj);
      }
      req.onSuccess();
      answer.value.isUpload = false;
    } catch (e) {
      req.onError();
      answer.value.isUpload = false;
      console.log('上传校验失败');
    }
  };
  const setMaterial = (item: any) => {
    if (answer.value.materialList.length > props.maxMaterialLength - 1) {
      warning(`最多新增${props.maxMaterialLength}个不同类型的素材`);
      return;
    }

    if (['miniprogram', 'link'].includes(item.type)) {
      addDialogVisible.value = true;
      type.value = item.type;
      isEditMaterial.value = false;
    }
  };
  /** 消息素材预览 */
  const openPreview = (row) => {
    console.log('[ row ] >', row);
    if (row.msgtype == 'image') {
      createImgPreview({ imageList: row.pic_url.split() });
    } else if (row.msgtype == 'video') {
      videoUrl.value = row.url;
      is_open_video.value = true;
    }
  };
  /** 消息素材编辑 */
  const openEdit = (row, index) => {
    rowIndex.value = index;
    isEditMaterial.value = true;
    type.value = row.msgtype;
    rowData = row;
    addDialogVisible.value = true;
  };
  const deleteMaterial = (index: number) => {
    answer.value.materialList.splice(index, 1);
  };

  const updateMaterial = (val: any) => {
    addDialogVisible.value = val.flag;
    type.value = '';
    if (!val.data) {
      return;
    }
    let obj = val.data;
    if (isEditMaterial.value) {
      answer.value.materialList.splice(rowIndex.value!, 1, obj);
    } else {
      answer.value.materialList.push(obj);
    }
    rowIndex.value = null;
  };

  function findIndex(row) {
    return answer.value.materialList.findIndex((material) => {
      return JSON.stringify(row) === JSON.stringify(material);
    });
  }
</script>

<style lang="less" scoped>
  /** 消息内容 */
  .publish-content-box {
    width: 100%;
    border: solid 1px #dcdfe6;
    border-radius: 4px;

    /** 表情选择区域 */
    .select-box-insert {
      display: flex;
      align-items: center;
      height: 60px;
      margin: 0 15px;

      .add-emoji {
        display: inline-flex;
        justify-content: center;
        margin-right: 10px;

        &.disabled {
          cursor: not-allowed;
        }
      }
    }

    /** 文本区域 */
    :deep(.mini-link) {
      color: #1890ff;
      cursor: pointer;
    }

    .textarea-box {
      width: calc(100% - 30px) !important;
      height: auto;
      margin-left: 15px;
      padding: 15px 0;
      overflow-y: auto;
      border-top: solid 1px #ebeef5;
      font-family: PingFangSC-Medium, 'PingFang SC', 'Microsoft YaHei', sans-serif;

      &.disabled {
        cursor: not-allowed;
      }
    }
  }

  /** 消息素材 */
  .material-select-box {
    position: relative;
    box-sizing: border-box;
    min-height: 105px;
    padding-bottom: 15px;

    /** 素材类型图标+文字 区域 */
    .select-btn {
      box-sizing: border-box;
      width: 100%;
      background-color: #fff;
    }

    /** 已选素材个数 */
    .material-number {
      color: #ed6f6f;
    }

    /** 全部移除 */
    .material-remove-all {
      margin-left: 25px;
      color: #ed6f6f;
      font-size: 14px;
      font-weight: 600;
    }
  }

  /** 消息素材-操作 */
  .cell-operate {
    display: inline-flex;

    /** 消息素材列表-操作文字 */
    .operate-text {
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .select-material-list {
    display: flex;

    .item-select {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 60px;
      margin: 0 10px;
    }

    .item-tip {
      color: #606266;
      font-size: 12px;
      line-height: 17px;
    }
  }

  .img-icon {
    width: 38px;
    height: 38px;
    background: url('@/assets/images/material/material-img-icon.png') no-repeat;
    background-size: 100% 100%;

    &:hover {
      background: url('@/assets/images/material/material-img-hover-icon.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .link-icon {
    width: 38px;
    height: 38px;
    background: url('@/assets/images/material/material-link-icon.png') no-repeat;
    background-size: 100% 100%;

    &:hover {
      background: url('@/assets/images/material/material-link-hover-icon.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .mini-icon {
    width: 38px;
    height: 38px;
    background: url('@/assets/images/material/material-mini-icon.png') no-repeat;
    background-size: 100% 100%;

    &:hover {
      background: url('@/assets/images/material/material-mini-hover-icon.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .video-icon {
    width: 38px;
    height: 38px;
    background: url('@/assets/images/material/material-video-icon.png') no-repeat;
    background-size: 100% 100%;

    &:hover {
      background: url('@/assets/images/material/material-video-hover-icon.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .file-icon {
    width: 38px;
    height: 38px;
    background: url('@/assets/images/material/material-file-icon.png') no-repeat;
    background-size: 100% 100%;

    &:hover {
      background: url('@/assets/images/material/material-file-hover-icon.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  /** 必填字段前星号 */
  .required-symbol {
    margin-right: 3px;
    color: #ed6f6f;
  }

  .tips {
    margin: 5px 0;
    color: rgba(0 0 0 / 85%);
    font-size: 12px;
  }

  .insert-btn {
    display: flex;
    position: relative;
    align-items: center;

    .btn-img {
      width: 14px;
      height: 14px;
      margin-right: 6px;
      cursor: pointer;
    }
  }
</style>
