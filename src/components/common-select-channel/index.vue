<template>
  <a-modal
    v-model:open="dialogVisible"
    title="选择渠道"
    :width="1000"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @cancel="closeDialog"
  >
    <div>
      <a-form :model="selectForm" layout="inline">
        <a-form-item>
          <a-input-group compact :style="{ width: '360px' }">
            <a-select :style="{ width: '140px' }" v-model:value="queryStyleType">
              <a-select-option :value="1">渠道分组名称</a-select-option>
              <a-select-option :value="2">渠道名称</a-select-option>
            </a-select>
            <a-input
              :style="{ width: '220px' }"
              :placeholder="stylePlaceHolder"
              v-model:value="selectForm.queryStyleVal"
              allowClear
            />
          </a-input-group>
        </a-form-item>
        <a-form-item>
          <a-button class="search-button" type="primary" @click="handleSearch">查询</a-button>
          <a-button class="reset-btn m-l-16" @click="cleanList">重置</a-button>
        </a-form-item>
      </a-form>
      <div class="content">
        <div class="group-list">
          <div class="title">渠道分组</div>
          <div class="wrap">
            <div
              class="item"
              :class="{ on: currentGroup === group.id }"
              @click="groupChoose(group)"
              v-for="group in groupList"
              :key="group.id"
            >
              {{ group.name }}
            </div>
          </div>
        </div>
        <div class="channel-list">
          <div class="title">渠道</div>
          <div class="wrap">
            <div
              class="item"
              @click="channelChoose(channel)"
              v-for="channel in channelList"
              :key="channel.id"
            >
              <div class="name">{{ channel.name }}</div>
              <div :class="resultListIds.includes(channel.id) ? 'show' : 'hidden'">
                <CheckOutlined />
              </div>
            </div>
          </div>
        </div>
        <div class="result">
          <div class="title">已选渠道</div>
          <div class="wrap">
            <span class="item" v-for="channel in resultList" :key="channel.id">
              {{ channel.name }}
              <CloseOutlined @click="delItem(channel)" />
            </span>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <a-button class="close-btn" @click="closeDialog">取消</a-button>
      <a-button class="submit-btn" type="primary" @click="handleConfirm">确定</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, watch, computed } from 'vue';
  import { getGroupListApi, getChannelListApi } from '@/api/sys/components';
  import { CloseOutlined, CheckOutlined } from '@ant-design/icons-vue';

  interface Group {
    name: string;
    id: string;
  }

  interface Channel {
    name: string;
    id: string;
  }

  interface SelectForm {
    queryStyleVal: string;
  }
  const props = defineProps({
    visible: { type: Boolean },
    chose: { type: Array as PropType<Channel[]> },
  });
  const emits = defineEmits(['close', 'submit']);

  const dialogVisible = ref(props.visible);
  const selectForm = ref<SelectForm>({ queryStyleVal: '' });
  const queryStyleType = ref(1);
  const currentGroup = ref('');
  const groupList = ref<Group[]>([]);
  const channelList = ref<Channel[]>([]);
  const resultList = ref<Channel[]>([]);

  const loading = ref(false);

  watch(
    () => props.visible,
    (value) => {
      dialogVisible.value = value;
      if (value) {
        currentGroup.value = '';
        selectForm.value.queryStyleVal = '';
        queryStyleType.value = 1;
        resultList.value = props.chose ? JSON.parse(JSON.stringify(props.chose)) : [];
        cleanList();
      }
    },
  );

  const stylePlaceHolder = computed(() => {
    return queryStyleType.value === 1 ? '请输入渠道分组名称' : '请输入渠道名称';
  });

  const resultListIds = computed(() => {
    return resultList.value.map((item) => item.id);
  });

  const closeDialog = () => {
    emits('close');
  };

  const handleConfirm = () => {
    emits('submit', resultList.value);
  };

  const getGroupList = async () => {
    loading.value = true;
    const params = {
      searchType: queryStyleType.value,
      name: selectForm.value.queryStyleVal || '',
    };
    try {
      const res = await getGroupListApi(params);

      const data = res || [];
      groupList.value = data;
      if (data.length > 0) {
        groupChoose(data[0]);
      }
    } finally {
      loading.value = false;
    }
  };

  const groupChoose = (group: Group) => {
    currentGroup.value = group.id;

    channelList.value = [];
    getChannelList();
  };

  const getChannelList = async () => {
    loading.value = true;
    const params = {
      contactChannelGroupId: currentGroup.value,
      name: queryStyleType.value === 2 ? selectForm.value.queryStyleVal : '',
      // pageNum: channelMeta.value.pageNum,
      // pageSize: channelMeta.value.pageSize,
    };
    try {
      const res = await getChannelListApi(params);
      const data = res || [];
      channelList.value = [...data];
    } finally {
      loading.value = false;
    }
  };

  const channelChoose = (channel: Channel) => {
    if (resultListIds.value.includes(channel.id)) {
      resultList.value = resultList.value.filter((item) => item.id !== channel.id);
    } else {
      resultList.value.unshift(channel);
    }
  };

  const handleSearch = () => {
    groupList.value = [];
    channelList.value = [];
    getGroupList();
  };

  const cleanList = () => {
    queryStyleType.value = 1;
    selectForm.value.queryStyleVal = '';
    handleSearch();
  };

  const delItem = (channel: Channel) => {
    resultList.value = resultList.value.filter((item) => item.id !== channel.id);
  };

  // const handleChannelScroll = throttle((event: Event) => {
  //   const target = event.target as HTMLElement;
  //   const { scrollTop, clientHeight, scrollHeight } = target;
  //   if (scrollTop > 0 && scrollTop + clientHeight >= scrollHeight - 5) {
  //     loadMoreChannel();
  //   }
  // }, 500);
</script>

<style lang="scss" scoped>
  .m-l-16 {
    margin-left: 16px;
  }

  .content {
    display: grid;
    grid-template-columns: 200px 250px auto;

    .group-list {
      padding: 10px;
      border-right: 1px solid #eee;

      .wrap {
        height: 300px;
        overflow-y: auto;
      }

      .title {
        margin-bottom: 4px;
        font-weight: bold;
      }

      .item {
        padding-bottom: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
        user-select: none;

        &.on {
          color: $colorBlack1;
        }
      }
    }

    .channel-list {
      padding: 10px;
      border-right: 1px solid #eee;

      .wrap {
        height: 300px;
        overflow-y: auto;
      }

      .title {
        margin-bottom: 4px;
        font-weight: bold;
      }

      .item {
        display: flex;
        align-items: center;
        padding-bottom: 8px;
        cursor: pointer;
        user-select: none;

        .name {
          flex-grow: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .show {
          opacity: 1;
        }

        .hidden {
          opacity: 0;
        }

        .check {
          flex-shrink: 0;
          margin-left: 10px;
          color: $colorBlack1;
        }
      }
    }

    .result {
      padding: 10px;

      .wrap {
        height: 300px;
        overflow-y: auto;
      }

      .title {
        margin-bottom: 4px;
        font-weight: bold;
      }

      .item {
        display: inline-block;
        margin: 0 5px 5px 0;
        padding: 4px 10px;
        border: 1px solid #eee;
        border-radius: 4px;
        background: $colorBlack1;
        color: #fff;
        font-size: 12px;
        cursor: pointer;
        user-select: none;

        .color {
          color: #fff;
        }
      }
    }
  }
</style>
