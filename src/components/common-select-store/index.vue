<template>
  <a-modal
    :title="title"
    v-model:open="dialogVisible"
    width="1000px"
    :class="['select-person']"
    destroy-on-close
    closable
    @cancel="handleCancel"
  >
    <a-row class="content">
      <!-- 左侧门店 -->
      <a-col :span="14" class="left-content">
        <div class="box-left">
          <!-- 搜索框模块-->
          <a-input-group compact>
            <a-select
              class="select"
              v-model:value="searchInfo.checkedKey"
              @change="selectChangedFn"
            >
              <a-select-option
                v-for="item in searchInfo.codeType"
                :key="item.key"
                :value="item.key"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
            <a-input
              class="select-input"
              v-model:value="searchInfo.value"
              :placeholder="searchInfo.placeholder"
            />
          </a-input-group>

          <div class="search-btn m-l-16">
            <a-button class="search-button" @click="handleSearch" type="primary">查询</a-button>
            <a-button class="reset-btn m-l-16" @click="handleReset">重置</a-button>
          </div>
        </div>

        <div class="tree-box">
          <div class="scroll-tree">
            <a-table
              :row-key="(record) => record.wecomStoreId"
              :preserveSelectedRowKeys="true"
              :row-selection="{
                selectedRowKeys: selectedRowKeys,
                onSelect: onSelect,
                hideSelectAll: true,
              }"
              :dataSource="storeListFilter"
              :pagination="false"
            >
              <a-table-column
                key="wecomStoreId"
                title="StoreCode"
                dataIndex="wecomStoreId"
                min-width="120"
              />
              <a-table-column key="name" title="门店名称" dataIndex="name" min-width="120" />
            </a-table>
          </div>
        </div>
      </a-col>

      <!-- 右侧已选门店 -->
      <a-col :span="10" class="right-content">
        <!-- 提示文案 -->
        <div class="right-top">
          <div>
            <div class="right-title">已选</div>
          </div>
          <!-- 不加这个标签会导致按钮样式异常 -->
          <div>
            <a-button class="clear-all" :disabled="!(selectedRows.length > 0)" @click="clearRange"
              >清空</a-button
            >
          </div>
        </div>

        <!-- 右侧列表 -->
        <div class="right-persons">
          <div v-for="(item, index) in selectedRows" :key="item.wecomStoreId" class="person-item">
            <TeamOutlined class="store" alt="" />
            <div class="name">{{ item.name }}</div>
            <CloseOutlined class="del-icon" @click="removeSelect(index)" />
          </div>
        </div>
      </a-col>
    </a-row>

    <template #footer>
      <a-button class="close-btn" @click="handleCancel">取 消</a-button>
      <a-button class="submit-btn" type="primary" @click="handleSubmit">确 定</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch, nextTick } from 'vue';
  import { cloneDeep } from 'lodash-es';
  import { storeListApi } from '@/api/sys/components';
  import { CloseOutlined, TeamOutlined } from '@ant-design/icons-vue';

  interface Store {
    address: string;
    brandId: string;
    cityId: string;
    createdBy: string;
    createdDate: string;
    deletedBy: string;
    deletedDate: string | null;
    id: string;
    latitude: string;
    longitude: string;
    modifiedBy: string;
    modifiedDate: string;
    name: string;
    openingTime: string;
    phone: string;
    thirdId: string;
    wecomStoreId: string;
    wecomStoreN: string;
  }

  interface SearchType {
    key: number;
    name: string;
  }

  const searchType: SearchType[] = [
    { key: 1, name: 'StoreCode' },
    { key: 2, name: '门店名称' },
  ];

  interface SearchInfo {
    codeType: SearchType[];
    value: string;
    checkedKey: number;
    placeholder: string;
  }

  const props = withDefaults(
    defineProps<{
      visible: boolean;
      selectedData: Store[];
      title?: string;
    }>(),
    {
      visible: false,
      selectedData: () => [],
      title: '门店选择',
    },
  );

  const emit = defineEmits(['close', 'submit']);

  const searchInfo = reactive<SearchInfo>({
    codeType: searchType,
    value: '',
    checkedKey: 1,
    placeholder: '请输入StoreCode',
  });

  const filterStoreCode = ref('');
  const filterStoreName = ref('');
  const storeList = ref<Store[]>([]);
  const selectedRows = ref<Store[]>([]);
  // 可修改的 visible
  const dialogVisible = ref(props.visible);
  // 已勾选门店 wecomStoreId 集合
  const selectedRowKeys = computed(() => {
    return selectedRows.value.map((item) => item.wecomStoreId);
  });

  const storeListFilter = computed(() => {
    if (filterStoreCode.value || filterStoreName.value) {
      if (filterStoreCode.value) {
        return storeList.value.filter((store) =>
          store.wecomStoreId.includes(filterStoreCode.value),
        );
      }
      if (filterStoreName.value) {
        return storeList.value.filter((store) => store.name.includes(filterStoreName.value));
      }
    }
    console.log('storeListFilter', storeList.value);
    return storeList.value;
  });

  watch(() => props.visible, changeVisible);
  function changeVisible(visible) {
    dialogVisible.value = visible;
    if (visible) {
      selectedRows.value = cloneDeep(props.selectedData);
      nextTick(() => {
        handleInit();
      });
    }
  }
  /**
   * 门店勾选 取消，维护selectedRows
   */
  const onSelect = (record, selected) => {
    console.log(record, selected);
    if (selected) {
      selectedRows.value.push(record);
    } else {
      const tempIndex = selectedRows.value.findIndex(
        (item) => item.wecomStoreId === record.wecomStoreId,
      );
      selectedRows.value.splice(tempIndex, 1);
    }
  };
  function selectChangedFn() {
    const checkedItem = searchInfo.codeType.find((item) => item.key === searchInfo.checkedKey);
    if (checkedItem) {
      searchInfo.placeholder = '请输入' + checkedItem.name;
    }
  }

  async function handleInit() {
    storeList.value = [];
    const storeListResponse = await getDp({});
    if (storeListResponse) {
      storeList.value = storeListResponse;
    }
  }

  function handleSearch() {
    if (searchInfo.checkedKey === 1) {
      filterStoreCode.value = searchInfo.value;
      filterStoreName.value = '';
    }
    if (searchInfo.checkedKey === 2) {
      filterStoreName.value = searchInfo.value;
      filterStoreCode.value = '';
    }
  }

  function handleReset() {
    searchInfo.checkedKey = 1;
    searchInfo.value = '';
    handleSearch();
    handleInit();
  }

  function removeSelect(index: number) {
    selectedRows.value.splice(index, 1);
  }

  function clearRange() {
    selectedRows.value = [];
  }

  async function getDp(params: Record<string, any>) {
    try {
      const res = await storeListApi(params);
      return res || [];
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  function handleSubmit() {
    if (!selectedRows.value.length) {
      emit('submit', []);
    } else {
      emit('submit', cloneDeep(selectedRows.value));
    }
  }

  function handleCancel() {
    selectedRows.value = [];
    emit('close');
  }
</script>

<style lang="scss" scoped>
  $color-main: #000;
  $color-text-second: #909399; // 提示文字
  $color-disable: #c0c4cc; // 置灰色
  $color-text-normal: #606266; // 常规文字
  $color-third-line: #ebeef5; // 三级边框颜色
  $color-background: #f5f6fa; // 背景色

  ::v-deep {
    .#{$prefix}-table-wrapper .#{$prefix}-table-tbody > tr.#{$prefix}-table-row-selected > td {
      background-color: #fff;
    }
  }

  .m-l-16 {
    margin-left: 16px;
  }

  .select-person {
    .select {
      width: 110px !important;
    }

    .select-input {
      width: 280px;
    }

    .content {
      min-height: 200px;

      .left-content {
        padding-right: 15px;

        .search-btn {
          flex-shrink: 0;
        }

        .search-item {
          cursor: pointer;
        }

        .box-left {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;
        }

        .tree-box {
          height: 390px;
          overflow: hidden;
          overflow-y: auto;
        }

        .scroll-tree {
          width: 100%;
          min-height: 100%;
        }
      }

      .right-content {
        padding-left: 10px;
        border-left: 1px solid $color-third-line;

        .right-top {
          display: flex;
          justify-content: space-between;
        }

        .right-persons {
          height: 418px;
          padding-right: 10px;
          overflow: hidden;
          overflow-y: auto;

          .right-title {
            margin: 15px 0 10px;
            font-weight: bold;
          }

          .person-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 30px;
            line-height: 30px;

            .store {
              flex-shrink: 0;
              width: 13px;
              height: 13px;
            }

            .name {
              flex-grow: 1;
              padding-left: 5px;
              color: $color-text-normal;
              font-size: 14px;
            }

            .del-icon {
              flex-shrink: 0;
              color: $color-main;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
</style>
