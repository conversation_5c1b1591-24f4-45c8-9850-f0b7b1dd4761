<template>
  <div class="person">
    <!-- 用户icon -->
    <UserOutlined class="member-img" v-if="!data.is_dp && data.selectable" />
    <img
      class="member-img"
      v-if="!data.is_dp && !data.selectable"
      src="@/assets/images/member_dark.png"
      alt=""
    />
    <!-- 部门icon -->
    <TeamOutlined class="group-img" v-if="data.is_dp" />

    <!-- 员工名称/部门名称 -->
    <span class="name text-color">{{ data.name || data.departmentName }}</span>

    <!-- 员工id -->
    <span v-if="!data.is_dp" class="user-id">({{ data.userId }})</span>
    <!-- 选中 判断是否选中 -->
    <CheckOutlined class="del-icon" v-if="!del && selected" />
    <!-- 右侧删除图标 -->
    <CloseOutlined v-if="del" class="del-icon" @click="handleClick" />
  </div>
</template>

<script setup lang="ts">
  import { defineProps, defineEmits } from 'vue';
  import { CheckOutlined, CloseOutlined, UserOutlined, TeamOutlined } from '@ant-design/icons-vue';

  defineProps({
    data: {
      type: Object,
      required: true,
    },
    selected: {
      type: Boolean,
      default: false,
    },
    del: {
      type: Boolean,
      default: false,
    },
  });

  const emits = defineEmits(['handleClick']);

  const handleClick = () => {
    emits('handleClick');
  };
</script>

<style scoped lang="less">
  @color-text-second: #909399; // 提示文字
  @color-main: #000;
  @color-disable: #cacaca; // 置灰色
  @color-text-normal: #000; // 常规文字
  @color-third-line: #ebeef5; // 三级边框颜

  .person {
    position: relative;
    padding-right: 24px;
    line-height: 30px;

    .name {
      display: inline;
      padding-left: 5px;
    }

    .user-id {
      display: inline;
      padding-left: 3px;
      color: @color-text-second;
    }

    .icon-color {
      color: @color-main;
    }

    .text-color {
      color: @color-text-normal;
    }

    .member-img {
      position: relative;
      width: 14px;
      height: 14px;
      vertical-align: middle;
    }

    .group-img {
      position: relative;
      width: 14px;
      height: 14px;
      vertical-align: baseline;
    }

    .del-icon {
      position: absolute;
      top: 50%;
      right: 6px;
      transform: translateY(-50%);
      color: @color-main;
      cursor: pointer;
    }
  }

  .disabled-person {
    .icon-color {
      color: @color-disable;
    }

    .text-color {
      color: @color-text-normal;
    }
  }
</style>
