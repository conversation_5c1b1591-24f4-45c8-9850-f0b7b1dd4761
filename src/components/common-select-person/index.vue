<template>
  <a-modal
    v-model:open="dialogVisible"
    class="select-person"
    :title="title"
    width="1000px"
    @cancel="handleCancel"
    :destroyOnClose="true"
    :maskClosable="false"
    :forceRender="true"
  >
    <a-row class="content">
      <!-- 左侧员工树 -->
      <a-col :span="14" class="left-content">
        <div class="box-left">
          <!-- 搜索框模块-->
          <a-input-group compact :style="{ width: '390px' }">
            <a-select
              class="select"
              v-model:value="searchInfo.checkedKey"
              @change="selectChangedFn"
            >
              <a-select-option
                v-for="item in searchInfo.codeType"
                :key="item.key"
                :value="item.key"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
            <a-input
              class="select-input"
              v-model:value="searchInfo.value"
              :placeholder="searchInfo.placeholder"
              allowClear
              @press-enter="handleSearch"
            />
          </a-input-group>
          <div class="search-btn m-l-16">
            <a-button class="search-button" type="primary" @click="handleSearch">查询</a-button>
            <a-button class="reset-btn m-l-16" @click="handleReset">重置</a-button>
          </div>
        </div>

        <div class="tree-box" v-if="dialogVisible">
          <div class="scroll-tree">
            <div v-if="!searchInfo.show">
              <a-tree
                :load-data="loadNode"
                :block-node="true"
                :tree-data="treeData"
                :field-names="fieldNames"
                @select="handleNodeClick"
              >
                <template #title="{ key, dataRef, nodeSelected }">
                  <person :data="dataRef" :key="key" :selected="nodeSelected" />
                </template>
              </a-tree>
            </div>
            <!-- 搜索结果页 -->
            <div
              v-show="searchInfo.show"
              v-for="(item, index) in searchList"
              :key="index"
              class="search-item"
              @click="selectSearchItem(item)"
            >
              <person :data="item" :key="key" :selected="item.nodeSelected" :del="false" />
            </div>
          </div>
        </div>
      </a-col>

      <!-- 右侧已选员工 -->
      <a-col :span="10" class="right-content">
        <!-- 提示文案 -->
        <div class="right-top">
          <div>
            <div class="right-title">已选择的部门或成员</div>
            <div class="right-tip">（保存时 会自动合并已选部门下成员）</div>
          </div>
          <!-- 不加这个标签会导致按钮样式异常 -->
          <div>
            <a-button
              class="clear-all"
              :disabled="!(selectMember.length > 0 || selectDept.length > 0)"
              @click="clearMemberRange"
              >清空</a-button
            >
          </div>
        </div>

        <!-- 右侧列表 -->
        <div class="right-persons">
          <div class="right-title" v-if="selectDept.length > 0">部门：</div>
          <div v-for="(item, index) in selectDept" :key="item.id" class="person-item">
            <person
              :data="{ ...item, selectable: true }"
              :del="true"
              @handle-click="delNode(item, index)"
            />
          </div>
          <div class="right-title" v-if="selectMember.length > 0">人员：</div>
          <div v-for="(item, index) in selectMember" :key="item.id" class="person-item">
            <person
              :data="{ ...item, selectable: true }"
              :del="true"
              @handle-click="delNode(item, index)"
            />
          </div>
        </div>
      </a-col>
    </a-row>
    <template #footer>
      <a-button class="close-btn" @click="handleCancel">取消</a-button>
      <a-button class="submit-btn m-l-16" type="primary" @click="handleSubmit">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import { flatten, cloneDeep } from 'lodash-es';
  import Person from './person.vue';
  import { getEmpListApi, getDeptApi, transEmpListApi } from '@/api/sys/components';
  import { useMessage } from '@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const { warning } = createMessage;
  /**搜索类型 */
  const SearchType = [
    { key: 1, name: '员工姓名' },
    { key: 2, name: '部门名称' },
    { key: 3, name: 'UserID' },
  ];
  const emits = defineEmits(['close', 'submit']);
  // props
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    selectedData: {
      type: Object,
      default: () => ({ member: [], dept: [] }),
    },
    title: {
      type: String,
      default: '添加员工（支持多选）',
    },
  });

  // state
  const dialogVisible = ref(props.visible);
  const fieldNames = ref({
    title: 'name',
  });
  const searchInfo = ref({
    show: false,
    codeType: SearchType,
    value: '',
    checkedKey: 1,
    placeholder: '请输入员工姓名',
  });
  // 选中的人员
  const selectMember = ref([]);
  // 选中的部门
  const selectDept = ref([]);
  // 搜索结果
  const searchList = ref([]);

  // 树节点数据
  const treeData = ref([]);
  // 扁平化后的树结点
  const flatTreeData = computed(() => {
    const array = [];
    function flatArray(arr) {
      arr.map((item) => {
        if (Array.isArray(item.children)) {
          flatArray(item.children);
        } else {
          array.push(item);
        }
      });
    }
    if (Array.isArray(treeData.value)) {
      flatArray(treeData.value);
      return array;
    } else {
      return [];
    }
  });
  const rootId = ref('');
  // computed
  const selectMemberIds = computed(() => {
    const idsArray = [[], []];
    const [dpIds, personIds] = idsArray;

    selectMember.value.forEach((item) => personIds.push(item.id));
    selectDept.value.forEach((item) => dpIds.push(item.id));

    return idsArray;
  });

  // watch
  watch(() => props.visible, changeVisible);

  // methods
  async function changeVisible(visible) {
    dialogVisible.value = visible;
    if (visible) {
      const selectMemberData = props.selectedData.member.map((member) => ({
        is_dp: member.dp || false,
        ...member,
      }));
      console.log(selectMemberData, 'changeVisible');
      const selectDeptData = props.selectedData.dept;

      selectMember.value = cloneDeep(selectMemberData);
      selectDept.value = cloneDeep(selectDeptData);
      searchList.value = [];
      handleReset();
    }
  }

  function selectChangedFn() {
    const checkedItem = searchInfo.value.codeType.find(
      (item) => item.key === searchInfo.value.checkedKey,
    );
    searchInfo.value.placeholder = '请输入' + checkedItem.name;
  }

  async function handleSearch() {
    searchInfo.value.show = true;
    // 扁平化ids
    const ids = flatten(selectMemberIds.value);
    console.log('handleSearch', ids);
    if (searchInfo.value.checkedKey === 2) {
      // 搜索部门
      const res = await getDp({ departmentName: searchInfo.value.value });
      searchList.value = res.map((item) => ({
        ...item,
        nodeSelected: ids.indexOf(item.TODO) !== -1,
      }));
    } else {
      // 搜索员工
      try {
        const list = await getPeople(-1);
        searchList.value = list.map((item) => ({
          is_dp: false,
          name: item.name,
          id: item.id,
          userId: item.userid,
          isLeaf: true,
          selectable: item.status === 1,
          nodeSelected: ids.indexOf(item.id) !== -1,
        }));
      } catch (e) {
        searchList.value = [];
      }
    }
  }

  async function handleReset() {
    treeData.value = [];
    initTreeRoot();
    searchInfo.value.checkedKey = 1;
    searchInfo.value.value = '';
    searchInfo.value.show = false;
  }
  /**
   * 搜索列表选中
   * @param item
   */
  function selectSearchItem(item) {
    const { nodeSelected, selectable } = item;
    if (!selectable) return warning(`员工${item.name}还未加入企业, 请重新选择`);

    if (nodeSelected) {
      // 取消选中
      delNode(item);
      item.nodeSelected = false;
    } else {
      // 设置选中
      addNode(item);
      item.nodeSelected = true;
    }
  }
  /**
   * 批量清空
   */
  async function clearMemberRange() {
    // 基于响应式先取消部门选中
    selectDept.value.forEach((item) => {
      item.nodeSelected = false;
    });

    // 清空右边已选
    selectMember.value = [];
    selectDept.value = [];

    if (searchInfo.value.show) {
      // 处理搜索列表
      searchList.value.forEach((item) => {
        item.nodeSelected = false;
      });
    } else {
      // 处理左侧Tree
      flatTreeData.value.forEach((item) => {
        item.nodeSelected = false;
      });
    }
  }

  /**
   * 单个移除
   * @param data
   * @param index
   */
  function delNode(data, index) {
    data.nodeSelected = false;
    // 定义一个变量来存储选择的数组，根据 data.is_dp 决定是部门还是成员
    const selectedArray = data.is_dp ? selectDept.value : selectMember.value;
    if (!index) {
      index = selectedArray.findIndex((item) => item.id === data.id);
      selectedArray.splice(index, 1);
    } else {
      selectedArray.splice(index, 1);
    }
    // 同步处理左侧处理搜索列表
    if (searchInfo.value.show) {
      searchList.value.forEach((item) => {
        if (item.id === data.id) {
          item.nodeSelected = false;
        }
      });
    } else {
      const cancelNode = flatTreeData.value.find((item) => item.id === data.id);
      cancelNode && (cancelNode.nodeSelected = false);
    }
  }
  /**
   *
   * @param data
   */
  function addNode(data) {
    if (data.is_dp) {
      selectDept.value.push(data);
    } else {
      selectMember.value.push(data);
    }
  }
  /**
   * 选中节点
   * @param selectedKeys
   * @param e
   */
  function handleNodeClick(selectedKeys, e) {
    const { node } = e;
    if (!node.selectable) return warning(`员工${node.name}还未加入企业, 请重新选择`);
    /** 操作后的选中状态 */
    const nodeSelected = (node.dataRef.nodeSelected = node.nodeSelected = !node.nodeSelected);
    if (nodeSelected) {
      // 设置选中
      addNode(node.dataRef);
    } else {
      // 取消选中
      delNode(node.dataRef);
    }
  }

  async function getDp(params) {
    try {
      //  扁平化ids
      const ids = flatten(selectMemberIds.value);
      const res = await getDeptApi(params);
      res.forEach((item) => {
        item.is_dp = true;
        item.name = item.departmentName;
        item.isLeaf = false;
        item.selectable = true;
        item.nodeSelected = ids.indexOf(item.id) !== -1;
      });
      return res;
    } catch (error) {
      console.error(error);
    }
  }

  async function getPeople(id) {
    try {
      // 组装请求参数
      const params = {
        departmentId: id,
      };
      // 添加搜索情况
      if (id === -1) {
        //无参数，不调用接口，否则接口会报错
        if (!searchInfo.value.value) {
          handleReset();
          return;
        }
        delete params.departmentId;
        if (searchInfo.value.checkedKey === 1) {
          params.name = searchInfo.value.value;
        } else {
          params.userid = searchInfo.value.value;
        }
      }
      const res = await getEmpListApi(params);
      return res || [];
    } catch (e) {
      console.error(e);
      return [];
    }
  }
  function loadNode(treeNode) {
    return new Promise((resolve) => {
      if (treeNode.dataRef.children) {
        //子节点中有人员存在，不需要查部门下的人
        const findMember = treeNode.dataRef.children.some((item) => {
          return !item.is_dp;
        });
        if (findMember) {
          resolve();
          return;
        }
      }
      // 节点
      getPeople(treeNode.id)
        .then((memberList) => {
          // 扁平化ids
          const ids = flatten(selectMemberIds.value);
          // 循环插入员工
          const newMemberList = memberList.map((item) => ({
            is_dp: false,
            name: item.name,
            id: item.id,
            userId: item.userid,
            isLeaf: true,
            selectable: item.status === 1,
            nodeSelected: ids.indexOf(item.id) !== -1,
          }));
          if (newMemberList && newMemberList.length > 0) {
            treeNode.dataRef.children = treeNode.dataRef.children.concat(newMemberList);
          } else if (treeNode.dataRef.children.length === 0) {
            treeNode.dataRef.isLeaf = true;
          }
          treeData.value = [...treeData.value];
          resolve();
        })
        .catch(() => {
          resolve([]);
        });
    });
  }
  /** 查询所有部门 */
  async function initTreeRoot() {
    try {
      //  扁平化ids
      const ids = flatten(selectMemberIds.value);
      const res = await getDeptApi();
      res.forEach((item) => {
        item.name = item.departmentName;
        item.selectable = true;
        item.is_dp = true;
        item.isLeaf = false;
        item.nodeSelected = ids.indexOf(item.id) !== -1;
      });
      treeData.value = deptDataHandler(rootId.value, res, []);
    } catch (e) {
      console.error(e);
    }
  }
  /**部门数据处理 */
  function deptDataHandler(parentId, arr, newTree) {
    // 遍历输入的数组
    for (let i = arr.length - 1; i >= 0; i--) {
      // 如果当前元素的 parentId 与输入的 parentId 相等
      if (arr[i].parentId === parentId) {
        // 将当前元素添加到结果数组中
        newTree.push(arr[i]);
        // 从原数组中移除当前元素
        arr.splice(i, 1);
      }
    }

    // 对结果数组中的每个元素进行处理
    newTree.map((r) => {
      // 为当前元素添加一个空数组作为children属性
      r.children = [];
      // 递归调用deptDataHandler函数，处理当前元素的children
      deptDataHandler(r.id, arr, r.children);
    });
    // 返回结果数组
    return newTree;
  }

  async function handleSubmit() {
    // 处理选中为空的情况
    if (!selectMember.value.length && !selectDept.value.length) {
      return emits('submit', {
        list: [],
        rawData: {
          member: [],
          dept: [],
        },
      });
    }

    try {
      // 部门， 人员信息转人员
      const res = await transEmpListApi({
        dpIds: selectMemberIds.value[0],
        employeeIds: selectMemberIds.value[1],
      });

      // 重新组合数据
      const data = res || [];

      emits('submit', {
        list: data.map((item) => {
          return {
            id: item.useridAes || item.userId,
            name: item.name,
          };
        }),
        rawData: { member: selectMember.value, dept: selectDept.value },
      });
    } catch (e) {
      console.error(e);
    }
  }

  function handleCancel() {
    treeData.value = [];
    selectMember.value = [];
    selectDept.value = [];
    searchList.value = [];
    searchInfo.value.value = '';
    searchInfo.value.checkedKey = 1;
    emits('close');
  }
</script>

<style lang="scss" scoped>
  $color-text-second: #909399; // 提示文字
  $color-disable: #c0c4cc; // 置灰色
  $color-text-normal: #606266; // 常规文字
  $color-third-line: #ebeef5; // 三级边框颜色
  $color-background: #f5f6fa; // 背景色

  .m-l-16 {
    margin-left: 16px;
  }

  .select {
    width: 110px !important;
  }

  .select-input {
    width: 280px;
  }

  ::v-deep {
    .#{$prefix}-tree .#{$prefix}-tree-node-content-wrapper.#{$prefix}-tree-node-selected {
      background: #fff;
    }
    .#{$prefix}-tree.#{$prefix}-tree-block-node
      .#{$prefix}-tree-list-holder-inner
      .#{$prefix}-tree-node-content-wrapper,
    .tree-node-content-wrapper {
      flex-shrink: 0;
      white-space: nowrap;
    }
  }

  .select-person {
    ::v-deep {
      .#{$prefix}-tree-node {
        margin: 2px 0;
      }
    }

    .content {
      min-height: 200px;

      .left-content {
        padding-right: 15px;

        .search-btn {
          flex-shrink: 0;
        }

        .search-item {
          cursor: pointer;
        }

        .box-left {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;
        }

        .tree-box {
          height: 390px;
          overflow: auto;
        }

        .scroll-tree {
          width: 100%;
          min-height: 100%;
        }
      }

      .right-content {
        padding-left: 10px;
        border-left: 1px solid $color-third-line;

        .right-top {
          display: flex;
          justify-content: space-between;
        }

        .right-tip {
          margin-bottom: 5px;
          color: #ed6f6f;
          font-size: 12px;
        }

        .right-persons {
          height: 418px;
          overflow: hidden;
          overflow-y: auto;

          .person-item {
            padding: 0 5px;
          }

          .right-title {
            margin: 15px 0 10px;
            font-weight: bold;
          }
        }
      }
    }
  }
</style>
