<template>
  <a-config-provider
    :prefix-cls="prefixCls"
    :theme="customThemeRef"
    :autoInsertSpaceInButton="false"
    :locale="locale"
  >
    <RouterView v-slot="{ Component, route }">
      <keep-alive>
        <component :is="wrap(route, Component)" :key="route.fullPath" />
      </keep-alive>
    </RouterView>
  </a-config-provider>
</template>
<script setup lang="ts">
  import { RouterView, type RouteLocationNormalizedLoaded } from 'vue-router';
  import customTheme from './theme';
  import { ref, h, type Component } from 'vue';
  import { useLocale } from '@/locales/useLocale';

  const locale = useLocale().getAntdLocale;

  const customThemeRef = ref(customTheme);

  const prefixCls = import.meta.env.VITE_PREFIX;

  const wrapperMap = new Map();
  const wrap = (route: RouteLocationNormalizedLoaded, component: Component) => {
    const wrapperName = route.fullPath;

    if (wrapperName === '/') {
      return component;
    }

    let wrapper;
    if (wrapperMap.has(wrapperName)) {
      wrapper = wrapperMap.get(wrapperName);
    } else {
      wrapper = {
        name: wrapperName,
        render() {
          return h(component);
        },
      };
      wrapperMap.set(wrapperName, wrapper);
    }

    return h(wrapper);
  };
</script>
