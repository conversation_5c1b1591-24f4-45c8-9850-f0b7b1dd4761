import { describe, it, expect, vi, beforeEach } from 'vitest';

import { setupStore } from '@/stores';
import { setupI18n } from '@/locales/setupI18n';
import { setupGlobDirectives } from '@/directives';
import { ConfigProvider } from 'ant-design-vue';
import Antd from 'ant-design-vue';
import './main';

vi.mock('vite-plugin-qiankun/dist/helper', () => ({
  renderWithQiankun: vi.fn().mockImplementation(async ({ bootstrap, mount }) => {
    bootstrap();
    const props = {
      container: {
        querySelector: vi.fn().mockReturnValue('#app'),
      },
      isInPortal: vi.fn().mockReturnValue(false),
    };
    await mount(props);
  }),
  qiankunWindow: {
    __POWERED_BY_QIANKUN__: true,
  },
}));

vi.mock('@/stores', () => ({
  setupStore: vi.fn(),
}));

vi.mock('@/locales/setupI18n', () => ({
  setupI18n: vi.fn(),
}));

vi.mock('@/router', () => ({
  setupRouter: vi.fn(),
}));

vi.mock('@/directives', () => ({
  setupGlobDirectives: vi.fn(),
}));

vi.mock('ant-design-vue', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    ConfigProvider: vi.fn(),
    Antd: {
      install: vi.fn(),
    },
  };
});

describe('mount function', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  it('should mount the app correctly with qiankun props', async () => {
    localStorage.setItem('accessToken', 'testToken');
    localStorage.setItem('tenantId', 'testTenantId');

    window[import.meta.env.VITE_PREFIX].login();
    window[import.meta.env.VITE_PREFIX].logout();

    expect(setupStore).toHaveBeenCalled();
    expect(setupI18n).toHaveBeenCalled();
    expect(setupGlobDirectives).toHaveBeenCalled();

    expect(ConfigProvider).toHaveBeenCalled();
  });
});
