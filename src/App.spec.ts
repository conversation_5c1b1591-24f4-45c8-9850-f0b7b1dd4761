import { mount, VueWrapper } from '@vue/test-utils';
import App from '@/App.vue';
import { useLocale } from '@/locales/useLocale';
import { ref, type VNode } from 'vue';
import { describe, beforeEach, it, expect, vi } from 'vitest';

vi.mock('@/locales/useLocale', () => ({
  useLocale: vi.fn(),
}));

type AppWrapper = VueWrapper & {
  vm: {
    wrap: (route: { fullPath: string }, component: any) => VNode;
  };
};

describe('App.vue', () => {
  beforeEach(() => {
    (useLocale as any).mockReturnValue({
      getAntdLocale: ref({}),
    });
  });

  it('renders correctly', () => {
    const wrapper = mount(App, {
      global: {
        stubs: {
          RouterView: true,
          'a-config-provider': true,
        },
      },
    });
    expect(wrapper.exists()).toBe(true);
  });

  it('wraps component correctly for a new route', () => {
    const wrapper = mount(App, {
      global: {
        stubs: {
          RouterView: true,
          'a-config-provider': true,
        },
      },
    }) as AppWrapper;

    const route = { fullPath: '/test' };
    const component = { template: '<div>Test Component</div>' };
    const wrappedComponent = wrapper.vm.wrap(route, component);
    const renderedComponent = mount({
      template: '<component :is="wrappedComponent"></component>',
      data() {
        return { wrappedComponent };
      },
    });

    // console.log(999, wrappedComponent);
    expect(renderedComponent.html()).toBe(component.template);
    // expect(wrappedComponent.type.name).toBe('/test');
    // expect(typeof wrappedComponent.type.render).toBe('function');
  });

  it('reuses wrapper for the same route', () => {
    const wrapper = mount(App, {
      global: {
        stubs: {
          RouterView: true,
          'a-config-provider': true,
        },
      },
    }) as AppWrapper;

    const route = { fullPath: '/test' };
    const component = { template: '<div>Test Component</div>' };
    const wrappedComponent1 = wrapper.vm.wrap(route, component);
    const wrappedComponent2 = wrapper.vm.wrap(route, component);

    expect(wrappedComponent1).toStrictEqual(wrappedComponent2);
  });
});
