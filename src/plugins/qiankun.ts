import type { App } from 'vue';
import {
  type RouteLocationAsPathGeneric,
  type RouteLocationAsRelativeGeneric,
  type Router,
} from 'vue-router';

type customRouter = string | RouteLocationAsRelativeGeneric | RouteLocationAsPathGeneric;

export const setupQiankun = async (app: App, props: any, router: Router | customRouter[]) => {
  const qiankunProps = props || {};

  if (!props?.isInPortal?.()) {
    // 独立运行时的重载登陆和退出
    qiankunProps.login = async () => {
      console.log('login');
    };

    qiankunProps.logout = async () => {
      console.log('logout');
    };

    qiankunProps.getLang = async () => {
      return 'zh-CN';
    };
  }
  // 路由跳转
  if (!qiankunProps.jumpTo) {
    qiankunProps.jumpTo = async (to: customRouter) => {
      to && (await (router as Router).push(to));
    };

    qiankunProps.replaceTo = async (to: customRouter) => {
      to && (await (router as Router).replace(to));
    };
  }

  const language = await qiankunProps.getLang();

  const _props = {
    ...qiankunProps,
  };
  _props.language = language;

  window[import.meta.env.VITE_PREFIX] = _props || {};

  app.provide('portalProps', qiankunProps);
};
