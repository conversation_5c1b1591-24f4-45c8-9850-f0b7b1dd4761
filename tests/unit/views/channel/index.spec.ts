import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/channel/list.vue';

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

import { DeleteOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  Select,
  Spin,
  Switch,
  Table,
} from 'ant-design-vue';

import { sleep } from '@/utils';
import {
  createChannel<PERSON><PERSON>,
  createChannelGroupApi,
  deleteChannelApi,
  deleteChannelGroupApi,
  exportChannelApi,
  getChannelGroupsApi,
  getChannelListApi,
  updateChannelApi,
} from '@/api/sys/channel';

const REQ_TIME = 10;
const components = {
  'a-form': Form,
  'a-form-item': Form.Item,
  'a-input': Input,
  'a-input-group': InputGroup,
  'a-select': Select,
  'a-select-option': Select.Option,
  'a-checkbox': Checkbox,
  'a-switch': Switch,
  'a-button': Button,
  'a-table': Table,
  'a-spin': Spin,
  'a-empty': Empty,
  'a-modal': Modal,
  'a-popover': Popover,
  DeleteOutlined,
};

// 创建一个模拟函数
const mockJumpTo = vi.fn();
const prefix = import.meta.env.VITE_PREFIX;
// 将模拟函数赋值给 window[prefix].jumpTo
window[prefix] = {
  jumpTo: mockJumpTo,
};

// 模拟 ant-design-vue Modal
vi.mock('ant-design-vue', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    Modal: {
      ...actual.Modal,
      confirm: ({ onOk }) => {
        onOk();
      },
    },
  };
});

vi.mock('@/api/sys/channel', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(REQ_TIME).then(() => res));

  return {
    deleteChannelGroupApi: httpMock({ code: 200 }),
    createChannelGroupApi: httpMock(),
    updateChannelApi: httpMock(),
    createChannelApi: httpMock(),
    deleteChannelApi: httpMock(),
    exportChannelApi: httpMock({ url: '11' }),
    getChannelListApi: httpMock([{ id: 1, name: 'test' }]),
    getChannelGroupsApi: httpMock([
      { id: 1, name: 'test1', channelList: [] },
      { id: 2, name: 'test2', channelList: [] },
      { id: 3, name: 'test3', channelList: [] },
      { id: 4, name: 'test4', channelList: [] },
    ]),
  };
});

describe('channel/list.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;

  beforeEach(async () => {
    wrapper = mount(Component, {
      components,
    }) as VueWrapper<typeof Component>;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试组件是否被正确渲染
  it('component render', async () => {
    // 测试组件是否被正确渲染
    expect(wrapper.exists()).toBe(true);
  });

  // 测试 搜索按钮
  it('search submit', async () => {
    const buttons = wrapper.findAll('.ant-btn');

    // 断言 按钮是否存在
    expect(buttons[1].text()).toBe('搜 索');

    const selects = wrapper.findAllComponents(Select);
    // 断言 selects 数量
    expect(selects.length).toBe(1);
    // 设置 searchParams.searchType 值
    selects[0].vm.$emit('update:value', 1);
    // 断言 searchParams.searchType 值
    expect(wrapper.vm.searchParams.searchType).toBe(1);

    const inputs = wrapper.findAllComponents(Input);
    // 断言 inputs 数量
    expect(inputs.length).toBe(1);
    // 设置 searchParams.name 值
    inputs[0].vm.$emit('update:value', 'test');
    // 断言 searchParams.name 值
    expect(wrapper.vm.searchParams.name).toBe('test');

    // 测试搜索按钮点击
    await buttons[1].trigger('click');
    expect(getChannelGroupsApi).toHaveBeenCalled();

    // 测试 导出数据 按钮点击
    await buttons[2].trigger('click');
    expect(exportChannelApi).toHaveBeenCalled();

    selects[0].vm.$emit('update:value', 2);
    await buttons[1].trigger('click');
    expect(getChannelGroupsApi).toHaveBeenCalled();

    // 测试 导出数据 按钮点击
    await buttons[2].trigger('click');
    expect(exportChannelApi).toHaveBeenCalled();
  });
  // 测试 重置
  it('search reset', async () => {
    const buttons = wrapper.findAll('.ant-btn');
    expect(buttons[0].text()).toBe('重置');
    await buttons[0].trigger('click');
    expect(getChannelGroupsApi).toHaveBeenCalled();
    wrapper.vm.loading = true;
    await buttons[0].trigger('click');
  });
  // 测试 添加渠道分组
  it('and channel group', async () => {
    const button = wrapper.find('.add-text');

    // 断言 按钮是否存在
    expect(button.text()).toBe('新增渠道组');

    // 测试 新增分组渠道 按钮点击
    await button.trigger('click');
    await sleep(100);
    const modals = wrapper.findAllComponents(Modal);
    const addModalForm = mount(modals[0].vm.$slots.default);
    // 获取输入框
    const inputs = addModalForm.findAllComponents(Input);
    // 断言 输入框数量
    expect(inputs.length).toBe(2);

    // 设置 channelGroupForm.name 值
    inputs[0].vm.$emit('update:value', '渠道分组名称');
    // 断言 channelGroupForm.name 值
    expect(wrapper.vm.channelGroupForm.name).toBe('渠道分组名称');

    // 设置 channelGroupForm.channelList[0].name 值
    inputs[1].vm.$emit('update:value', '渠道名称1');
    // 断言 channelGroupForm.channelList[0].name 值
    expect(wrapper.vm.channelGroupForm.channelList[0].name).toBe('渠道名称1');

    const checkbox = addModalForm.findComponent(Checkbox);

    checkbox.vm.$emit('update:checked', true);
    expect(wrapper.vm.channelGroupForm.channelList[0].autoMakeTag).toBe(true);

    const span = addModalForm.find('.add-channel');
    // 断言 添加渠道
    expect(span.text()).toBe('新增渠道');

    await span.trigger('click');
    expect(wrapper.vm.channelGroupForm.channelList.length).toBe(2);

    const deleteBtn = addModalForm.findAll('.icon-delete');
    await deleteBtn[0].trigger('click');
    expect(wrapper.vm.channelGroupForm.channelList.length).toBe(1);

    // 测试 提交表单 按钮点击
    modals[0].vm.$emit('ok');
    await sleep(500);
    expect(createChannelGroupApi).toHaveBeenCalled();
    console.log('[ channelGroupList ] >', wrapper.vm.channelGroupList);
    // 断言 渠道分组数据
    expect(wrapper.vm.channelGroupList.length).toBe(4);

    const groupList = wrapper.findAll('.group-list-item');
    // 断言 渠道分组数量
    expect(groupList.length).toBe(4);
    // 测试 渠道分组点击
    await groupList[0].trigger('click');
    // 断言 获取渠道数据
    expect(getChannelListApi).toHaveBeenCalled();

    // 测试 渠道分组点击
    await groupList[1].trigger('click');
    // 断言 获取渠道数据
    expect(getChannelListApi).toHaveBeenCalled();
  });
  // 测试 删除渠道分组
  it('delete channel group', async () => {
    wrapper.vm.channelGroupList = [
      { id: 1, name: 'test1', channelList: [] },
      { id: 2, name: 'test2', channelList: [] },
      { id: 3, name: 'test3', channelList: [] },
      { id: 4, name: 'test4', channelList: [] },
    ];
    // 测试 分组删除
    wrapper.vm.deleteChannelGroup({ id: 1 }, 0);
    expect(deleteChannelGroupApi).toHaveBeenCalled();
    await sleep(100);
    expect(wrapper.vm.channelGroupList.length).toBe(3);
    wrapper.vm.currentChannelGroupId = -1;

    // 测试 分组删除按钮点击
    wrapper.vm.deleteChannelGroup({ id: 1 }, 0);
    await sleep(100);
    expect(deleteChannelGroupApi).toHaveBeenCalled();
    expect(wrapper.vm.channelGroupList.length).toBe(2);
  });
  // 测试 添加渠道
  it('add channel', async () => {
    const buttons = wrapper.findAll('.ant-btn');
    expect(buttons[3].text()).toBe('新增渠道');

    await buttons[3].trigger('click');
    await sleep(500);

    const modals = wrapper.findAllComponents(Modal);
    const modalForm = mount(modals[1].vm.$slots.default);

    const select = modalForm.findComponent(Select);
    // 设置 channelForm.contactChannelGroupId 值
    select.vm.$emit('update:value', 1);
    // 断言 channelForm.contactChannelGroupId 值
    expect(wrapper.vm.channelForm.contactChannelGroupId).toBe(1);

    const input = modalForm.findComponent(Input);
    // 设置 channelForm.name 值
    input.vm.$emit('update:value', '渠道名称');
    // 断言 channelForm.name 值
    expect(wrapper.vm.channelForm.name).toBe('渠道名称');

    const checkbox = modalForm.findComponent(Checkbox);
    // 设置 channelForm.autoMakeTag 值
    checkbox.vm.$emit('update:checked', true);
    // 断言 channelForm.autoMakeTag 值
    expect(wrapper.vm.channelForm.autoMakeTag).toBe(true);

    // 测试 提交表单 按钮点击
    modals[1].vm.$emit('ok');
    await sleep(100);
    expect(createChannelApi).toHaveBeenCalled();
  });
  // 测试 渠道编辑
  it('edit channel', async () => {
    await sleep(500);
    const buttons = wrapper.findAllComponents(Button);

    buttons.forEach((button) => {
      console.log('[ button.text() ] >', button.text());
    });
    // TODO: 获取不到template动态渲染的内容
    // 测试 编辑
    await buttons[4].trigger('click');
    await sleep(500);

    const modals = wrapper.findAllComponents(Modal);

    // 测试 提交表单 按钮点击
    modals[1].vm.$emit('ok');
    await sleep(100);
    expect(updateChannelApi).toHaveBeenCalled();
  });
  // 测试 渠道删除
  it('delete channel', async () => {
    await sleep(300);
    const buttons = wrapper.findAllComponents(Button);
    buttons.forEach((button) => {
      console.log('[ button.text() ] >', button.text());
    });
    // 找不到按钮
    await buttons[5].trigger('click');
    expect(deleteChannelApi).toHaveBeenCalled();
  });
  // 测试 查看渠道顾客
  it('view channel customer', async () => {
    await sleep(500);
    const button = wrapper.find('.count-text');
    // 按钮没有找到
    await button.trigger('click');
    expect(window[prefix].jumpTo).toHaveBeenCalled();
  });
  // 测试 切换渠道状态
  it('change channel autoMakeTag', async () => {
    await sleep(500);
    const sw = wrapper.findComponent(Switch);
    // switch没有找到
    sw.vm.$emit('change', 2);
    expect(updateChannelApi).toHaveBeenCalled();
  });
});
