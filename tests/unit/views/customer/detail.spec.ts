import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/customer/detail.vue';

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  Select,
  Spin,
  Switch,
  Table,
  PageHeader,
  Descriptions,
  DescriptionsItem,
  Divider,
  Tag,
  Skeleton,
  Timeline,
  TimelineItem,
  Tabs,
  TabPane,
} from 'ant-design-vue';

// import { getCustomerDetailApi, getSalesmanListApi, getFootprintListApi } from '@/api/sys/customer';

import { sleep } from '@/utils';

const REQ_TIME = 1;

vi.mock('@/api/sys/customer', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(REQ_TIME).then(() => res));

  return {
    getCustomerDetailApi: httpMock(),
    getFootprintListApi: httpMock({
      list: [
        {
          name: '鱼红涛',
          department: '测试',
          addWay: '',
          channel: '',
          joinTime: 1722244900,
          joinChatGroupNums: null,
        },
      ],
      total: 1,
      size: 10,
      page: 1,
    }),
    getSalesmanListApi: httpMock({
      list: [
        {
          action: 1,
          actionDate: '2024-07-29T09:21:46.639+00:00',
          name: '',
          userid: '',
        },
      ],
      total: 1,
      size: 10,
      page: 1,
    }),
  };
});

const routeMock = {
  query: {
    id: '1',
  },
};

// 模拟 useRoute 钩子
vi.mock('vue-router', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useRoute: () => routeMock,
  };
});

describe('Customer/detail.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;

  beforeEach(async () => {
    wrapper = mount(Component, {
      mocks: {
        // 如果需要模拟某些属性或方法，可以在这里设置
        $route: {
          query: {
            id: '123',
          },
        },
      },
      components: {
        'a-form': Form,
        'a-form-item': Form.Item,
        'a-input': Input,
        'a-input-group': InputGroup,
        'a-select': Select,
        'a-select-option': Select.Option,
        'a-checkbox': Checkbox,
        'a-switch': Switch,
        'a-button': Button,
        'a-table': Table,
        'a-spin': Spin,
        'a-empty': Empty,
        'a-modal': Modal,
        'a-popover': Popover,
        'a-page-header': PageHeader,
        'a-descriptions': Descriptions,
        'a-descriptions-item': DescriptionsItem,
        'a-divider': Divider,
        'a-tag': Tag,
        'a-skeleton': Skeleton,
        'a-timeline': Timeline,
        'a-timeline-item': TimelineItem,
        'a-tabs': Tabs,
        'a-tab-pane': TabPane,
      },
    }) as VueWrapper<typeof Component>;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  // 测试组件是否被正确渲染
  it('renders the component', async () => {
    await wrapper.vm.$nextTick();
    // 测试组件是否被正确渲染
    expect(wrapper.exists()).toBe(true);
  });

  // 测试 列表数据
  // it('should list', async () => {
  //   wrapper.vm.fetchList({ current: 1, pageSize: 10 });
  // });
});
