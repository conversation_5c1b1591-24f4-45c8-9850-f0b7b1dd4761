import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/customer/list.vue';

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  RangePicker,
  Row,
  Select,
  Spin,
  Switch,
  Table,
  Tree,
} from 'ant-design-vue';

// exportCustomerListApi
import { getCustomerListApi } from '@/api/sys/customer';

import { sleep } from '@/utils';

const REQ_TIME = 1;

vi.mock('@/api/sys/customer', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(REQ_TIME).then(() => res));

  return {
    getCustomerListApi: httpMock({
      list: [
        {
          wecomExternalId: '1',
          avatar:
            'http://wx.qlogo.cn/mmhead/dx4Y70y9XcurtOaHFLytuaXCdKibKTtfXUVQAQ9WQUNhBK7ymFd0bCg/0',
          nickName: 'nickName',
          unionid: 'ouOt5048PHrfu1NovF',
        },
      ],
      total: 1,
      size: 10,
      page: 1,
    }),
    getChannelSelectApi: httpMock([{ id: '2', name: 'name' }]),
    getQycodeListApi: httpMock([{ id: '3', qrCodeName: 'qrCodeName' }]),
    getActivityListApi: httpMock([{ id: '4', activityName: 'activityName' }]),
    exportCustomerListApi: httpMock(),
  };
});
// 创建一个模拟函数
const mockJumpTo = vi.fn();
const prefix = import.meta.env.VITE_PREFIX;
// 将模拟函数赋值给 window[prefix].jumpTo
window[prefix] = {
  jumpTo: mockJumpTo,
};
// 模拟 useRoute 钩子
vi.mock('vue-router', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useRoute: () => ({
      query: {
        channelId: '',
        userid: '',
        activity: '',
      },
    }),
  };
});

describe('Customer/list.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;

  beforeEach(async () => {
    wrapper = mount(Component, {
      components: {
        'a-form': Form,
        'a-form-item': Form.Item,
        'a-input': Input,
        'a-input-group': InputGroup,
        'a-select': Select,
        'a-select-option': Select.Option,
        'a-checkbox': Checkbox,
        'a-switch': Switch,
        'a-range-picker': RangePicker,
        'a-button': Button,
        'a-table': Table,
        'a-spin': Spin,
        'a-empty': Empty,
        'a-modal': Modal,
        'a-popover': Popover,
        'a-row': Row,
        'a-tree': Tree,
      },
    }) as VueWrapper<typeof Component>;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  // 测试组件是否被正确渲染
  it('renders the component', async () => {
    await wrapper.vm.$nextTick();
    // 测试组件是否被正确渲染
    expect(wrapper.exists()).toBe(true);
  });

  // 测试 列表数据
  it('should list', async () => {
    wrapper.vm.searchGroup[0].searchOptions.nickName.value = '客人昵称';
    wrapper.vm.searchGroup[1].searchOptions.userName.value = '导购姓名';
    wrapper.vm.fetchList({ current: 1, pageSize: 10 });
    expect(getCustomerListApi).toHaveBeenCalled();
  });

  // 测试 分页组件
  it('should pageChange is clicked', async () => {
    await sleep(REQ_TIME);
    wrapper.vm.handleTableChange({ current: 1, pageSize: 10 });
    expect(wrapper.exists()).toBe(true);
  });

  // 测试select 数量
  it('select number', async () => {
    const selects = wrapper.findAllComponents(Select);
    expect(selects.length).toBe(5);

    selects[0].vm.$emit('update:value', 'unionId');
    selects[1].vm.$emit('update:value', 'userId');
    selects[2].vm.$emit('update:value', 'activity');
    selects[3].vm.$emit('update:value', 'test_1');
    selects[4].vm.$emit('update:value', 'test_2');
  });

  // 测试 下拉选择
  it('should form select', async () => {
    // 模拟选择器的改变
    await wrapper.findComponent(Select).vm.$emit('change', 'full-time');
    expect(wrapper.exists()).toBe(true);
  });

  // 测试input 数量
  it('input number', async () => {
    const inputs = wrapper.findAllComponents(Input);
    expect(inputs.length).toBe(2);

    inputs[0].vm.$emit('update:value', '客人昵称');
    expect(wrapper.vm.searchGroup[0].searchOptions.nickName.value).toBe('客人昵称');

    inputs[1].vm.$emit('update:value', '导购姓名');
    expect(wrapper.vm.searchGroup[1].searchOptions.userName.value).toBe('导购姓名');
  });

  // 测试button 数量
  it('button number', async () => {
    const buttons = wrapper.findAllComponents(Button);
    expect(buttons.length).toBe(3);
  });

  // 测试 重置
  it('search reset', async () => {
    await sleep(10);
    const buttons = wrapper.findAll('.ant-btn');
    expect(buttons[0].text()).toBe('重置');
    await buttons[0].trigger('click');
    expect(getCustomerListApi).toHaveBeenCalled();
    wrapper.vm.loading = true;
    await buttons[0].trigger('click');
  });

  // 测试 搜索按钮
  it('search submit', async () => {
    await sleep(10);
    const buttons = wrapper.findAll('.ant-btn');
    expect(buttons[1].text()).toBe('搜 索');
    await buttons[1].trigger('click');
    expect(getCustomerListApi).toHaveBeenCalled();
    wrapper.vm.loading = true;
    await buttons[1].trigger('click');
  });

  // 测试 导出
  it('search reset', async () => {
    await sleep(100);
    const buttons = wrapper.findAll('.ant-btn');
    expect(buttons[2].text()).toBe('导出数据');
    // await buttons[2].trigger('click');
    // expect(exportCustomerListApi).toHaveBeenCalled();
  });

  // 测试 查看详情
  it('search reset', async () => {
    await sleep(10);
    const buttons = wrapper.findAll('.ant-btn-link');
    expect(buttons[0].text()).toBe('查看详情');
    await buttons[0].trigger('click');
    expect(window[prefix].jumpTo).toHaveBeenCalled();
  });
});
