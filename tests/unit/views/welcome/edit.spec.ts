import { shallowMount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/welcome/edit.vue';

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { sleep } from '@/utils';
import WelcomeTemplate from '@/views/welcome/components/welcomeTemplate.vue';

const components = {
  WelcomeTemplate,
};

describe('welcome/edit.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;

  beforeEach(async () => {
    wrapper = shallowMount(Component, {
      components,
    }) as VueWrapper<typeof Component>;
    await sleep(100);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试组件是否被正确渲染
  it('component render', async () => {
    expect(wrapper.exists()).toBe(true);
  });
});
