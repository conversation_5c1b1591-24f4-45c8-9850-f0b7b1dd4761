import { shallowMount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/welcome/components/welcomeTemplate.vue';
import autoAnswerComp from '@/components/common-auto-answer/index.vue';
import {
  Button,
  Card,
  Checkbox,
  Col,
  Empty,
  Flex,
  Form,
  FormItem,
  Input,
  InputGroup,
  Modal,
  PageHeader,
  Popconfirm,
  Popover,
  Radio,
  RadioGroup,
  RangePicker,
  Row,
  Select,
  SelectOption,
  Spin,
  Switch,
  Table,
  Tabs,
  Tag,
  Tree,
  Upload,
} from 'ant-design-vue';
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { sleep } from '@/utils';

import { getDetailApi, addApi, updateApi } from '@/api/sys/welcome';

const components = {
  'a-form': Form,
  'a-form-item': FormItem,
  'a-input': Input,
  'a-input-group': InputGroup,
  'a-select': Select,
  'a-select-option': SelectOption,
  'a-range-picker': RangePicker,
  'a-checkbox': Checkbox,
  'a-radio': Radio,
  'a-radio-group': RadioGroup,
  'a-switch': Switch,
  'a-button': Button,
  'a-table': Table,
  'a-table-column': Table.Column,
  'a-spin': Spin,
  'a-empty': Empty,
  'a-modal': Modal,
  'a-popover': Popover,
  'a-row': Row,
  'a-col': Col,
  'a-page-header': PageHeader,
  'a-card': Card,
  'a-upload': Upload,
  'a-textarea': Input.TextArea,
  'a-popconfirm': Popconfirm,
  'a-tabs': Tabs,
  'a-tab-pane': Tabs.TabPane,
  'a-tree': Tree,
  'a-flex': Flex,
  'a-tag': Tag,
  'auto-answer-comp': autoAnswerComp,
};

// 创建一个模拟函数
const mockJumpTo = vi.fn();
const prefix = import.meta.env.VITE_PREFIX;
// 将模拟函数赋值给 window[prefix].jumpTo
window[prefix] = {
  jumpTo: mockJumpTo,
};
const routeMock = {
  query: {
    // 这里定义你的查询参数
    id: '1',
    welcomeId: '99',
  },
};

vi.mock('@/utils/uuid', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    buildUUID: () => {
      return '26f06ffeba95d83614e2983e1a9b807b';
    },
  };
});
// 模拟 useRouter 钩子
vi.mock('vue-router', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useRoute: () => routeMock,
  };
});

vi.mock('@/api/sys/welcome', () => {
  const res = {
    id: '26f06ffeba95d83614e2983e1a9b807b',
    createdBy: 'SYSTEM',
    createdDate: '2024-09-03T03:20:07.104+00:00',
    modifiedBy: 'SYSTEM',
    modifiedDate: '2024-09-03T07:15:20.625+00:00',
    deletedBy: '',
    deletedDate: null,
    brandId: '1001',
    uniformType: 1,
    welcomeMessageContent: '{}',
    uniformWelcomeMessageContent: '[{"msgtype":"text","text":{"content":"统一欢迎语内容配置"}}]',
    scope: 2,
    params: 'gY9xwo5dQW+FydRI3Wcc6Q==',
    selectContent: `{"selectedData":{"member":[{"is_dp":false,"name":"朱海燕","id":"93ad7de912ba45fa8c515ccb5d2635e2","userId":"18392198582","isLeaf":true,"selectable":true,"nodeSelected":true}],"dept":[]},"userList":[{"id":"gY9xwo5dQW+FydRI3Wcc6Q==","name":"朱海燕"}]}`,
    departmentIds: null,
  };
  const res2 = {
    id: '26f06ffeba95d83614e2983e1a9b807b',
    createdBy: 'SYSTEM',
    createdDate: '2024-09-03T03:20:07.104+00:00',
    modifiedBy: 'SYSTEM',
    modifiedDate: '2024-09-03T07:15:20.625+00:00',
    deletedBy: '',
    deletedDate: null,
    brandId: '1001',
    uniformType: 2,
    welcomeMessageContent: '[{"msgtype":"text","text":{"content":"会员欢迎语内容配置"}}]',
    uniformWelcomeMessageContent: '[{"msgtype":"text","text":{"content":"非会员欢迎语内容配置"}}]',
    scope: 2,
    params: 'gY9xwo5dQW+FydRI3Wcc6Q==',
    selectContent: `{"selectedData":{"member":[{"is_dp":false,"name":"朱海燕","id":"93ad7de912ba45fa8c515ccb5d2635e2","userId":"18392198582","isLeaf":true,"selectable":true,"nodeSelected":true}],"dept":[]},"userList":[{"id":"gY9xwo5dQW+FydRI3Wcc6Q==","name":"朱海燕"}]}`,
    departmentIds: null,
  };
  const httpMock = (res?: any) => vi.fn(() => sleep(1).then(() => res));
  return {
    getDetailApi: vi
      .fn()
      .mockImplementationOnce(() => sleep(1).then(() => res))
      .mockImplementationOnce(() => sleep(1).then(() => res2)),
    addApi: httpMock({}),
    updateApi: httpMock({}),
  };
});

vi.mock('@/hooks/web/useMessage', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useMessage: () => ({
      createMessage: {
        success: vi.fn(),
        warning: vi.fn(),
      },
      createConfirm: ({ onOk }) => {
        onOk();
      },
    }),
  };
});

describe('welcomeTemplate.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;

  beforeEach(async () => {
    wrapper = shallowMount(Component, {
      components,
      global: {
        components,
      },
    }) as VueWrapper<typeof Component>;
    await sleep(100);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试组件是否被正确渲染
  it('component render', async () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('userValidator', async () => {
    wrapper.vm.formData.scope = 1;
    let res = await wrapper.vm.userValidator('', []);
    expect(res).toEqual(undefined);

    try {
      wrapper.vm.formData.scope = 2;
      res = await wrapper.vm.userValidator('', []);
    } catch (e) {
      expect(e).toEqual('请选择员工');
    }

    wrapper.vm.formData.userList = [];
    res = await wrapper.vm.userValidator('', [{ id: 'gY9xwo5dQW+FydRI3Wcc6Q==', name: '朱海燕' }]);
    expect(res).toEqual(undefined);
  });

  it('submitFn', async () => {
    wrapper.vm.submitFn({
      rawData: {
        member: [
          {
            is_dp: false,
            name: '朱海燕',
            id: '93ad7de912ba45fa8c515ccb5d2635e2',
            userId: '18392198582',
            isLeaf: true,
            selectable: true,
            nodeSelected: true,
          },
        ],
        dept: [],
      },
      list: [{ id: 'gY9xwo5dQW+FydRI3Wcc6Q==', name: '朱海燕' }],
    });
    expect(wrapper.vm.formData.userList).toEqual([
      { id: 'gY9xwo5dQW+FydRI3Wcc6Q==', name: '朱海燕' },
    ]);
  });

  it('clearStaff', async () => {
    wrapper.vm.clearStaff();
    expect(wrapper.vm.selectPerson.selectedData).toEqual({ member: [], dept: [] });
    expect(wrapper.vm.formData.userList).toEqual([]);
  });

  it('validateAnswer', async () => {
    // 统一欢迎语 未配置
    wrapper.vm.sameAnswer.textContent = '';
    wrapper.vm.formData.uniformType = 1;
    let res = wrapper.vm.validateAnswer();
    expect(res).toBe(false);

    // 统一欢迎语 已配置
    wrapper.vm.sameAnswer.textContent = '1';
    wrapper.vm.sameAnswer.materialList = [{}];
    res = wrapper.vm.validateAnswer();
    expect(res).toBe(true);

    // 非统一欢迎语 已配置
    wrapper.vm.formData.uniformType = 2;
    wrapper.vm.vipAnswer.textContent = '1';
    wrapper.vm.vipAnswer.materialList = [{}];
    wrapper.vm.normalAnswer.textContent = '1';
    wrapper.vm.normalAnswer.materialList = [{}];
    res = wrapper.vm.validateAnswer();
    expect(res).toBe(true);

    wrapper.vm.vipAnswer.textContent = '';
    wrapper.vm.vipAnswer.materialList = [];
    wrapper.vm.normalAnswer.textContent = '';
    wrapper.vm.normalAnswer.materialList = [];
    res = wrapper.vm.validateAnswer();
    expect(res).toBe(false);
  });
  it('handleSave', async () => {
    wrapper.vm.handleSave();
    expect(updateApi).toHaveBeenCalled();
    //修改id为空，调用  新增 接口
    wrapper.vm.route.query.id = '';
    await wrapper.vm.$nextTick();
    wrapper.vm.handleSave();
    expect(addApi).toHaveBeenCalled();
  });

  it('getDetail', async () => {
    await wrapper.vm.getDetails('1');
    expect(getDetailApi).toHaveBeenCalled();
  });

  it('dealMaterialList', async () => {
    const list = [
      {
        msgtype: 'text',
        text: {
          content:
            '尊敬的贵宾，\n\n希望您一切都好。\n\n\n我很高兴与您分享，Circuit运动鞋以大胆廓形、融合舒适性、前卫设计与超轻量感，重塑时尚风格。Circuit寓意一种循环往复的状态——既指运动中不断重复的动作周期，又象征着电流回路的概念。全新配色现已上市，欢迎点击小程序链接探索更多产品信息。\n\n \n您若对该作品感兴趣，我很期待向您展示此作品以及我们其他最新到货的作品。如您有任何需求或问题，请随时与我联系。😎',
        },
      },
      {
        msgtype: 'miniprogram',
        miniprogram: {
          title: 'Bottega Veneta 贵宾中心',
          appid: 'wxe88ff340417678dc',
          picurl:
            'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/30fd553f-7de9-441e-a0d1-2cb30d83d578.jpg',
          page: 'pages/index/index?channel=97',
          appendParams: {
            user_id: 'saCode',
            store_code: 'storeNo',
          },
        },
      },
      {
        msgtype: 'file',
        file: {
          name: '订单列表.xlsx',
          url: 'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/894149fe-0df1-4429-b1d6-c240b5148eac.xlsx',
        },
      },
    ];
    const res = wrapper.vm.dealMaterialList(list);
    expect(res).toEqual([
      {
        msgtype: 'miniprogram',
        title: 'Bottega Veneta 贵宾中心',
        appid: 'wxe88ff340417678dc',
        picurl:
          'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/30fd553f-7de9-441e-a0d1-2cb30d83d578.jpg',
        page: 'pages/index/index?channel=97',
        fileList: [
          {
            uid: '26f06ffeba95d83614e2983e1a9b807b',
            name: undefined,
            status: 'done',
            url: 'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/30fd553f-7de9-441e-a0d1-2cb30d83d578.jpg',
          },
        ],
        appendParams: {
          user_id: 'saCode',
          store_code: 'storeNo',
        },
      },
      {
        msgtype: 'file',
        name: '订单列表.xlsx',
        url: 'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/894149fe-0df1-4429-b1d6-c240b5148eac.xlsx',
      },
    ]);
  });

  it('validateSave', async () => {
    wrapper.vm.formRef = {
      validate: vi.fn(() => Promise.resolve(true)),
    };
    wrapper.vm.formData.uniformType = 1;
    wrapper.vm.sameAnswer.textContent = '统一欢迎语';
    wrapper.vm.sameAnswer.materialList = [
      {
        msgtype: 'miniprogram',
        title: 'Bottega Veneta 贵宾中心',
        appid: 'wxe88ff340417678dc',
        picurl:
          'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/30fd553f-7de9-441e-a0d1-2cb30d83d578.jpg',
        page: 'pages/index/index?channel=97',
        fileList: [
          {
            uid: '26f06ffeba95d83614e2983e1a9b807b',
            name: undefined,
            status: 'done',
            url: 'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/30fd553f-7de9-441e-a0d1-2cb30d83d578.jpg',
          },
        ],
        appendParams: {
          user_id: 'saCode',
          store_code: 'storeNo',
        },
      },
      {
        msgtype: 'file',
        name: '订单列表.xlsx',
        url: 'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/894149fe-0df1-4429-b1d6-c240b5148eac.xlsx',
      },
    ];
    wrapper.vm.validateSave();
    await wrapper.vm.$nextTick();
    expect(addApi).toHaveBeenCalled();

    wrapper.vm.formData.uniformType = 2;
    wrapper.vm.vipAnswer.textContent = '会员';
    wrapper.vm.vipAnswer.materialList = [
      {
        msgtype: 'miniprogram',
        title: 'Bottega Veneta 贵宾中心',
        appid: 'wxe88ff340417678dc',
        picurl:
          'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/30fd553f-7de9-441e-a0d1-2cb30d83d578.jpg',
        page: 'pages/index/index?channel=97',
        fileList: [
          {
            uid: '26f06ffeba95d83614e2983e1a9b807b',
            name: undefined,
            status: 'done',
            url: 'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/30fd553f-7de9-441e-a0d1-2cb30d83d578.jpg',
          },
        ],
        appendParams: {
          user_id: 'saCode',
          store_code: 'storeNo',
        },
      },
      {
        msgtype: 'file',
        name: '订单列表.xlsx',
        url: 'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/894149fe-0df1-4429-b1d6-c240b5148eac.xlsx',
      },
    ];
    wrapper.vm.normalAnswer.textContent = '非会员';
    wrapper.vm.normalAnswer.materialList = [
      {
        msgtype: 'miniprogram',
        title: 'Bottega Veneta 贵宾中心',
        appid: 'wxe88ff340417678dc',
        picurl:
          'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/30fd553f-7de9-441e-a0d1-2cb30d83d578.jpg',
        page: 'pages/index/index?channel=97',
        fileList: [
          {
            uid: '26f06ffeba95d83614e2983e1a9b807b',
            name: undefined,
            status: 'done',
            url: 'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/30fd553f-7de9-441e-a0d1-2cb30d83d578.jpg',
          },
        ],
        appendParams: {
          user_id: 'saCode',
          store_code: 'storeNo',
        },
      },
      {
        msgtype: 'file',
        name: '订单列表.xlsx',
        url: 'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202409/894149fe-0df1-4429-b1d6-c240b5148eac.xlsx',
      },
    ];
    wrapper.vm.validateSave();
    await wrapper.vm.$nextTick();
    expect(addApi).toHaveBeenCalled();
  });
});
