import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/welcome/list.vue';

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

import {
  Button,
  Card,
  Checkbox,
  Col,
  Empty,
  Flex,
  Form,
  FormItem,
  Input,
  InputGroup,
  Modal,
  PageHeader,
  Popconfirm,
  Popover,
  Radio,
  RadioGroup,
  RangePicker,
  Row,
  Select,
  SelectOption,
  Spin,
  Switch,
  Table,
  Tabs,
  Tag,
  Tree,
  Upload,
} from 'ant-design-vue';
import { sleep } from '@/utils';
import { getListApi, deleteApi } from '@/api/sys/welcome';

const components = {
  'a-form': Form,
  'a-form-item': FormItem,
  'a-input': Input,
  'a-input-group': InputGroup,
  'a-select': Select,
  'a-select-option': SelectOption,
  'a-range-picker': RangePicker,
  'a-checkbox': Checkbox,
  'a-radio': Radio,
  'a-radio-group': RadioGroup,
  'a-switch': Switch,
  'a-button': Button,
  'a-table': Table,
  'a-table-column': Table.Column,
  'a-spin': Spin,
  'a-empty': Empty,
  'a-modal': Modal,
  'a-popover': Popover,
  'a-row': Row,
  'a-col': Col,
  'a-page-header': PageHeader,
  'a-card': Card,
  'a-upload': Upload,
  'a-textarea': Input.TextArea,
  'a-popconfirm': Popconfirm,
  'a-tabs': Tabs,
  'a-tab-pane': Tabs.TabPane,
  'a-tree': Tree,
  'a-flex': Flex,
  'a-tag': Tag,
};

vi.mock('@/api/sys/welcome', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(1).then(() => res));
  const data = [
    {
      id: '26f06ffeba95d83614e2983e1a9b807b',
      createdBy: 'SYSTEM',
      createdDate: '2024-09-03T03:20:07.104+00:00',
      modifiedBy: 'SYSTEM',
      modifiedDate: '2024-09-03T07:15:20.625+00:00',
      deletedBy: '',
      deletedDate: null,
      brandId: '1001',
      uniformType: 1,
      welcomeMessageContent: '',
      uniformWelcomeMessageContent: '[{"msgtype":"text","text":{"content":"统一欢迎语内容配置"}}]',
      scope: 2,
      params: 'gY9xwo5dQW+FydRI3Wcc6Q==',
      selectContent: `{"selectedData":{"member":[{"is_dp":false,"name":"朱海燕","id":"93ad7de912ba45fa8c515ccb5d2635e2","userId":"18392198582","isLeaf":true,"selectable":true,"nodeSelected":true}],"dept":[]},"userList":[{"id":"gY9xwo5dQW+FydRI3Wcc6Q==","name":"朱海燕"}]}`,
      departmentIds: null,
    },
  ];

  const res = data;

  return {
    getListApi: httpMock(res),
    deleteApi: httpMock({}),
  };
});

// 创建一个模拟函数
const mockJumpTo = vi.fn();
const prefix = import.meta.env.VITE_PREFIX;
// 将模拟函数赋值给 window[prefix].jumpTo
window[prefix] = {
  jumpTo: mockJumpTo,
};
// 模拟 useRouter 钩子
vi.mock('vue-router', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
  };
});

// 模拟钩子
vi.mock('ant-design-vue', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    Modal: {
      ...actual.Modal,
      confirm: ({ onOk }) => {
        onOk();
      },
    },
  };
});

vi.mock('@/hooks/web/useMessage', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useMessage: () => ({
      createMessage: {
        success: vi.fn(),
        warning: vi.fn(),
        error: vi.fn(),
      },
      createConfirm: ({ onOk }) => {
        onOk();
      },
    }),
  };
});

describe('welcome/list.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;

  beforeEach(async () => {
    wrapper = mount(Component, {
      components,
    }) as VueWrapper<typeof Component>;
    await sleep(100);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试组件是否被正确渲染
  it('component render', async () => {
    expect(wrapper.exists()).toBe(true);

    const table = wrapper.findComponent(Table);
    expect(table.exists()).toBe(true);
    expect(getListApi).toHaveBeenCalled();
  });

  it('renderContent', async () => {
    const res = wrapper.vm.renderContent({
      uniformWelcomeMessageContent: '[{"msgtype":"text","text":{"content":"统一欢迎语内容配置"}}]',
      welcomeMessageContent: '[{"msgtype":"text","text":{"content":"vip欢迎语内容配置"}}]',
    });
    expect(res.normalAnswerTextContent).toBe('非会员：统一欢迎语内容配置');
    expect(res.vipAnswerTextContent).toBe('会员：vip欢迎语内容配置');
  });

  it('handleCreate', () => {
    wrapper.vm.handleCreate();
    expect(window[prefix].jumpTo).toHaveBeenCalled();
  });

  it('openEdit', () => {
    wrapper.vm.openEdit({ id: 1 });
    expect(window[prefix].jumpTo).toHaveBeenCalled();
  });

  it('toDelete', async () => {
    wrapper.vm.toDelete({ id: 1 });
    await wrapper.vm.$nextTick();
    expect(deleteApi).toHaveBeenCalled();
  });
});
