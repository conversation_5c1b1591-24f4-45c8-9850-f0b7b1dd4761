import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/recommendCode/detail.vue';
import { sleep } from '@/utils';
import dayjs from 'dayjs';
import { exportDayApi, exportListApi, getStaticsListApi } from '@/api/sys/recommendCode';
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import {
  Button,
  Card,
  CardGrid,
  Checkbox,
  Col,
  Empty,
  Flex,
  Form,
  FormItem,
  Input,
  InputGroup,
  Modal,
  PageHeader,
  Popconfirm,
  Popover,
  Radio,
  RadioGroup,
  RangePicker,
  Row,
  Select,
  SelectOption,
  Spin,
  Switch,
  Table,
  Tabs,
  Tree,
  Upload,
} from 'ant-design-vue';

const components = {
  'a-form': Form,
  'a-form-item': FormItem,
  'a-input': Input,
  'a-input-group': InputGroup,
  'a-select': Select,
  'a-select-option': SelectOption,
  'a-range-picker': RangePicker,
  'a-checkbox': Checkbox,
  'a-radio': Radio,
  'a-radio-group': RadioGroup,
  'a-switch': Switch,
  'a-button': Button,
  'a-table': Table,
  'a-table-column': Table.Column,
  'a-spin': Spin,
  'a-empty': Empty,
  'a-modal': Modal,
  'a-popover': Popover,
  'a-row': Row,
  'a-col': Col,
  'a-page-header': PageHeader,
  'a-card': Card,
  'a-card-grid': CardGrid,
  'a-upload': Upload,
  'a-textarea': Input.TextArea,
  'a-popconfirm': Popconfirm,
  'a-tabs': Tabs,
  'a-tab-pane': Tabs.TabPane,
  'a-tree': Tree,
  'a-flex': Flex,
};

const routeMock = {
  query: {
    id: '1',
  },
};
// 创建一个模拟函数
const mockJumpTo = vi.fn();
const prefix = import.meta.env.VITE_PREFIX;
// 将模拟函数赋值给 window[prefix].jumpTo
window[prefix] = {
  jumpTo: mockJumpTo,
};
// 模拟 useRoute 钩子
vi.mock('vue-router', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useRoute: () => routeMock,
  };
});

vi.mock('@/hooks/web/usePage', () => {
  return {
    useGo: vi.fn(() => vi.fn()),
  };
});

vi.mock('@/api/sys/recommendCode', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(1).then(() => res));

  return {
    getActivityDetailApi: httpMock({
      activityName: '111',
      autoPass: 1,
      activityType: 1,
      uniformWecomeMessageContent: JSON.stringify([
        { msgtype: 'text', text: { content: '欢迎使用企业微信' } },
        { msgtype: 'image', image: { url: 'https://www.baidu.com' } },
      ]),
      wecomeMessageContent: JSON.stringify([
        { msgtype: 'text', text: { content: '欢迎使用企业微信' } },
        { msgtype: 'image', image: { url: 'https://www.baidu.com' } },
      ]),
      selectContent: JSON.stringify({
        userList: [{ id: 1, name: '张三' }],
        selectData: {
          member: [{ id: 1, name: '张三' }],
          dept: [{ id: 1, name: '部门1' }],
        },
      }),
      channels: [{ channelId: 1, channelName: '渠道1' }],
      uniformType: 1,
      startTime: 1727712000,
      endTime: 1730217600,
      longTermEffect: 0,
      wecomeMessageType: 0,
    }),
    getStaticsApi: httpMock({
      wecomContactCodeExternalRes: {
        addCountNumber: 1,
        saNumber: 1,
        mergeFriendNumber: 1,
        addRate: 1,
        countRequestNumber: 1,
      },
    }),
    getStaticsListApi: httpMock({
      list: [
        {
          userid: '1',
          name: 'name',
        },
      ],
      total: 0,
    }),
    exportListApi: httpMock({ url: 'https://www.baidu.com' }),
    exportDayApi: httpMock({ url: 'https://www.baidu.com' }),
  };
});

describe('recommendCode/detail.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;

  beforeEach(async () => {
    wrapper = mount(Component, {
      global: {
        components,
      },
    }) as VueWrapper<typeof Component>;
    await sleep(100);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试组件是否被正确渲染
  it('component render', async () => {
    const defaultWrapper = wrapper;
    const tabs = defaultWrapper.findComponent(Tabs);
    tabs.vm.$emit('update:activeKey', 2);
    tabs.vm.$emit('change', 2);
    await sleep(100);
    expect(wrapper.vm.activeKey).toBe(2);

    // 选择数据查询范围
    const rangePicker = defaultWrapper.findComponent(RangePicker);
    rangePicker.vm.$emit('open-change', false);
    rangePicker.vm.$emit('open-change', true);
    rangePicker.vm.$emit('calendar-change', [
      dayjs(new Date(2024, 7, 1)),
      dayjs(new Date(2024, 7, 30)),
    ]);
    rangePicker.vm.$emit('change', []);
    await sleep(100);
    rangePicker.vm.$emit('change', [dayjs(new Date(2024, 7, 1)), dayjs(new Date(2024, 7, 30))]);
  });
  // 测试组件是否被正确渲染
  it('search', async () => {
    const defaultWrapper = wrapper;
    const tabs = defaultWrapper.findComponent(Tabs);
    tabs.vm.$emit('update:activeKey', 2);
    tabs.vm.$emit('change', 2);
    await sleep(100);
    expect(wrapper.vm.activeKey).toBe(2);

    // 选择数据查询范围
    const rangePicker = defaultWrapper.findAllComponents(RangePicker);
    rangePicker[1].vm.$emit('open-change', false);
    rangePicker[1].vm.$emit('open-change', true);
    rangePicker[1].vm.$emit('calendar-change', [
      dayjs(new Date(2024, 7, 1)),
      dayjs(new Date(2024, 7, 30)),
    ]);
    rangePicker[1].vm.$emit('change', []);
    await sleep(100);
    rangePicker[1].vm.$emit('change', [dayjs(new Date(2024, 7, 1)), dayjs(new Date(2024, 7, 30))]);

    const buttons = defaultWrapper.findAllComponents(Button);

    buttons.forEach((button, index) => {
      console.log(button.text() + index);
    });

    // 查询
    buttons[0].vm.$emit('click');
    await sleep(10);
    expect(getStaticsListApi).toHaveBeenCalled();

    // 重置
    buttons[1].vm.$emit('click');
    await sleep(10);
    expect(getStaticsListApi).toHaveBeenCalled();

    // 导出活动数据
    buttons[2].vm.$emit('click');
    expect(exportListApi).toHaveBeenCalled();

    // 导出每日添加数据
    buttons[3].vm.$emit('click');
    await sleep(10);
    expect(exportDayApi).toHaveBeenCalled();

    // 查看客人数据
    buttons[4].vm.$emit('click');
    await sleep(10);
  });
  it('table render', async () => {
    const defaultWrapper = wrapper;
    const tabs = defaultWrapper.findComponent(Tabs);
    tabs.vm.$emit('update:activeKey', 2);
    tabs.vm.$emit('change', 2);
    await sleep(100);
    expect(wrapper.vm.activeKey).toBe(2);

    const table = defaultWrapper.findComponent(Table);
    expect(table.exists()).toBe(true);

    // table.vm.pagination.onChange(1, 10);
    // table.vm.pagination.onShowSizeChange(1, 10);
  });
});
