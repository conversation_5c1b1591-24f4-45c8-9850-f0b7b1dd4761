import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/recommendCode/view.vue';

import { describe, it, beforeEach, vi, afterEach, Mock, expect } from 'vitest';

import {
  Button,
  Card,
  CardGrid,
  Checkbox,
  Col,
  Empty,
  Flex,
  Form,
  FormItem,
  Input,
  InputGroup,
  Modal,
  PageHeader,
  Popconfirm,
  Popover,
  Radio,
  RadioGroup,
  RangePicker,
  Row,
  Select,
  SelectOption,
  Spin,
  Switch,
  Table,
  Tabs,
  Tree,
  Upload,
} from 'ant-design-vue';

import { sleep } from '@/utils';
import { getActivityDetailApi } from '@/api/sys/recommendCode';

const components = {
  'a-form': Form,
  'a-form-item': FormItem,
  'a-input': Input,
  'a-input-group': InputGroup,
  'a-select': Select,
  'a-select-option': SelectOption,
  'a-range-picker': RangePicker,
  'a-checkbox': Checkbox,
  'a-radio': Radio,
  'a-radio-group': RadioGroup,
  'a-switch': Switch,
  'a-button': Button,
  'a-table': Table,
  'a-table-column': Table.Column,
  'a-spin': Spin,
  'a-empty': Empty,
  'a-modal': Mo<PERSON>,
  'a-popover': Popover,
  'a-row': Row,
  'a-col': Col,
  'a-page-header': PageHeader,
  'a-card': Card,
  'a-card-grid': CardGrid,
  'a-upload': Upload,
  'a-textarea': Input.TextArea,
  'a-popconfirm': Popconfirm,
  'a-tabs': Tabs,
  'a-tab-pane': Tabs.TabPane,
  'a-tree': Tree,
  'a-flex': Flex,
};
const REQ_TIME = 100;

// 模拟 useRoute 钩子
vi.mock('vue-router', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useRoute: () => ({
      query: {
        id: 1,
      },
    }),
  };
});

vi.mock('@/api/sys/recommendCode', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(REQ_TIME).then(() => res));

  const res = {
    id: 1,
    activityName: 'test',
    activityType: 1,
    qrCodeTotal: 0,
    status: 0,
    longTermEffect: '',
    selectContent: JSON.stringify({
      userList: [],
      selectedData: {
        member: [],
        dept: [],
      },
    }),
    wecomeMessageType: 1,
    uniformType: 1,
    uniformWecomeMessageContent: JSON.stringify([
      {
        text: {
          content: 'test',
        },
        msgtype: 'text',
      },
      {
        link: {
          name: 'link',
          pic_url: 'test',
        },
        msgtype: 'link',
      },
    ]),
    startTime: 1727712000,
    endTime: 1730217600,
  };

  return {
    getActivityDetailApi: httpMock(res),
  };
});

describe('recommendCode/view.vue', async () => {
  beforeEach(async () => {});

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试组件是否被正确渲染
  it('component render2', async () => {
    const wrapper = mount(Component, {
      components,
      global: {
        components,
      },
    }) as VueWrapper<typeof Component>;
    await sleep(100);

    wrapper.vm.getDetails();
    await sleep(100);
    expect(getActivityDetailApi).toHaveBeenCalled();
  });
  // 测试组件是否被正确渲染
  it('component render', async () => {
    (getActivityDetailApi as Mock).mockResolvedValue({
      activityName: '111',
      autoPass: 1,
      activityType: 5,
      uniformWecomeMessageContent: JSON.stringify([
        { msgtype: 'text', text: { content: '欢迎使用企业微信' } },
        { msgtype: 'image', image: { url: 'https://www.baidu.com' } },
      ]),
      wecomeMessageContent: JSON.stringify([
        { msgtype: 'text', text: { content: '欢迎使用企业微信' } },
        { msgtype: 'image', image: { url: 'https://www.baidu.com' } },
      ]),
      selectContent: JSON.stringify({
        storeList: [{ id: 1, name: '门店1' }],
      }),
      channels: [{ channelId: 1, channelName: '渠道1' }],
      uniformType: 2,
      startTime: 1727712000,
      endTime: 1730217600,
      longTermEffect: 0,
      wecomeMessageType: 1,
    });
    const wrapper = mount(Component, {
      components,
      global: {
        components,
      },
    }) as VueWrapper<typeof Component>;
    // await sleep(100);

    wrapper.vm.getDetails();
    await sleep(100);
    expect(getActivityDetailApi).toHaveBeenCalled();
  });
});
