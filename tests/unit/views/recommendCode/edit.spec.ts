import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/recommendCode/edit.vue';
import { describe, it, expect, beforeEach, vi, afterEach, Mock } from 'vitest';

import {
  Button,
  Card,
  Checkbox,
  Col,
  Empty,
  Flex,
  Form,
  FormItem,
  Input,
  InputGroup,
  Modal,
  PageHeader,
  Popconfirm,
  Popover,
  Radio,
  RadioGroup,
  RangePicker,
  Row,
  Select,
  SelectOption,
  Spin,
  Switch,
  Table,
  Tabs,
  Tree,
  Upload,
} from 'ant-design-vue';
import { sleep } from '@/utils';
import selectPersonComp from '@/components/common-select-person/index.vue';
import selectChannelComp from '@/components/common-select-channel/index.vue';
import { getActivityDetailApi, updateActivityApi } from '@/api/sys/recommendCode';
import selectStoreComp from '@/components/common-select-store/index.vue';
import autoAnswerComp from '@/components/common-auto-answer/index.vue';
import dayjs from 'dayjs';

// import { addCodeApi } from '@/api/sys/qyCode';
const components = {
  'a-form': Form,
  'a-form-item': FormItem,
  'a-input': Input,
  'a-input-group': InputGroup,
  'a-select': Select,
  'a-select-option': SelectOption,
  'a-range-picker': RangePicker,
  'a-checkbox': Checkbox,
  'a-radio': Radio,
  'a-radio-group': RadioGroup,
  'a-switch': Switch,
  'a-button': Button,
  'a-table': Table,
  'a-table-column': Table.Column,
  'a-spin': Spin,
  'a-empty': Empty,
  'a-modal': Modal,
  'a-popover': Popover,
  'a-row': Row,
  'a-col': Col,
  'a-page-header': PageHeader,
  'a-card': Card,
  'a-upload': Upload,
  'a-textarea': Input.TextArea,
  'a-popconfirm': Popconfirm,
  'a-tabs': Tabs,
  'a-tab-pane': Tabs.TabPane,
  'a-tree': Tree,
  'a-flex': Flex,
  'select-person-comp': selectPersonComp,
  'select-channel-comp': selectChannelComp,
  'select-store-comp': selectStoreComp,
  'auto-answer-comp': autoAnswerComp,
};

// 创建一个模拟函数
const mockJumpTo = vi.fn();
const prefix = import.meta.env.VITE_PREFIX;
// 将模拟函数赋值给 window[prefix].jumpTo
window[prefix] = {
  jumpTo: mockJumpTo,
};

vi.mock('@/api/sys/recommendCode', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(1).then(() => res));

  return {
    getActivityDetailApi: httpMock({
      activityName: '111',
      autoPass: 1,
      activityType: 1,
      uniformWecomeMessageContent: JSON.stringify([
        { msgtype: 'text', text: { content: '欢迎使用企业微信' } },
        { msgtype: 'image', image: { url: 'https://www.baidu.com' } },
      ]),
      wecomeMessageContent: JSON.stringify([
        { msgtype: 'text', text: { content: '欢迎使用企业微信' } },
        { msgtype: 'image', image: { url: 'https://www.baidu.com' } },
      ]),
      selectContent: JSON.stringify({
        userList: [{ id: 1, name: '张三' }],
        selectData: {
          member: [{ id: 1, name: '张三' }],
          dept: [{ id: 1, name: '部门1' }],
        },
      }),
      channels: [{ channelId: 1, channelName: '渠道1' }],
      uniformType: 1,
      startTime: 1727712000,
      endTime: 1730217600,
      longTermEffect: 0,
      wecomeMessageType: 0,
    }),
    addCodeApi: httpMock(),
    updateActivityApi: httpMock(),
  };
});
vi.mock('@/api/sys/components', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(1).then(() => res));

  return {
    storeListApi: httpMock([]),
    getDeptApi: httpMock([]),
    getEmpListApi: httpMock([]),
    transEmpListApi: httpMock([]),
    getChannelListApi: httpMock([]),
    getGroupListApi: httpMock([]),
  };
});

vi.mock('@/hooks/web/usePage', () => {
  return {
    useGo: vi.fn(() => vi.fn()),
  };
});

const routeMock = {
  query: {
    id: '1',
  },
};

// 模拟 useRoute 钩子
vi.mock('vue-router', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useRoute: () => routeMock,
  };
});

describe('recommendCode/edit.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;
  beforeEach(async () => {
    wrapper = mount(Component, {
      components,
      global: {
        components,
      },
    }) as VueWrapper<typeof Component>;

    await sleep(100);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试 取消编辑
  it('edit cancel', async () => {
    const buttons = wrapper.findAllComponents(Button);

    buttons.forEach((button) => {
      console.log(button.text());
    });
    buttons[1].trigger('click');

    const pageHeader = wrapper.findComponent(PageHeader);
    expect(pageHeader.exists()).toBe(true);
    pageHeader.vm.$emit('back');
    expect(window[prefix].jumpTo).toHaveBeenCalled();
  });

  // 测试 提交编辑
  it('edit submit', async () => {
    expect(wrapper.exists()).toBe(true);
    const submitButton = wrapper.findAllComponents(Button)[4];

    const inputs = wrapper.findAllComponents(Input);
    console.log('[ inputs ] >', inputs);
    // 设置 formData.activityName 值
    inputs[0].vm.$emit('update:value', 'activityName');
    // 断言 formData.activityName 值
    expect(wrapper.vm.formData.activityName).toBe('activityName');

    const radio = wrapper.findComponent(RadioGroup);
    // 设置 formData.activityType 值
    radio.vm.$emit('update:value', 1);
    // 断言 formData.activityType 值
    expect(wrapper.vm.formData.activityType).toBe(1);

    const buttons = wrapper.findAllComponents(Button);

    buttons.forEach((button) => {
      console.log(button.text());
    });

    // 清空已选员工
    await buttons[3].trigger('click');

    submitButton.trigger('click');

    // 选择员工
    buttons[2].trigger('click');
    await sleep(100);
    const selectPerson = wrapper.findComponent(selectPersonComp);
    selectPerson.vm.$emit('submit', {
      list: [{ id: 1, name: '张三' }],
      rawData: {
        member: [{ id: 1, name: '张三' }],
        dept: [{ id: 1, name: '部门1' }],
      },
    });
    selectPerson.vm.$emit('close');

    // 是否设置欢迎语
    const sw = wrapper.findAllComponents(Switch);
    console.log(sw.length);

    // 设置 formData.autoAnswer 值 0
    sw[0].vm.$emit('update:checked', 0);
    expect(wrapper.vm.formData.wecomeMessageType).toBe(0);

    // 清空已选渠道
    await buttons[1].trigger('click');
    // 选择渠道
    buttons[0].trigger('click');
    await sleep(100);
    const selectChannel = wrapper.findComponent(selectChannelComp);
    selectChannel.vm.$emit('submit', [{ id: 1, name: '渠道1' }]);
    selectChannel.vm.$emit('close');

    // 设置有效期
    const checkbox = wrapper.findComponent(Checkbox);
    checkbox.vm.$emit('update:checked', false);
    checkbox.vm.$emit('change', false);
    await sleep(100);
    expect(wrapper.vm.formData.longTermEffect).toBe(false);

    const rangePicker = wrapper.findComponent(RangePicker);
    // 清空有效期
    rangePicker.vm.$emit('update:value', []);
    rangePicker.vm.$emit('change', []);
    await sleep(100);
    // 选择有效期范围
    const startTime = dayjs(new Date(2024, 9, 1));
    const endTime = dayjs(new Date(2024, 9, 30));

    rangePicker.vm.$emit('update:value', [startTime, endTime]);
    rangePicker.vm.$emit('change', [startTime, endTime]);
    await sleep(100);
    expect(wrapper.vm.formData.startTime).toBe(startTime.unix());
    expect(wrapper.vm.formData.endTime).toBe(endTime.unix());

    // 提交;
    await submitButton.trigger('click');
    await sleep(100);
    expect(updateActivityApi).toHaveBeenCalled();
  });

  // 测试 组件是否被正确渲染
  it('get details & submit', async () => {
    (getActivityDetailApi as Mock).mockResolvedValue({
      activityName: '111',
      autoPass: 1,
      activityType: 5,
      uniformWecomeMessageContent: JSON.stringify([
        { msgtype: 'text', text: { content: '欢迎使用企业微信' } },
        { msgtype: 'image', image: { url: 'https://www.baidu.com' } },
      ]),
      wecomeMessageContent: JSON.stringify([
        { msgtype: 'text', text: { content: '欢迎使用企业微信' } },
        { msgtype: 'image', image: { url: 'https://www.baidu.com' } },
      ]),
      selectContent: JSON.stringify({
        storeList: [{ id: 1, name: '门店1' }],
      }),
      channels: [{ channelId: 1, channelName: '渠道1' }],
      uniformType: 2,
      startTime: 1727712000,
      endTime: 1730217600,
      longTermEffect: 0,
      wecomeMessageType: 1,
    });
    wrapper.vm.getDetails('1');
    await sleep(100);
    expect(wrapper.exists()).toBe(true);

    const buttons = wrapper.findAllComponents(Button);

    buttons.forEach((button) => {
      console.log('[ button.text() ] >', button.text());
    });
    const submitButton = buttons[4];
    // 清空已选门店
    await buttons[3].trigger('click');
    // 校验拦截
    await submitButton.trigger('click');

    // 选择门店
    await buttons[2].trigger('click');
    await sleep(100);
    const selectStore = wrapper.findComponent(selectStoreComp);
    selectStore.vm.$emit('submit', [{ wecomStoreId: 1, name: '门店1' }]);
    selectStore.vm.$emit('close');

    // 是否设置欢迎语
    let sw = wrapper.findAllComponents(Switch);
    console.log(1111, sw.length);

    // 设置 formData.wecomeMessageType 值 0
    sw[0].vm.$emit('update:checked', 1);
    await sleep(100);
    expect(wrapper.vm.formData.wecomeMessageType).toBe(1);
    // 是否设置欢迎语
    sw = wrapper.findAllComponents(Switch);
    // 触发统一欢迎语 开启
    sw[1].vm.$emit('update:checked', 1);
    await sleep(100);
    expect(wrapper.vm.formData.uniformType).toBe(1);

    // 配置欢迎语
    const welcomeMessage = wrapper.findComponent(autoAnswerComp);
    welcomeMessage.vm.$emit('update:answer', {
      textContent: '1111',
      materialList: [
        {
          msgtype: 'text',
          content: '欢迎使用企业微信',
        },
        {
          msgtype: 'link',
          link: '111', //H5消息页面url 必填
          title: '11', //H5消息标题
          desc: '111', //H5消息摘要
          imgUrl: '11', //H5消息封面图片URL
        },
      ],
      isUpload: false,
      isChecking: false,
    });
    await sleep(100);

    // 提交
    await submitButton.trigger('click');
    await sleep(100);
    // expect(updateActivityApi).toHaveBeenCalled();

    // 触发统一欢迎语 关闭
    sw[1].vm.$emit('update:checked', 2);
    await sleep(100);
    expect(wrapper.vm.formData.uniformType).toBe(2);
    // 提交
    await submitButton.trigger('click');
  });
});
