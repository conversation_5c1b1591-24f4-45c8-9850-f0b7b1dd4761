import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/recommendCode/list.vue';

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  RangePicker,
  Row,
  Select,
  Spin,
  Switch,
  Table,
} from 'ant-design-vue';

import { sleep } from '@/utils';
import { getActivityListApi } from '@/api/sys/recommendCode';

const REQ_TIME = 100;
// 创建一个模拟函数
const mockJumpTo = vi.fn();
const prefix = import.meta.env.VITE_PREFIX;
// 将模拟函数赋值给 window[prefix].jumpTo
window[prefix] = {
  jumpTo: mockJumpTo,
};

vi.mock('@/api/sys/recommendCode', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(REQ_TIME).then(() => res));
  const data = [
    {
      id: 1,
      activityName: 'test',
      activityType: 1,
      qrCodeTotal: 0,
      status: 0,
      longTermEffect: '',
      modifiedDate: '',
    },
    {
      id: 1,
      activityName: 'test',
      activityType: 1,
      qrCodeTotal: 0,
      status: 1,
      longTermEffect: '',
      modifiedDate: '',
    },
  ];

  const res = {
    list: data,
    total: data.length,
  };

  return {
    getActivityListApi: httpMock(res),
  };
});

describe('recommendCode/list.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;

  beforeEach(async () => {
    wrapper = mount(Component, {
      components: {
        'a-form': Form,
        'a-form-item': Form.Item,
        'a-input': Input,
        'a-input-group': InputGroup,
        'a-select': Select,
        'a-select-option': Select.Option,
        'a-checkbox': Checkbox,
        'a-switch': Switch,
        'a-range-picker': RangePicker,
        'a-button': Button,
        'a-table': Table,
        'a-spin': Spin,
        'a-empty': Empty,
        'a-modal': Modal,
        'a-popover': Popover,
        'a-row': Row,
      },
    }) as VueWrapper<typeof Component>;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试组件是否被正确渲染
  it('component render', async () => {
    // await wrapper.vm.$nextTick();
    // 测试组件是否被正确渲染
    expect(wrapper.exists()).toBe(true);
  });
  it('button click', async () => {
    await wrapper.vm.getList();
    await sleep(REQ_TIME);
    expect(getActivityListApi).toHaveBeenCalled();
    const buttons = wrapper.findAll('.ant-btn');

    buttons.forEach(async (button) => {
      console.log(button.text());

      switch (button.text()) {
        case '重置':
          expect(button.exists()).toBe(true);
          await button.trigger('click');
          expect(getActivityListApi).toHaveBeenCalled();
          break;
        case '搜索':
          expect(button.exists()).toBe(true);
          await button.trigger('click');
          expect(getActivityListApi).toHaveBeenCalled();
          break;
        case '创建活动':
          expect(button.exists()).toBe(true);
          await button.trigger('click');
          expect(window[prefix].jumpTo).toHaveBeenCalled();
          break;
        case '详情':
          expect(button.exists()).toBe(true);
          await button.trigger('click');
          expect(window[prefix].jumpTo).toHaveBeenCalled();
          break;
        case '编辑':
          expect(button.exists()).toBe(true);
          await button.trigger('click');
          expect(window[prefix].jumpTo).toHaveBeenCalled();
          break;
        case '终止':
          expect(button.exists()).toBe(true);
          await button.trigger('click');
          break;
        case '删除':
          expect(button.exists()).toBe(true);
          await button.trigger('click');
          break;
      }
    });
  });
  it('search form change', async () => {
    const selects = wrapper.findAllComponents(Select);
    // 测试 select 是否被正确渲染
    expect(selects.length > 0).toBe(true);

    // 测试 selectForm.type 值改变
    selects[0].vm.$emit('update:value', 1);
    selects[0].vm.$emit('change', 1);
    // 测试 selectForm.activityType 值改变
    selects[1].vm.$emit('update:value', 1);
    // 测试 selectForm.status 值改变
    selects[2].vm.$emit('update:value', 0);

    // 假设 selectForm.activityType 是响应式的
    const inputs = wrapper.findAllComponents(Input);

    expect(inputs.length > 0).toBe(true);
    inputs[0].vm.$emit('update:value', 'test');

    wrapper.findComponent(RangePicker).vm.$emit('update:value', []);
    wrapper.findComponent(RangePicker).vm.$emit('change', []);

    console.log(wrapper.vm.state.selectForm, 222);
    await wrapper.vm.getList();
    await sleep(REQ_TIME);
    expect(getActivityListApi).toHaveBeenCalled();
  });
  it('table pagination change', async () => {
    wrapper.vm.pageChange(1, 10);
    await sleep(REQ_TIME);
    expect(getActivityListApi).toHaveBeenCalled();
    wrapper.vm.pageSizeChange(1, 10);
    await sleep(REQ_TIME);
    expect(getActivityListApi).toHaveBeenCalled();
  });
  it('search form type change', async () => {
    wrapper.vm.state.selectForm.type = 2;
    await wrapper.vm.getList();
    await sleep(REQ_TIME);
    expect(getActivityListApi).toHaveBeenCalled();
  });
});
