import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/qyCode/list.vue';

import downloadCode from '@/views/qyCode/components/download-code.vue';

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

import {
  Button,
  Card,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  RangePicker,
  Select,
  Spin,
  Switch,
  Table,
} from 'ant-design-vue';
import { sleep } from '@/utils';
import { deleteCodeApi, generateRuleApi, getCodeListApi } from '@/api/sys/qyCode';

const components = {
  'a-form': Form,
  'a-form-item': Form.Item,
  'a-input': Input,
  'a-input-group': InputGroup,
  'a-select': Select,
  'a-select-option': Select.Option,
  'a-checkbox': Checkbox,
  'a-switch': Switch,
  'a-range-picker': RangePicker,
  'a-button': Button,
  'a-table': Table,
  'a-spin': Spin,
  'a-empty': Empty,
  'a-modal': Modal,
  'a-popover': Popover,
  'a-card': Card,
  'download-code': downloadCode,
};
// 创建一个模拟函数
const mockJumpTo = vi.fn();
const prefix = import.meta.env.VITE_PREFIX;
// 将模拟函数赋值给 window[prefix].jumpTo
window[prefix] = {
  jumpTo: mockJumpTo,
};

vi.mock('@/api/sys/qyCode', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(1).then(() => res));

  return {
    getCodeListApi: httpMock({
      list: [
        {
          id: 1,
          qrCodeName: 'qrCodeName',
          qrCodeType: 1,
          qrCodeCreateStatus: 4,
          qrCodeDownloadUrl: JSON.stringify({
            url: '1111',
          }),
        },
      ],
      total: 1,
    }),
    generateRuleApi: httpMock(),
    deleteCodeApi: httpMock(),
  };
});

// 模拟 useMessage 钩子
vi.mock('@/hooks/web/useMessage', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useMessage: () => ({
      createMessage: {
        success: vi.fn(),
        warning: vi.fn(),
      },
      createConfirm: ({ onOk }) => {
        onOk();
      },
    }),
  };
});

describe('qyCode/list.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;
  let buttons: VueWrapper<any>[] = [];

  beforeEach(async () => {
    wrapper = mount(Component, {
      components,
      global: {
        components,
      },
    }) as VueWrapper<typeof Component>;
    // 等待组件更新
    await wrapper.vm.$nextTick();
    // 等待一段时间，确保所有操作完成
    await sleep(300);
    buttons = wrapper.findAllComponents(Button);

    buttons.forEach((button) => {
      console.log('for-each-buttons', button.text());
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试组件是否被正确渲染
  it('component render', async () => {
    // 测试组件是否被正确渲染
    expect(wrapper.exists()).toBe(true);
  });
  // 测试 搜索
  it('search submit', async () => {
    await sleep(300);
    const buttons = wrapper.findAllComponents(Button);
    expect(buttons[1].text()).toBe('搜 索');

    const selects = wrapper.findAllComponents(Select);
    // 设置 selectForm.type 值
    selects[0].vm.$emit('update:value', 1);
    selects[0].vm.$emit('change', 1);
    // 断言 selectForm.type 值
    expect(wrapper.vm.selectForm.type).toBe(1);

    const input = wrapper.findComponent(Input);
    // 设置 selectForm.name 值
    input.vm.$emit('update:value', 'test');
    // 断言 selectForm.name 值
    expect(wrapper.vm.selectForm.name).toBe('test');

    // 设置 selectForm.qrCodeType 值
    selects[1].vm.$emit('update:value', 1);
    // 断言 selectForm.qrCodeType 值
    expect(wrapper.vm.selectForm.qrCodeType).toBe(1);

    // 设置 selectForm.qrCodeCreateStatus 值
    selects[2].vm.$emit('update:value', 1);
    // 断言 selectForm.qrCodeCreateStatus 值
    expect(wrapper.vm.selectForm.qrCodeCreateStatus).toBe(1);

    const rangePicker = wrapper.findComponent(RangePicker);
    rangePicker.vm.$emit('update:value', []);

    await buttons[1].trigger('click');
    expect(getCodeListApi).toHaveBeenCalled();
  });
  // 测试 重置
  it('search reset', async () => {
    expect(buttons[0].text()).toBe('重置');
    await buttons[0].trigger('click');
    expect(getCodeListApi).toHaveBeenCalled();
  });
  // 测试 创建活码
  it('create code', async () => {
    await buttons[2].trigger('click');
    expect(window[prefix].jumpTo).toHaveBeenCalled();
  });
  // 测试 查看详情
  it('regeneration', async () => {
    await buttons[3].trigger('click');
    expect(generateRuleApi).toHaveBeenCalled();
  });
  // 测试 查看详情
  it('view code', async () => {
    await buttons[4].trigger('click');
    expect(window[prefix].jumpTo).toHaveBeenCalled();
  });
  // 测试 编辑
  it('edit code', async () => {
    await buttons[5].trigger('click');
    expect(window[prefix].jumpTo).toHaveBeenCalled();
  });
  // 测试 下载
  it('download code', async () => {
    await buttons[6].trigger('click');
    expect(wrapper.vm.state.syncVisible).toBe(true);
  });
  // 测试 删除
  it('delete code', async () => {
    await buttons[7].trigger('click');
    expect(deleteCodeApi).toHaveBeenCalled();
  });
});
