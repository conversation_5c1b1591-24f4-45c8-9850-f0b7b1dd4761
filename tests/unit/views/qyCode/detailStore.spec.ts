import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/qyCode/detailStore.vue';

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

import {
  Button,
  Card,
  CardGrid,
  Checkbox,
  Col,
  Empty,
  Flex,
  Form,
  FormItem,
  Input,
  InputGroup,
  Modal,
  PageHeader,
  Popconfirm,
  Popover,
  Radio,
  RadioGroup,
  RangePicker,
  Row,
  Select,
  SelectOption,
  Spin,
  Switch,
  Table,
  Tabs,
  Tree,
  Upload,
} from 'ant-design-vue';
import { sleep } from '@/utils';
import { generateCodeApi, getDetailListApi } from '@/api/sys/qyCode';

const components = {
  'a-form': Form,
  'a-form-item': FormItem,
  'a-input': Input,
  'a-input-group': InputGroup,
  'a-select': Select,
  'a-select-option': SelectOption,
  'a-range-picker': RangePicker,
  'a-checkbox': Checkbox,
  'a-radio': Radio,
  'a-radio-group': RadioGroup,
  'a-switch': Switch,
  'a-button': Button,
  'a-table': Table,
  'a-table-column': Table.Column,
  'a-spin': Spin,
  'a-empty': Empty,
  'a-modal': Modal,
  'a-popover': Popover,
  'a-row': Row,
  'a-col': Col,
  'a-page-header': PageHeader,
  'a-card': Card,
  'a-card-grid': CardGrid,
  'a-upload': Upload,
  'a-textarea': Input.TextArea,
  'a-popconfirm': Popconfirm,
  'a-tabs': Tabs,
  'a-tab-pane': Tabs.TabPane,
  'a-tree': Tree,
  'a-flex': Flex,
};

const routeMock = {
  query: {
    id: '1',
  },
};

// 模拟 useRoute 钩子
vi.mock('vue-router', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useRoute: () => routeMock,
  };
});

vi.mock('@/api/sys/qyCode', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(1).then(() => res));

  return {
    generateCodeApi: httpMock(true),
    getDetailListApi: httpMock({
      list: [
        { userId: 1, qrCodeUrl: 'https://www.baidu.com', status: 0 },
        { userId: 1, qrCodeUrl: 'https://www.baidu.com', status: 1 },
      ],
      total: 1,
    }),
    exportApi: httpMock({
      url: 'https://www.baidu.com',
    }),
    exportCustomApi: httpMock({
      url: 'https://www.baidu.com',
    }),
  };
});

describe('qyCode/detailStore.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;

  beforeEach(async () => {
    wrapper = mount(Component, {
      components,
    }) as VueWrapper<typeof Component>;

    await sleep(100);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试组件是否被正确渲染
  it('component render', async () => {
    const selects = wrapper.findAllComponents(Select);

    // expect(selects.length).toBe(2);

    selects[0].vm.$emit('update:value', 1);
    await sleep(10);
    expect(wrapper.vm.searchParams.searchType).toBe(1);

    const input = wrapper.findComponent(Input);
    input.vm.$emit('update:value', '1');
    await sleep(10);
    expect(wrapper.vm.searchParams.searchValue).toBe('1');

    selects[1].vm.$emit('update:value', 1);

    await sleep(1000);
    const buttons = wrapper.findAllComponents(Button);

    buttons.forEach((button) => {
      console.log('[ button.text() ] >', button.text());
    });

    // 查询
    buttons[1].vm.$emit('click');
    await sleep(10);
    expect(getDetailListApi).toHaveBeenCalled();

    // 重置
    buttons[1].vm.$emit('click');
    await sleep(10);
    expect(getDetailListApi).toHaveBeenCalled();
  });
  // 测试组件是否被正确渲染
  it('table render', async () => {
    const table = wrapper.findComponent(Table);
    expect(table.exists()).toBe(true);

    table.vm.pagination.onChange(1, 10);
    table.vm.pagination.onShowSizeChange(1, 10);

    const buttons = wrapper.findAllComponents(Button);

    buttons.forEach((button) => {
      console.log('[ button.text() ] >', button.text());
    });

    wrapper.vm.searchParams.searchValue = '1';
    wrapper.vm.searchParams.searchType = 1;
    // 重新生成
    buttons[2].vm.$emit('click');
    await sleep(10);
    expect(generateCodeApi).toHaveBeenCalled();

    // 下载
    buttons[3].vm.$emit('click');
  });
});
