import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/qyCode/edit.vue';
import { describe, it, expect, beforeEach, vi, afterEach, Mock } from 'vitest';

import {
  Button,
  Card,
  Checkbox,
  Col,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  PageHeader,
  Popconfirm,
  Popover,
  Radio,
  RadioGroup,
  Row,
  Select,
  Spin,
  Switch,
  Table,
  Tabs,
  Tree,
  Upload,
} from 'ant-design-vue';
import { sleep } from '@/utils';
import selectPersonComp from '@/components/common-select-person/index.vue';
import selectChannelComp from '@/components/common-select-channel/index.vue';
import { getCodeDetailApi, updateCodeApi } from '@/api/sys/qyCode';
import selectStoreComp from '@/components/common-select-store/index.vue';
import autoAnswerComp from '@/components/common-auto-answer/index.vue';

const components = {
  'a-form': Form,
  'a-form-item': Form.Item,
  'a-input': Input,
  'a-input-group': InputGroup,
  'a-select': Select,
  'a-select-option': Select.Option,
  'a-checkbox': Checkbox,
  'a-radio': Radio,
  'a-radio-group': RadioGroup,
  'a-switch': Switch,
  'a-button': Button,
  'a-table': Table,
  'a-table-column': Table.Column,
  'a-spin': Spin,
  'a-empty': Empty,
  'a-modal': Modal,
  'a-popover': Popover,
  'a-row': Row,
  'a-col': Col,
  'a-page-header': PageHeader,
  'a-card': Card,
  'a-upload': Upload,
  'a-textarea': Input.TextArea,
  'a-popconfirm': Popconfirm,
  'a-tabs': Tabs,
  'a-tab-pane': Tabs.TabPane,
  'a-tree': Tree,
  'select-person-comp': selectPersonComp,
  'select-channel-comp': selectChannelComp,
  'select-store-comp': selectStoreComp,
  'auto-answer-comp': autoAnswerComp,
};

vi.mock('@/api/sys/qyCode', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(1).then(() => res));

  return {
    getCodeDetailApi: httpMock({
      qrCodeName: '111',
      autoPass: 1,
      qrCodeType: 1,
      uniformWecomeMessageContent: '[{"type":"text","content":"欢迎使用企业微信"}]',
      wecomeMessageContent: '[{"type":"text","content":"欢迎使用企业微信"}]',
      selectContent: JSON.stringify({
        userList: [{ id: 1, name: '张三' }],
        selectData: {
          member: [{ id: 1, name: '张三' }],
          dept: [{ id: 1, name: '部门1' }],
        },
      }),
      qrCodeChannel: [{ channelId: 1, channelName: '渠道1' }],
      uniformType: 1,
    }),
    addCodeApi: httpMock(),
    updateCodeApi: httpMock(),
  };
});
vi.mock('@/api/sys/components', () => {
  const httpMock = (res?: any) => vi.fn(() => sleep(1).then(() => res));

  return {
    storeListApi: httpMock([]),
    getDeptApi: httpMock([]),
    getEmpListApi: httpMock([]),
    transEmpListApi: httpMock([]),
    getChannelListApi: httpMock([]),
    getGroupListApi: httpMock([]),
  };
});

const routeMock = {
  query: {
    id: '1',
  },
};
// 创建一个模拟函数
const mockJumpTo = vi.fn();
const prefix = import.meta.env.VITE_PREFIX;
// 将模拟函数赋值给 window[prefix].jumpTo
window[prefix] = {
  jumpTo: mockJumpTo,
};

// 模拟 useRoute 钩子
vi.mock('vue-router', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useRoute: () => routeMock,
  };
});

describe('qyCode/edit.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;
  beforeEach(async () => {
    wrapper = mount(Component, {
      components,
      global: {
        components,
      },
    }) as VueWrapper<typeof Component>;

    await sleep(100);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试 取消编辑
  it('edit cancel', async () => {
    const buttons = wrapper.findAllComponents(Button);

    buttons.forEach((button, index) => {
      console.log('button' + index, button.text());
    });
    buttons[1].trigger('click');

    const pageHeader = wrapper.findComponent(PageHeader);
    expect(pageHeader.exists()).toBe(true);
    pageHeader.vm.$emit('back');
    expect(window[prefix].jumpTo).toHaveBeenCalled();
  });

  // 测试 提交编辑
  it('edit submit', async () => {
    await sleep(100);

    expect(wrapper.exists()).toBe(true);

    const footerWrapper = wrapper.find('.footer-box');
    const submitButton = footerWrapper.findAllComponents(Button)[0];
    console.log('submitButton', submitButton.text());

    const inputs = wrapper.findAllComponents(Input);
    // 设置 formData.qrCodeName 值
    inputs[0].vm.$emit('update:value', 'qrCodeName');
    // 断言 formData.qrCodeName 值
    expect(wrapper.vm.formData.qrCodeName).toBe('qrCodeName');

    const radio = wrapper.findComponent(RadioGroup);
    // 设置 formData.qrCodeType 值
    radio.vm.$emit('update:value', 1);
    // 断言 formData.qrCodeType 值
    expect(wrapper.vm.formData.qrCodeType).toBe(1);

    const buttons = wrapper.findAllComponents(Button);

    buttons.forEach((button, index) => {
      console.log('button' + index, button.text());
    });

    // 清空已选员工
    await buttons[1].trigger('click');

    submitButton.trigger('click');

    // 选择员工
    buttons[0].trigger('click');
    await sleep(100);
    const selectPerson = wrapper.findComponent(selectPersonComp);
    selectPerson.vm.$emit('submit', {
      list: [{ id: 1, name: '张三' }],
      rawData: {
        member: [{ id: 1, name: '张三' }],
        dept: [{ id: 1, name: '部门1' }],
      },
    });
    selectPerson.vm.$emit('close');

    // 是否设置欢迎语
    const sw = wrapper.findAllComponents(Switch);
    sw[0].vm.$emit('update:checked', 1);
    expect(wrapper.vm.formData.autoPass).toBe(1);

    // 设置 formData.autoAnswer 值 0
    sw[1].vm.$emit('update:checked', 0);
    expect(wrapper.vm.formData.autoAnswer).toBe(0);

    // 清空已选渠道
    await buttons[3].trigger('click');
    // 选择渠道
    buttons[1].trigger('click');
    await sleep(100);
    const selectChannel = wrapper.findComponent(selectChannelComp);
    selectChannel.vm.$emit('submit', [{ id: 1, name: '渠道1' }]);
    selectChannel.vm.$emit('close');

    // 提交
    await submitButton.trigger('click');
    await sleep(100);
    expect(updateCodeApi).toHaveBeenCalled();
  });

  // 测试 组件是否被正确渲染
  it(
    'get details & submit',
    async () => {
      (getCodeDetailApi as Mock).mockResolvedValue({
        qrCodeName: '111',
        autoPass: 1,
        qrCodeType: 3,
        uniformWecomeMessageContent: JSON.stringify([
          { msgtype: 'text', text: { content: '欢迎使用企业微信' } },
          { msgtype: 'image', image: { url: 'https://www.baidu.com' } },
        ]),
        wecomeMessageContent: JSON.stringify([
          { msgtype: 'text', text: { content: '欢迎使用企业微信' } },
          { msgtype: 'image', image: { url: 'https://www.baidu.com' } },
        ]),
        selectContent: JSON.stringify({
          storeList: [{ id: 1, name: '门店1' }],
        }),
        qrCodeChannel: [{ channelId: 1, channelName: '渠道1' }],
        uniformType: 2,
      });
      wrapper.vm.getDetails('1');
      await sleep(100);
      expect(wrapper.exists()).toBe(true);

      const footerWrapper = wrapper;

      const submitButton = footerWrapper.findAllComponents(Button)[17];

      const buttons = wrapper.findAllComponents(Button);

      buttons.forEach((button, index) => {
        console.log('button' + index, button.text());
      });
      // 清空已选门店
      await buttons[1].trigger('click');
      // 校验拦截
      await submitButton.trigger('click');

      // 选择门店
      await buttons[0].trigger('click');
      await sleep(100);
      const selectStore = wrapper.findComponent(selectStoreComp);
      selectStore.vm.$emit('submit', [{ wecomStoreId: 1, name: '门店1' }]);
      selectStore.vm.$emit('close');

      // 是否设置欢迎语
      const sw = wrapper.findAllComponents(Switch);

      // 设置 formData.autoAnswer 值 0
      sw[1].vm.$emit('update:checked', 1);
      expect(wrapper.vm.formData.autoAnswer).toBe(1);

      // 触发统一欢迎语 开启
      sw[2].vm.$emit('update:checked', 1);
      await sleep(100);
      expect(wrapper.vm.formData.uniformType).toBe(1);

      // 配置欢迎语
      const welcomeMessage = wrapper.findComponent(autoAnswerComp);
      welcomeMessage.vm.$emit('update:answer', {
        textContent: '1111',
        materialList: [
          {
            msgtype: 'text',
            content: '欢迎使用企业微信',
          },
          {
            msgtype: 'link',
            link: '111', //H5消息页面url 必填
            title: '11', //H5消息标题
            desc: '111', //H5消息摘要
            imgUrl: '11', //H5消息封面图片URL
          },
        ],
        isUpload: false,
        isChecking: false,
      });
      await sleep(100);

      // 提交
      await submitButton.trigger('click');
      await sleep(100);
      // expect(updateCodeApi).toHaveBeenCalled();

      // 触发统一欢迎语 关闭
      sw[2].vm.$emit('update:checked', 2);
      await sleep(100);
      expect(wrapper.vm.formData.uniformType).toBe(2);
      // 提交
      await submitButton.trigger('click');
    },
    { timeout: 10000 },
  );
});
