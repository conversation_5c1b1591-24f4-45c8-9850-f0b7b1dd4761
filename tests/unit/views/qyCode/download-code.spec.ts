import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/qyCode/components/download-code.vue';

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  Select,
  Spin,
  Switch,
  Table,
} from 'ant-design-vue';
import { sleep } from '@/utils';
import { DownloadOutlined } from '@ant-design/icons-vue';

describe('qyCode/download-code.vue', async () => {
  let wrapper: VueWrapper;

  beforeEach(async () => {
    wrapper = mount(Component, {
      props: {
        syncVisible: false,
      },
      components: {
        'a-form': Form,
        'a-form-item': Form.Item,
        'a-input': Input,
        'a-input-group': InputGroup,
        'a-select': Select,
        'a-select-option': Select.Option,
        'a-checkbox': Checkbox,
        'a-switch': Switch,
        'a-button': Button,
        'a-table': Table,
        'a-spin': Spin,
        'a-empty': Empty,
        'a-modal': Modal,
        'a-popover': Popover,
        DownloadOutlined,
      },
    });
    await sleep(100);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // 测试组件是否被正确渲染
  it('component render', async () => {
    expect(wrapper.exists()).toBe(true);

    const modal = wrapper.findComponent({ name: 'a-modal' });
    expect(modal.exists()).toBe(true);
    wrapper.setProps({ syncVisible: true });
    await sleep(100);
    expect(wrapper.vm.dialogVisible).toBe(true);
    modal.vm.$emit('open');

    const btn = wrapper.findComponent(DownloadOutlined);
    expect(btn.exists()).toBe(true);

    btn.trigger('click');

    modal.vm.$emit('cancel');
    wrapper.setProps({ syncVisible: false });
    await sleep(100);
    expect(wrapper.vm.dialogVisible).toBe(false);
  });
});
