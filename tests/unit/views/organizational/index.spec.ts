import { mount, VueWrapper } from '@vue/test-utils';
import Component from '@/views/organizational/index.vue';

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  RangePicker,
  Row,
  Select,
  Spin,
  Switch,
  Table,
  Tree,
} from 'ant-design-vue';
import { getEmpListApi } from '@/api/sys/organizational';

const REQ_TIME = 100;
function sleep(time) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve('ok');
    }, time);
  });
}

vi.mock('@/api/sys/organizational', async (importOriginal) => {
  const httpMock = (res?: any) => vi.fn(() => sleep(REQ_TIME).then(() => res));
  const actual: any = await importOriginal();
  return {
    ...actual,
    getEmpListApi: httpMock([
      {
        id: 1,
        name: 'test1',
        userid: 'test1',
        extAttrStoreId: 'test1',
        position: '',
        departmentName: 'test1',
      },
      {
        id: 2,
        name: 'test2',
        userid: 'test2',
        extAttrStoreId: 'test2',
        position: '',
        departmentName: 'test2',
      },
      {
        id: 3,
        name: 'test3',
        userid: 'test3',
        extAttrStoreId: 'test3',
        position: '',
        departmentName: 'test3',
      },
      {
        id: 4,
        name: 'test4',
        userid: 'test4',
        extAttrStoreId: 'test4',
        position: '',
        departmentName: 'test4',
      },
    ]),
  };
});
describe('organizational/index.vue', async () => {
  let wrapper: VueWrapper<typeof Component>;

  beforeEach(async () => {
    wrapper = mount(Component, {
      components: {
        'a-form': Form,
        'a-form-item': Form.Item,
        'a-input': Input,
        'a-input-group': InputGroup,
        'a-select': Select,
        'a-select-option': Select.Option,
        'a-checkbox': Checkbox,
        'a-switch': Switch,
        'a-range-picker': RangePicker,
        'a-button': Button,
        'a-table': Table,
        'a-spin': Spin,
        'a-empty': Empty,
        'a-modal': Modal,
        'a-popover': Popover,
        'a-row': Row,
        'a-tree': Tree,
      },
    }) as VueWrapper<typeof Component>;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  // 测试组件是否被正确渲染
  it('renders the component', async () => {
    // await wrapper.vm.$nextTick();
    // 测试组件是否被正确渲染
    expect(wrapper.exists()).toBe(true);
  });
  // 测试 列表数据
  it('should list', async () => {
    wrapper.vm.state.queryParams.staffValue = '测试';
    wrapper.vm.state.queryParams.status = 1;
    wrapper.vm.state.queryParams.staffType = 1;
    wrapper.vm.state.queryParams.id = '';
    wrapper.vm.state.ids = [];
    wrapper.vm.getList();
    wrapper.vm.deptDataHandler(
      0,
      [
        { parentId: 0, name: 'test1' },
        { parentId: 0, name: 'test2' },
      ],
      [],
    );
    expect(getEmpListApi).toHaveBeenCalled();
  });
  // 测试 分页组件
  it('should pageChange is clicked', async () => {
    wrapper.vm.pageChange(1, 10);
    await sleep(REQ_TIME);
    wrapper.vm.pageSizeChange(1, 10);
    await sleep(REQ_TIME);
    expect(wrapper.exists()).toBe(true);
  });
  // 测试 下拉选择
  it('should form select', async () => {
    // 模拟选择器的改变
    await wrapper.findComponent(Select).vm.$emit('change', 'full-time');
    expect(wrapper.exists()).toBe(true);
  });
  it('should form tree', async () => {
    // 模拟选择器的改变
    await wrapper.findComponent(Tree).vm.$emit('select', 'full-time');
    expect(wrapper.exists()).toBe(true);
  });
  // 测试 搜索按钮
  it('search submit', async () => {
    const buttons = wrapper.findAll('.ant-btn');
    // 测试搜索按钮点击
    await buttons[1].trigger('click');
    expect(getEmpListApi).toHaveBeenCalled();
  });
  // 测试 重置
  it('search reset', async () => {
    await sleep(500);
    const buttons = wrapper.findAll('.ant-btn');
    expect(buttons[0].text()).toBe('重置');
    await buttons[0].trigger('click');
    expect(getEmpListApi).toHaveBeenCalled();
    wrapper.vm.loading = true;
    await buttons[0].trigger('click');
  });
});
