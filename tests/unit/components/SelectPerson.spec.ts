import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import Component from '@/components/common-select-person/index.vue';
import Person from '@/components/common-select-person/person.vue';
import * as Api from '@/api/sys/components';
import { CloseOutlined, TeamOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  Select,
  Spin,
  Switch,
  Table,
  Tree,
} from 'ant-design-vue';

import { sleep } from '@/utils';

const REQ_TIME = 1000;

vi.mock('@/api/sys/components', () => {
  const httpMock = (res) => vi.fn(() => sleep(REQ_TIME).then(() => res));
  return {
    getDeptApi: httpMock([{ departmentName: '部门1', id: 1 }]),
    getEmpListApi: httpMock([{ name: '部门1', userid: 1, status: 1, id: 1 }]),
    transEmpListApi: httpMock([{ name: '部门1', userid: 1, status: 1, id: 1 }]),
  };
});

describe('common-select-person.vue', () => {
  let wrapper;

  const props = {
    visible: true,
    selectedData: { member: [], dept: [] },
    title: '添加员工（支持多选）',
  };

  beforeEach(async () => {
    wrapper = mount(Component, {
      propsData: props,
      components: {
        CloseOutlined,
        TeamOutlined,
        Button,
        Checkbox,
        Empty,
        Form,
        Input,
        InputGroup,
        [Modal.name as string]: Modal,
        Popover,
        [Select.name as string]: Select,
        [Tree.name as string]: Tree,
        Spin,
        Switch,
        Table,
      },
    });

    // 确保所有异步操作完成
    await sleep(REQ_TIME);
    // 检查弹窗是否可见
    expect(wrapper.vm.visible).toBe(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render correctly', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('handle reset', async () => {
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const resetBtn = defaultSlot.find('.reset-btn');
    expect(resetBtn.exists()).toBe(true);
    await resetBtn.trigger('click');
    expect(wrapper.vm.searchInfo.show).toBe(false);
    expect(Api.getDeptApi).toHaveBeenCalled();
    await sleep(REQ_TIME);
    console.log('treeData', wrapper.vm.treeData);
    // expect(wrapper.vm.treeData).toEqual([{departmentName:"部门1",id:1}]);
  });

  it('handle search', async () => {
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const searchBtn = defaultSlot.find('.search-button');
    expect(searchBtn.exists()).toBe(true);
    //按员工搜索
    await searchBtn.trigger('click');
    expect(Api.getDeptApi).toHaveBeenCalled();
    wrapper.vm.searchInfo.value = 'good';
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.searchInfo.checkedKey).toBe(1);
    //按部门搜索
    wrapper.vm.searchInfo.checkedKey = 2;
    await searchBtn.trigger('click');
    expect(Api.getDeptApi).toHaveBeenCalled();
  });

  it('handle submit', async () => {
    const modal = wrapper.findComponent(Modal);
    expect(modal.exists()).toBe(true);

    const footer = mount(modal.vm.$slots.footer);

    const submitButton = footer.find('.submit-btn');
    expect(submitButton.exists()).toBe(true);
    await submitButton.trigger('click');
    expect(wrapper.emitted('submit')).toBeTruthy();
    wrapper.vm.selectMember = ['1'];
    await wrapper.vm.$nextTick();
    await submitButton.trigger('click');
    expect(Api.transEmpListApi).toHaveBeenCalled();
  });

  it('handle close', async () => {
    const modal = wrapper.findComponent(Modal);
    expect(modal.exists()).toBe(true);

    const footer = mount(modal.vm.$slots.footer);

    const closeButton = footer.find('.close-btn');
    expect(closeButton.exists()).toBe(true);
    await closeButton.trigger('click');
    expect(wrapper.emitted('close')).toBeTruthy();
  });

  it('watch props visible', async () => {
    await wrapper.setProps({ visible: false });
    expect(wrapper.vm.visible).toBe(false);
    await wrapper.setProps({
      visible: true,
      selectedData: { member: [{ name: '人员' }], dept: [{ name: '部门', parentId: '' }] },
      title: '添加员工（支持多选）',
    });
    expect(wrapper.vm.selectMember).toEqual([{ is_dp: false, name: '人员' }]);
    expect(wrapper.vm.selectDept).toEqual([{ name: '部门', parentId: '' }]);
    await wrapper.vm.$nextTick();
    expect(Api.getDeptApi).toHaveBeenCalled();
  });

  it('clear range', async () => {
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const clearBtn = defaultSlot.find('.clear-all');
    wrapper.vm.selectMember = [{ name: 'lpl' }];
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.selectMember).toEqual([{ name: 'lpl' }]);

    await clearBtn.trigger('click');

    expect(wrapper.vm.selectMember).toEqual([]);
    expect(wrapper.vm.flatTreeData).toEqual([]);

    wrapper.vm.searchInfo.show = true;
    await wrapper.vm.$nextTick();
    await clearBtn.trigger('click');
    expect(wrapper.vm.searchList).toEqual([]);
  });

  it('select change', async () => {
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const select = defaultSlot.findComponent(Select);
    select.vm.$emit('update:value', 2);
    select.vm.$emit('change', 2);
    expect(wrapper.vm.searchInfo.placeholder).toBe('请输入部门名称');
  });

  it('computed flatTreeData', async () => {
    wrapper.vm.treeData = [];
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.flatTreeData).toEqual([]);

    wrapper.vm.treeData = [{ name: '部门', parentId: '', children: [{ name: '子部门' }] }];

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.flatTreeData).toEqual([
      {
        name: '子部门',
      },
    ]);
  });

  it('selectSearchItem click', async () => {
    //搜索列表选中-人
    wrapper.vm.searchList = [{ name: 'lpl', nodeSelected: false, selectable: true }];
    await wrapper.vm.$nextTick();
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const btn = defaultSlot.find('.search-item');
    await btn.trigger('click');

    expect(wrapper.vm.selectMember).toEqual([
      { name: 'lpl', nodeSelected: true, selectable: true },
    ]);

    // 搜索列表选中-部门
    wrapper.vm.searchList = [{ name: '部门', is_dp: true, nodeSelected: false, selectable: true }];
    await wrapper.vm.$nextTick();
    await btn.trigger('click');
    expect(wrapper.vm.selectDept).toEqual([
      { name: '部门', is_dp: true, nodeSelected: true, selectable: true },
    ]);

    //搜索列表-删除
    wrapper.vm.searchList = [{ name: '部门', is_dp: true, nodeSelected: true, selectable: true }];
    await wrapper.vm.$nextTick();
    await btn.trigger('click');
    expect(wrapper.vm.selectDept).toEqual([]);
    expect(wrapper.vm.searchList).toEqual([
      { name: '部门', is_dp: true, nodeSelected: false, selectable: true },
    ]);

    //搜索列表-人员
    wrapper.vm.searchList = [{ name: '人员', is_dp: false, nodeSelected: true, selectable: true }];
    await wrapper.vm.$nextTick();
    await btn.trigger('click');
    expect(wrapper.vm.selectDept).toEqual([]);
    expect(wrapper.vm.searchList).toEqual([
      { name: '人员', is_dp: false, nodeSelected: false, selectable: true },
    ]);
  });

  it('person click', async () => {
    wrapper.vm.selectDept = [{ name: '部门', is_dp: true, nodeSelected: false, selectable: true }];
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const person = defaultSlot.findComponent(Person);
    const delBtn = person.find('.del-icon');
    await delBtn.trigger('click');
    console.log('emitted', person.emitted('handleClick'));
    expect(person.emitted('handleClick')).toBeTruthy();
  });
  it('clock node', async () => {
    //添加
    wrapper.vm.handleNodeClick([], {
      node: {
        selectable: true,
        name: '梁佩乐',
        nodeSelected: false,
        dataRef: {
          is_dept: false,
          name: '梁佩乐',
          id: 1,
        },
      },
    });
    expect(wrapper.vm.selectMember).toEqual([
      {
        nodeSelected: true,
        is_dept: false,
        name: '梁佩乐',
        id: 1,
      },
    ]);
    //删除
    wrapper.vm.handleNodeClick([], {
      node: {
        selectable: true,
        name: '梁佩乐',
        nodeSelected: true,
        dataRef: {
          is_dept: false,
          name: '梁佩乐',
          id: 1,
        },
      },
    });
    expect(wrapper.vm.selectMember).toEqual([]);
  });
  it('loadNode', async () => {
    //子节点中有人员存在，不需要查部门下的人
    await wrapper.vm.loadNode({
      dataRef: {
        children: [{ is_dp: false }],
      },
    });
    expect(Api.getEmpListApi).not.toHaveBeenCalled();

    //子节点没人员，需要查部门下的人
    await wrapper.vm.loadNode({
      id: 1,
      dataRef: {
        children: [],
      },
    });
    expect(Api.getEmpListApi).toHaveBeenCalledWith({ departmentId: 1 });
  });
});
