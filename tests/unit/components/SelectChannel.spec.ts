import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import Component from '@/components/common-select-channel/index.vue';

import * as Api from '@/api/sys/components';
import { CloseOutlined, TeamOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  Select,
  Spin,
  Switch,
  Table,
} from 'ant-design-vue';

import { sleep } from '@/utils';

const REQ_TIME = 1000;

vi.mock('@/api/sys/components', () => {
  const httpMock = (res: any) => vi.fn(() => sleep(REQ_TIME).then(() => res));
  return {
    getGroupListApi: httpMock([]),
    getChannelListApi: httpMock([]),
  };
});

describe('common-select-channel.vue', () => {
  let wrapper;
  const props = {
    visible: true,
    chose: [],
    title: '选择渠道',
  };

  beforeEach(async () => {
    wrapper = mount(Component, {
      propsData: props,
      components: {
        CloseOutlined,
        TeamOutlined,
        'a-form': Form,
        'a-form-item': Form.Item,
        'a-input': Input,
        'a-input-group': InputGroup,
        'a-select-option': Select.Option,
        'a-checkbox': Checkbox,
        'a-switch': Switch,
        'a-button': Button,
        'a-table': Table,
        'a-spin': Spin,
        'a-empty': Empty,
        'a-popover': Popover,
        [Modal.name as string]: Modal,
        [Select.name as string]: Select,
      },
    });

    // 确保所有异步操作完成
    await sleep(REQ_TIME);
    // 检查弹窗是否可见
    expect(wrapper.vm.visible).toBe(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render correctly', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('watch props visible', async () => {
    await wrapper.setProps({ visible: false });
    expect(wrapper.vm.visible).toBe(false);
    await wrapper.setProps({ visible: true, chose: ['1'] });

    expect(wrapper.vm.currentGroup).toBe('');
    expect(wrapper.vm.dialogVisible).toBe(true);
    expect(wrapper.vm.resultList).toEqual(['1']);
    await wrapper.vm.$nextTick();
    expect(Api.getGroupListApi).toHaveBeenCalled();
  });

  it('handle reset', async () => {
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const resetBtn = defaultSlot.find('.reset-btn');
    expect(resetBtn.exists()).toBe(true);
    await resetBtn.trigger('click');
    await sleep(REQ_TIME);
    expect(Api.getGroupListApi).toHaveBeenCalled();
  });

  it('handle search', async () => {
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const searchBtn = defaultSlot.find('.search-button');
    expect(searchBtn.exists()).toBe(true);

    await searchBtn.trigger('click');
    expect(Api.getGroupListApi).toHaveBeenCalled();
  });

  it('getChannelListApi be called', async () => {
    wrapper.vm.groupList = [{ name: 'group', id: 1 }];
    await wrapper.vm.$nextTick();
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const item = defaultSlot.findAll('.group-list .item')[0];
    await item.trigger('click');
    expect(Api.getChannelListApi).toHaveBeenCalled();
  });

  it('channelChoose be called', async () => {
    wrapper.vm.groupList = [{ name: 'group', id: 1 }];
    wrapper.vm.channelList = [{ name: 'channel', id: 2 }];
    await wrapper.vm.$nextTick();
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const item = defaultSlot.findAll('.channel-list .item')[0];
    await item.trigger('click');
    expect(wrapper.vm.resultList).toEqual([{ name: 'channel', id: 2 }]);
  });
});
