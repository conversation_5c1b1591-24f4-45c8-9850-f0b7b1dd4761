import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import Component from '@/components/common-auto-answer/add-material.vue';
import { CloseOutlined, TeamOutlined } from '@ant-design/icons-vue';

import * as Api from '@/api/sys/components';
import { Button, Checkbox, Form, Input, Modal, Select, Col, Row, Upload } from 'ant-design-vue';

// 模拟 useMessage 钩子
vi.mock('@/hooks/web/useMessage', async (importOriginal) => {
  const actual: any = await importOriginal(); // type is inferred
  return {
    ...actual,
    useMessage: () => ({
      createMessage: {
        success: vi.fn(),
        error: vi.fn(),
      },
    }),
  };
});
// 模拟接口
vi.mock('@/api/sys/components', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    uploadFileApi: vi.fn(),
  };
});

describe('add-material.vue', () => {
  let wrapper;

  beforeEach(async () => {
    wrapper = mount(Component, {
      propsData: {
        addDialogVisible: true,
        type: 'link', //miniprogram
        isEdit: true,
        rowData: {
          msgtype: 'link',
          picurl:
            'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202408/e9cccaf4-cb8a-4896-ade3-09d31e3369ad.png',
          title: 'title',
          desc: 'desc',
          url: 'https://www.baidu.com',
          fileList: [],
          appendParams: { user_id: 'userId', store_code: 'storeNo' },
        },
      },
      components: {
        CloseOutlined,
        TeamOutlined,
        [Button.name as string]: Button,
        [Checkbox.Group.name as string]: Checkbox.Group,
        [Checkbox.name as string]: Checkbox,
        [Upload.name as string]: Upload,
        [Form.name as string]: Form,
        [Form.Item.name as string]: Form.Item,
        [Input.name as string]: Input,
        [Modal.name as string]: Modal,
        [Select.name as string]: Select,
        [Col.name as string]: Col,
        [Row.name as string]: Row,
      },
    });
  });

  afterEach(() => {
    vi.resetModules();
  });

  it('should render', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.vm.dialogVisible).toBe(true);
    expect(wrapper.vm.dialogTitle).toBe('链接');
  });

  it('watch addDialogVisible', async () => {
    await wrapper.setProps({ addDialogVisible: false });
    expect(wrapper.vm.dialogVisible).toBe(false);
    await wrapper.setProps({ addDialogVisible: true });
    expect(wrapper.vm.dialogVisible).toBe(true);
  });

  it('valid title', async () => {
    const validTitleValue = '有效标题';
    const longTitle129 = 'a'.repeat(129);

    try {
      await wrapper.vm.validTitle(null, '');
    } catch (error) {
      expect(error).toEqual('链接标题不能为空');
    }

    try {
      await wrapper.vm.validTitle(null, longTitle129);
    } catch (error) {
      expect(error).toEqual('链接标题过长');
    }

    try {
      const res = await wrapper.vm.validTitle(null, validTitleValue);
      expect(res).toEqual(undefined);
    } catch (error) {
      console.log('error', error);
    }

    //校验卡片
    await wrapper.setProps({ type: 'miniprogram' });
    try {
      await wrapper.vm.validTitle(null, '');
    } catch (error) {
      expect(error).toEqual('卡片标题不能为空');
    }

    try {
      await wrapper.vm.validTitle(null, longTitle129);
    } catch (error) {
      expect(error).toEqual('卡片标题过长');
    }

    try {
      const res = await wrapper.vm.validTitle(null, validTitleValue);
      expect(res).toEqual(undefined);
    } catch (error) {
      console.log('error', error);
    }
  });

  it('valid desc', async () => {
    const validValue = '有效标题';
    const longTitle = 'a'.repeat(513);

    try {
      await wrapper.vm.validDesc(null, '');
    } catch (error) {
      expect(error).toEqual('链接描述不能为空');
    }
    try {
      await wrapper.vm.validDesc(null, longTitle);
    } catch (error) {
      expect(error).toEqual('链接描述过长');
    }

    try {
      const res = await wrapper.vm.validDesc(null, validValue);
      expect(res).toEqual(undefined);
    } catch (error) {
      console.log('error', error);
    }
  });

  it('valid url', async () => {
    const validValue = 'https://www.baiud.com';

    try {
      await wrapper.vm.validUrl(null, '');
    } catch (error) {
      expect(error).toEqual('URL不能为空');
    }
    try {
      await wrapper.vm.validUrl(null, '乱写');
    } catch (error) {
      expect(error).toEqual('请输入正确的URL');
    }

    try {
      const res = await wrapper.vm.validUrl(null, validValue);
      expect(res).toEqual(undefined);
    } catch (error) {
      console.log('error', error);
    }
  });

  it('valid PageUrl', async () => {
    //校验卡片
    await wrapper.setProps({ type: 'miniprogram' });
    const longTitle = 'a'.repeat(513);

    try {
      await wrapper.vm.validPageUrl(null, '');
    } catch (error) {
      expect(error).toEqual('页面路径不能为空');
    }
    try {
      await wrapper.vm.validPageUrl(null, longTitle);
    } catch (error) {
      expect(error).toEqual('已超出200个字符，请重新编辑');
    }

    try {
      const res = await wrapper.vm.validPageUrl(null, 'pages/index/index');
      expect(res).toEqual(undefined);
    } catch (error) {
      console.log('error', error);
    }
  });

  it('beforeUpload', async () => {
    //文件类型和大小正确时返回
    const fileJpg = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    let res = await wrapper.vm.beforeUpload(fileJpg);
    expect(res).toBe(true);

    // 文件类型错误时返回错误提示
    const fileTxt = new File(['test'], 'test.txt', { type: 'text/plain' });
    res = await wrapper.vm.beforeUpload(fileTxt);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 文件大小超过限制时返回错误提示
    const fileLarge = new File(['test'.repeat(3 * 1024 * 1024)], 'test.jpg', {
      type: 'image/jpeg',
    });
    res = await wrapper.vm.beforeUpload(fileLarge);
    expect(res).toBe(Upload.LIST_IGNORE);
  });

  it('customRequest', async () => {
    const onSuccess = vi.fn();
    const onError = vi.fn();
    const fileJpg = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    // 模拟成功的响应
    (Api.uploadFileApi as any).mockResolvedValueOnce({ url: 'https://a.png' });
    await wrapper.vm.customRequest({ file: fileJpg, onSuccess, onError });
    expect(Api.uploadFileApi).toHaveBeenCalled();
    expect(onSuccess).toHaveBeenCalled();
    // 模拟失败的响应
    (Api.uploadFileApi as any).mockResolvedValueOnce({ url: '' });
    await wrapper.vm.customRequest({ file: fileJpg, onSuccess, onError });
    expect(onError).toHaveBeenCalled();
    (Api.uploadFileApi as any).mockRejectedValueOnce(new Error('Upload failed'));
    // 模拟失败的响应
    await wrapper.vm.customRequest({ file: fileJpg, onSuccess, onError });
    expect(onError).toHaveBeenCalled();
  });

  it('handleRemove', async () => {
    wrapper.vm.materialForm.fileList = [{}];
    wrapper.vm.materialForm.picurl = 'url';
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.materialForm.fileList.length).toBe(1);
    expect(wrapper.vm.materialForm.picurl).toBe('url');
    await wrapper.vm.handleRemove();
    expect(wrapper.vm.materialForm.fileList.length).toBe(0);
    expect(wrapper.vm.materialForm.picurl).toBe('');
  });

  it('closeDialog', async () => {
    await wrapper.vm.closeDialog();
    expect(wrapper.emitted().updateMaterial).toEqual([[{ flag: false }]]);
  });

  it('handleConfirm', async () => {
    await wrapper.setProps({
      type: 'miniprogram',
    });

    const materialFormRef = wrapper.vm.materialFormRef;
    const spy = vi.spyOn(materialFormRef, 'validate').mockImplementation(() => {
      return Promise.resolve();
    });
    // 提交miniprogram
    await wrapper.vm.handleConfirm();
    expect(spy).toHaveBeenCalled();
    // 提交link
    await wrapper.setProps({
      type: 'link',
    });
    await wrapper.vm.handleConfirm();
    expect(spy).toHaveBeenCalled();
  });
});
