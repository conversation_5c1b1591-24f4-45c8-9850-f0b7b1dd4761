import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { shallowMount } from '@vue/test-utils';
import Component from '@/components/common-auto-answer/index.vue';
import AddMaterialComp from '@/components/common-auto-answer/add-material.vue';
import { CloseOutlined, TeamOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  Select,
  Spin,
  Switch,
  Table,
  Upload,
} from 'ant-design-vue';
import * as Api from '@/api/sys/components';
import { sleep } from '@/utils';
import { createImgPreview } from '@/components/Preview';

vi.mock('@/components/Preview', () => {
  return {
    createImgPreview: vi.fn(),
  };
});
// 模钩子
vi.mock('@/hooks/web/useMessage', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useMessage: () => ({
      createMessage: {
        success: vi.fn(),
        warning: vi.fn(),
        error: vi.fn(),
      },
      createConfirm: ({ onOk }) => {
        onOk();
      },
    }),
  };
});
const REQ_TIME = 300;

vi.mock('@/api/sys/components', () => {
  return {
    uploadFileApi: vi.fn(() => sleep(REQ_TIME).then(() => ({ url: 'ok:url' }))),
  };
});

describe('auto-Answer.vue', () => {
  let wrapper;

  beforeEach(async () => {
    wrapper = shallowMount(Component, {
      propsData: {
        answer: {
          textContent: '',
          materialList: [],
          isUpload: false,
          isChecking: false,
        },
        maxMaterialLength: 9,
        disabled: false,
      },
      components: {
        CloseOutlined,
        TeamOutlined,
        Button,
        Checkbox,
        Empty,
        Form,
        Input,
        InputGroup,
        [Upload.name as string]: Upload,
        [Modal.name as string]: Modal,
        [Popover.name as string]: Popover,
        [Select.name as string]: Select,
        [Table.name as string]: Table,
        Spin,
        Switch,
        'add-material-comp': AddMaterialComp,
      },
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render correctly', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('addEmoji', async () => {
    //超过500个字符不添加表情
    const longTitle500 = 'a'.repeat(500);
    wrapper.vm.answer.textContent = longTitle500;
    await wrapper.vm.$nextTick();
    await wrapper.vm.addEmoji('😀');
    expect(wrapper.vm.answer.textContent).toBe(longTitle500);

    //添加到最后
    const longTitle400 = 'a'.repeat(400);
    wrapper.vm.answer.textContent = longTitle400;
    await wrapper.vm.$nextTick();
    await wrapper.vm.addEmoji('😀');
    expect(wrapper.vm.answer.textContent).toEqual(longTitle400 + '😀');
    expect(wrapper.vm.emojiShow).toBe(false);
  });
  // 上传回调
  it('beforeUploadImg', async () => {
    //文件类型和大小正确时返回
    const fileJpg = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    let res = await wrapper.vm.beforeUploadImg(fileJpg);
    expect(res).toBe(true);

    // 文件类型错误时返回错误提示
    const fileTxt = new File(['test'], 'test.txt', { type: 'text/plain' });
    res = await wrapper.vm.beforeUploadImg(fileTxt);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 文件大小超过限制时返回错误提示
    const fileLarge = new File(['test'.repeat(11 * 1024 * 1024)], 'test.jpg', {
      type: 'image/jpeg',
    });
    res = await wrapper.vm.beforeUploadImg(fileLarge);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 素材名字超过15个字
    const fileName = new File(['test'], 'aaaaaaaaaaaaaaaa.jpg', { type: 'image/jpeg' });
    res = await wrapper.vm.beforeUploadImg(fileName);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 编辑第一个格式不对
    wrapper.vm.answer.materialList = [
      {
        uploadFail: false,
        isUploading: true,
      },
    ];
    await wrapper.vm.$nextTick();
    res = await wrapper.vm.beforeUploadImg(fileTxt, 0);
    expect(wrapper.vm.answer.materialList[0].uploadFail).toBe(true);
    expect(wrapper.vm.answer.materialList[0].isUploading).toBe(false);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 编辑第一个大小不对
    wrapper.vm.answer.materialList = [
      {
        uploadFail: false,
        isUploading: true,
      },
    ];
    await wrapper.vm.$nextTick();
    res = await wrapper.vm.beforeUploadImg(fileLarge, 0);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 素材数量超过限制
    wrapper.vm.answer.materialList = [{}, {}, {}, {}, {}, {}, {}, {}, {}];
    res = await wrapper.vm.beforeUploadImg(fileJpg);
    expect(res).toBe(Upload.LIST_IGNORE);
  });

  // 上传回调
  it('beforeUploadVideo', async () => {
    //文件类型和大小正确时返回
    const fileVideo = new File(['test'], 'test.mp4', { type: 'video/mp4' });
    let res = await wrapper.vm.beforeUploadVideo(fileVideo);
    expect(res).toBe(true);

    // 文件类型错误时返回错误提示
    const fileTxt = new File(['test'], 'test.txt', { type: 'text/plain' });
    res = await wrapper.vm.beforeUploadVideo(fileTxt);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 文件大小超过限制时返回错误提示
    const fileLarge = new File(['test'.repeat(100 * 1024 * 1024)], 'test.mp4', {
      type: 'video/mp4',
    });
    res = await wrapper.vm.beforeUploadVideo(fileLarge);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 素材名字超过15个字
    const fileName = new File(['test'], 'aaaaaaaaaaaaaaaa.mp4', { type: 'video/mp4' });
    res = await wrapper.vm.beforeUploadVideo(fileName);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 编辑第一个格式不对
    wrapper.vm.answer.materialList = [
      {
        uploadFail: false,
        isUploading: true,
      },
    ];
    res = await wrapper.vm.beforeUploadVideo(fileTxt, 0);
    expect(wrapper.vm.answer.materialList[0].uploadFail).toBe(true);
    expect(wrapper.vm.answer.materialList[0].isUploading).toBe(false);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 编辑第一个大小不对
    wrapper.vm.answer.materialList = [
      {
        uploadFail: false,
        isUploading: true,
      },
    ];
    res = await wrapper.vm.beforeUploadVideo(fileLarge, 0);
    expect(wrapper.vm.answer.materialList[0].uploadFail).toBe(false);
    expect(wrapper.vm.answer.materialList[0].isUploading).toBe(true);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 素材数量超过限制
    wrapper.vm.answer.materialList = [{}, {}, {}, {}, {}, {}, {}, {}, {}];
    res = await wrapper.vm.beforeUploadVideo(fileVideo);
    expect(res).toBe(Upload.LIST_IGNORE);
  });

  // 上传回调
  it('beforeUploadFile', async () => {
    //文件类型和大小正确时返回
    const fileText = new File(['test'], 'test.txt');
    let res = await wrapper.vm.beforeUploadFile(fileText);
    expect(res).toBe(true);

    // 文件类型错误时返回错误提示
    const fileVideo = new File(['test'], 'test.mp4', { type: 'video/mp4' });
    res = await wrapper.vm.beforeUploadFile(fileVideo);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 文件大小超过限制时返回错误提示
    const fileLarge = new File(['test'.repeat(2 * 20 * 1024 * 1024)], 'test.txt');
    res = await wrapper.vm.beforeUploadFile(fileLarge);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 编辑第一个格式不对
    wrapper.vm.answer.materialList = [
      {
        uploadFail: false,
        isUploading: true,
      },
    ];
    res = await wrapper.vm.beforeUploadFile(fileVideo, 0);
    expect(wrapper.vm.answer.materialList[0].uploadFail).toBe(true);
    expect(wrapper.vm.answer.materialList[0].isUploading).toBe(false);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 编辑第一个大小不对
    wrapper.vm.answer.materialList = [
      {
        uploadFail: false,
        isUploading: true,
      },
    ];
    res = await wrapper.vm.beforeUploadFile(fileLarge, 0);
    expect(wrapper.vm.answer.materialList[0].uploadFail).toBe(false);
    expect(wrapper.vm.answer.materialList[0].isUploading).toBe(true);
    expect(res).toBe(Upload.LIST_IGNORE);

    // 素材数量超过限制
    wrapper.vm.answer.materialList = [{}, {}, {}, {}, {}, {}, {}, {}, {}];
    res = await wrapper.vm.beforeUploadFile(fileText);
    expect(res).toBe(Upload.LIST_IGNORE);
  });

  it('customRequestImg', async () => {
    const fileJpg = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    wrapper.vm.materialName = 'text';
    await wrapper.vm.customRequestImg({ file: fileJpg, onError: vi.fn(), onSuccess: vi.fn() }, 0);
    expect(Api.uploadFileApi).toHaveBeenCalled();
  });
  it('customRequestFile', async () => {
    const fileText = new File(['test'], 'test.txt');
    wrapper.vm.materialName = 'text';
    await wrapper.vm.customRequestFile({ file: fileText, onError: vi.fn(), onSuccess: vi.fn() }, 0);
    expect(Api.uploadFileApi).toHaveBeenCalled();
  });
  it('customRequestVideo', async () => {
    const fileJpg = new File(['test'], 'test.mp4', { type: 'video/mp4' });
    wrapper.vm.materialName = 'text';
    await wrapper.vm.customRequestVideo({ file: fileJpg, onError: vi.fn(), onSuccess: vi.fn() }, 0);
    expect(Api.uploadFileApi).toHaveBeenCalled();
  });
  it('setMaterial maxLength', async () => {
    wrapper.vm.answer.materialList = [{}, {}, {}, {}, {}, {}, {}, {}, {}];
    const res = wrapper.vm.setMaterial({});
    await wrapper.vm.$nextTick();
    expect(res).toBe(undefined);
  });

  it('setMaterial ok', async () => {
    wrapper.vm.setMaterial({ type: 'link' });
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isEditMaterial).toBe(false);
  });

  it('openPreview', async () => {
    //预览图片
    wrapper.vm.openPreview({ msgtype: 'image', pic_url: 'https://meinv.png' });
    await wrapper.vm.$nextTick();
    expect(createImgPreview).toHaveBeenCalled();
    //预览视频
    wrapper.vm.openPreview({ msgtype: 'video', url: 'https://meinv.mp4' });
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.is_open_video).toBe(true);
  });
  it('openEdit', async () => {
    wrapper.vm.openEdit({ type: 'link' }, 0);
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.rowIndex).toBe(0);
  });

  it('deleteMaterial', async () => {
    wrapper.vm.answer.materialList = [{}];
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.answer.materialList.length).toBe(1);
    wrapper.vm.deleteMaterial(0);
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.answer.materialList).toHaveLength(0);
  });

  it('updateMaterial', async () => {
    const res = await wrapper.vm.updateMaterial({ flag: true, data: '' });
    expect(res).toBe(undefined);
    wrapper.vm.updateMaterial({ flag: true, data: { type: 'link' } });
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.answer.materialList).toHaveLength(1);
  });
  it('findIndex', async () => {
    wrapper.vm.answer.materialList = [{ a: 1 }, { a: 2 }];
    const res = wrapper.vm.findIndex({ a: 2 });
    expect(res).toBe(1);
  });
});
