import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import Component from '@/components/common-auto-answer/add-emoji.vue';
import emoji from '@/components/common-auto-answer/emoji.json';

import { CloseOutlined, TeamOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  Select,
  Spin,
  Switch,
  Table,
} from 'ant-design-vue';

const emojiList: any[] = [];
for (const key in emoji) {
  const arr: string[] = [];
  for (const em of emoji[key]) {
    arr.push(em);
  }
  const item = {
    type: key,
    list: arr,
  };
  emojiList.push(item);
}

describe('addMeoji.vue', () => {
  let wrapper;

  beforeEach(async () => {
    wrapper = mount(Component, {
      components: {
        CloseOutlined,
        TeamOutlined,
        Button,
        Checkbox,
        Empty,
        Form,
        Input,
        InputGroup,
        [Modal.name as string]: Modal,
        [Popover.name as string]: Popover,
        [Select.name as string]: Select,
        Spin,
        Switch,
        Table,
      },
    });

    wrapper.vm.showDialog = true;
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.showDialog).toBe(true);
    expect(wrapper.vm.emojiList).toEqual(emojiList);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render correctly', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('handle submit', async () => {
    const popover = wrapper.findComponent(Popover);
    expect(popover.exists()).toBe(true);

    const content = mount(popover.vm.$slots.content);

    const submitButton = content.find('.emjs');
    expect(submitButton.exists()).toBe(true);
    await submitButton.trigger('click');
    expect(wrapper.emitted('select')).toBeTruthy();

    await submitButton.trigger('click');
    expect(wrapper.emitted('select').length).toBe(2);
  });
});
