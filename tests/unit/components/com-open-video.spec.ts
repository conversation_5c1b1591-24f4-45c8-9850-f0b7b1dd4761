import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import Component from '@/components/common-auto-answer/com-open-video.vue';

import { CloseOutlined, TeamOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  Select,
  Spin,
  Switch,
  Table,
} from 'ant-design-vue';

describe('openVideo.vue', () => {
  let wrapper;

  beforeEach(async () => {
    wrapper = mount(Component, {
      propsData: {
        videoUrl:
          'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202408/b76f1198-01d9-40fe-9dd2-cad59b6ffbc9.mp4',
      },
      components: {
        CloseOutlined,
        TeamOutlined,
        Button,
        Checkbox,
        Empty,
        Form,
        Input,
        InputGroup,
        [Modal.name as string]: Modal,
        [Popover.name as string]: Popover,
        [Select.name as string]: Select,
        Spin,
        Switch,
        Table,
      },
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render correctly', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('has Video', async () => {
    const source = wrapper.find('source');
    expect(source.exists()).toBe(true);
    console.log(source.attributes('src'));
    // videoPlayer.
    expect(source.attributes('src')).toBe(
      'https://kering-public-qa.oss-cn-hangzhou.aliyuncs.com/material/202408/b76f1198-01d9-40fe-9dd2-cad59b6ffbc9.mp4',
    );
  });

  it('close', async () => {
    const closeBtn = wrapper.find('.close-btn');
    await closeBtn.trigger('click');
    expect(wrapper.emitted('closeModel')).toBeTruthy();
  });
});
