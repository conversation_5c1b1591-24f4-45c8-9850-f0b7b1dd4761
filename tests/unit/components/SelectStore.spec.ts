import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import Component from '@/components/common-select-store/index.vue';

import * as Api from '@/api/sys/components';
import { CloseOutlined, TeamOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  InputGroup,
  Modal,
  Popover,
  Select,
  Spin,
  Switch,
  Table,
} from 'ant-design-vue';

import { sleep } from '@/utils';

const REQ_TIME = 1000;

vi.mock('@/api/sys/components', () => {
  const httpMock = (res: any) => vi.fn(() => sleep(REQ_TIME).then(() => res));
  return {
    storeListApi: httpMock({
      wecomStoreId: '123',
      name: 'Test Store',
    }),
  };
});

describe('common-select-store.vue', () => {
  let wrapper;

  const props = {
    visible: true,
    selectedData: [],
    title: '门店选择',
  };

  beforeEach(async () => {
    wrapper = mount(Component, {
      propsData: props,
      components: {
        CloseOutlined,
        TeamOutlined,
        Button,
        Checkbox,
        Empty,
        Form,
        Input,
        InputGroup,
        [Modal.name as string]: Modal,
        Popover,
        [Select.name as string]: Select,
        Spin,
        Switch,
        Table,
      },
    });

    // 确保所有异步操作完成
    await sleep(REQ_TIME);
    // 检查弹窗是否可见
    expect(wrapper.vm.visible).toBe(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render correctly', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('handle reset', async () => {
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const resetBtn = defaultSlot.find('.reset-btn');
    expect(resetBtn.exists()).toBe(true);
    await resetBtn.trigger('click');
    await sleep(REQ_TIME);
    expect(Api.storeListApi).toHaveBeenCalled();
  });

  it('handle search', async () => {
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const searchBtn = defaultSlot.find('.search-button');
    expect(searchBtn.exists()).toBe(true);
    await searchBtn.trigger('click');
    expect(wrapper.vm.$.setupState.filterStoreName).toEqual('');
  });

  it('handle submit', async () => {
    const modal = wrapper.findComponent(Modal);
    expect(modal.exists()).toBe(true);

    const footer = mount(modal.vm.$slots.footer);

    const submitButton = footer.find('.submit-btn');
    expect(submitButton.exists()).toBe(true);
    await submitButton.trigger('click');
    expect(wrapper.emitted('submit')).toBeTruthy();
    wrapper.vm.selectedRows = ['1'];
    await submitButton.trigger('click');
    expect(wrapper.emitted('submit')[1]).toEqual([['1']]);
  });

  it('handle close', async () => {
    const modal = wrapper.findComponent(Modal);
    expect(modal.exists()).toBe(true);

    const footer = mount(modal.vm.$slots.footer);

    const closeButton = footer.find('.close-btn');
    expect(closeButton.exists()).toBe(true);
    await closeButton.trigger('click');
    expect(wrapper.emitted('close')).toBeTruthy();
  });

  it('watch props visible', async () => {
    await wrapper.setProps({ visible: false });
    expect(wrapper.vm.visible).toBe(false);
    await wrapper.setProps({ visible: true, selectedData: ['1'] });
    expect(wrapper.vm.selectedRows).toEqual(['1']);
    await wrapper.vm.$nextTick();
    expect(Api.storeListApi).toHaveBeenCalled();
  });

  it('clear range', async () => {
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    wrapper.vm.selectedRows = ['1'];
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.selectedRows).toEqual(['1']);

    const clearBtn = defaultSlot.find('.clear-all');
    await clearBtn.trigger('click');
    expect(wrapper.vm.selectedRows).toEqual([]);
  });

  it('select change', async () => {
    const modal = wrapper.findComponent(Modal);
    const defaultSlot = mount(modal.vm.$slots.default);
    const select = defaultSlot.findComponent(Select);
    select.vm.$emit('update:value', 2);
    select.vm.$emit('change', 2);
    expect(wrapper.vm.searchInfo.placeholder).toBe('请输入门店名称');
  });

  it('computed storeListFilter ', async () => {
    wrapper.vm.storeList = [
      {
        wecomStoreId: '1',
        name: '故宫',
      },
      {
        wecomStoreId: '2',
        name: '天安门',
      },
    ];
    wrapper.vm.filterStoreCode = '2';
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.storeListFilter).toEqual([
      {
        wecomStoreId: '2',
        name: '天安门',
      },
    ]);
    wrapper.vm.filterStoreCode = '';
    wrapper.vm.filterStoreName = '故宫';
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.storeListFilter).toEqual([
      {
        wecomStoreId: '1',
        name: '故宫',
      },
    ]);
  });
});
