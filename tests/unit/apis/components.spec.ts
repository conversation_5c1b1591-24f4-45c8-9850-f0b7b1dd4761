import {
  getDept<PERSON>pi,
  getEmpList<PERSON>pi,
  transEmpList<PERSON>pi,
  storeList<PERSON>pi,
  getChannelListApi,
  getGroupListApi,
  uploadFileApi,
  Api,
} from '@/api/sys/components';

import { defHttp } from '@/utils/http/axios';
import { describe, it, expect, vi } from 'vitest';
vi.mock('@/utils/http/axios');

describe('API Functions', () => {
  it('getDept<PERSON><PERSON> should make a GET request to the correct URL', async () => {
    await getDeptApi();
    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.DEPT_LIST });
  });

  it('getEmpList<PERSON><PERSON> should make a GET request with params', async () => {
    const params = { someParam: 'value' };
    await getEmpListApi(params);
    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.EMP_LIST, params });
  });

  it('transEmpList<PERSON><PERSON> should make a GET request with params', async () => {
    const params = { someParam: 'value' };
    await transEmpListApi(params);
    expect(defHttp.post).toHaveBeenCalledWith({ url: Api.TRANS_EMP_LIST, data: params });
  });

  it('storeListApi should make a GET request with params', async () => {
    const params = { someParam: 'value' };
    await storeListApi(params);
    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.STORE_LIST, params });
  });

  it('getChannelListApi should make a GET request with params', async () => {
    const params = { someParam: 'value' };
    await getChannelListApi(params);
    expect(defHttp.get).toHaveBeenCalledWith({
      url: '/contactus/v1/contact-channels',
      params,
    });
  });

  it('getGroupListApi should make a GET request with params', async () => {
    const params = { someParam: 'value' };
    await getGroupListApi(params);
    expect(defHttp.get).toHaveBeenCalledWith({
      url: '/contactus-qrcode/v1/contact-channel-groups',
      params,
    });
  });

  it('uploadFileApi should make a POST request with form data', async () => {
    const file = new File(['it content'], 'it.txt', { type: 'text/plain' });
    await uploadFileApi({ file });
    expect(defHttp.post).toHaveBeenCalled();
  });
});
