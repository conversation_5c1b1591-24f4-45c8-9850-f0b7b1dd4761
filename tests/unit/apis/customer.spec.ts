import {
  getCustomerListApi,
  getCustomerDetailApi,
  exportCustomerListApi,
  getSalesmanListApi,
  getFootprintListApi,
  getChannelSelectApi,
  getQycodeListApi,
  getActivityListApi,
  Api,
} from '@/api/sys/customer';

import { defHttp } from '@/utils/http/axios';
import { describe, it, expect, vi } from 'vitest';
vi.mock('@/utils/http/axios');

describe('API Functions', () => {
  it('getCustomerListApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getCustomerListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.CUSTOMER, params });
  });

  it('getCustomerDetailApi should call defHttp.get with correct params', async () => {
    const id = '123';
    const params = { someParam: 'value' };

    await getCustomerDetailApi(id, params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: `${Api.CUSTOMER_DETAIL}/${id}`, params });
  });

  it('exportCustomerListApi should call defHttp.get with correct params and timeout', async () => {
    const params = { someParam: 'value' };

    await exportCustomerListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith(
      { url: Api.EXPORT, params, timeout: 60 * 1000, responseType: 'blob' },
      { isReturnNativeResponse: true },
    );
  });

  it('getSalesmanListApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getSalesmanListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.SALESMAN, params });
  });

  it('getFootprintListApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getFootprintListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.FOOTPRINT, params });
  });

  it('getChannelSelectApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getChannelSelectApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.CHANNELS_SELECT, params });
  });

  it('getQycodeListApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getQycodeListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.QYCODE_LIST, params });
  });

  it('getActivityListApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getActivityListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.ACTIVITYS, params });
  });
});
