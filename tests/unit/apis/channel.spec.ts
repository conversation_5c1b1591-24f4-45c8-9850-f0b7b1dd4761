import { defHttp } from '@/utils/http/axios';
import { describe, it, expect, vi } from 'vitest';

import {
  getChannelGroupsApi,
  createChannelGroupApi,
  deleteChannelGroupApi,
  getChannelList<PERSON>pi,
  create<PERSON>hannel<PERSON><PERSON>,
  delete<PERSON>hannel<PERSON><PERSON>,
  update<PERSON><PERSON>nel<PERSON><PERSON>,
  export<PERSON>hannel<PERSON>pi,
  Api,
} from '@/api/sys/channel';

vi.mock('@/utils/http/axios');

describe('API Functions', () => {
  it('getChannelGroupsApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getChannelGroupsApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.CHANNEL_GROUPS, params });
  });

  it('createChannelGroupApi should call defHttp.post with correct params', async () => {
    const params = { someParam: 'value' };

    await createChannelGroup<PERSON>pi(params);

    expect(defHttp.post).toHaveBeenCalledWith({ url: Api.CHANNEL_GROUPS, params });
  });

  it('deleteChannelGroupApi should call defHttp.delete with correct params', async () => {
    const id = '123';
    const params = { someParam: 'value' };

    await deleteChannelGroupApi(id, params);

    expect(defHttp.delete).toHaveBeenCalledWith({ url: `${Api.CHANNEL_GROUPS}/${id}`, params });
  });

  it('getChannelListApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getChannelListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.CHANNEL, params });
  });

  it('createChannelApi should call defHttp.post with correct params', async () => {
    const params = { someParam: 'value' };

    await createChannelApi(params);

    expect(defHttp.post).toHaveBeenCalledWith({ url: Api.CHANNEL, params });
  });

  it('deleteChannelApi should call defHttp.delete with correct params', async () => {
    const id = '123';
    const params = { someParam: 'value' };

    await deleteChannelApi(id, params);

    expect(defHttp.delete).toHaveBeenCalledWith({ url: `${Api.CHANNEL}/${id}`, params });
  });

  it('updateChannelApi should call defHttp.put with correct params', async () => {
    const id = '123';
    const params = { someParam: 'value' };

    await updateChannelApi(id, params);

    expect(defHttp.put).toHaveBeenCalledWith({ url: `${Api.CHANNEL}/${id}`, params });
  });

  it('exportChannelApi should call defHttp.get with correct params and timeout', async () => {
    const params = { someParam: 'value' };

    await exportChannelApi(params);

    expect(defHttp.get).toHaveBeenCalledWith(
      { url: Api.EXPORT, params, timeout: 60 * 1000, responseType: 'blob' },
      { isReturnNativeResponse: true },
    );
  });
});
