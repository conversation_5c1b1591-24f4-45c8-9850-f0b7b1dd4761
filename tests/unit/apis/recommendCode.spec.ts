import { defHttp } from '@/utils/http/axios';
import { describe, it, expect, vi } from 'vitest';
import {
  getActivityListApi,
  getActivityDetailApi,
  addActivityApi,
  updateActivityApi,
  deleteActivityApi,
  stopActivityApi,
  getStaticsApi,
  getStaticsListApi,
  exportListApi,
  exportDayApi,
  Api,
} from '@/api/sys/recommendCode';

vi.mock('@/utils/http/axios');
describe('API Functions', () => {
  it('getActivityListA<PERSON> should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getActivityListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.ACTIVITYS, params });
  });

  it('getActivityDetailApi should call defHttp.get with correct url', async () => {
    const id = '123';

    await getActivityDetailApi(id);

    expect(defHttp.get).toHaveBeenCalledWith({ url: `${Api.ACTIVITYS}/detail/${id}` });
  });

  it('addActivityApi should call defHttp.post with correct params', async () => {
    const params = { someParam: 'value' };

    await addActivityApi(params);

    expect(defHttp.post).toHaveBeenCalledWith({ url: Api.ACTIVITYS, params });
  });

  it('updateActivityApi should call defHttp.put with correct params', async () => {
    const id = '123';
    const params = { someParam: 'value' };

    await updateActivityApi(id, params);

    expect(defHttp.put).toHaveBeenCalledWith({ url: `${Api.ACTIVITYS}/${id}`, params });
  });

  it('deleteActivityApi should call defHttp.delete with correct url', async () => {
    const id = '123';

    await deleteActivityApi(id);

    expect(defHttp.delete).toHaveBeenCalledWith({ url: `${Api.ACTIVITYS}/${id}` });
  });

  it('stopActivityApi should call defHttp.post with correct url', async () => {
    const id = '123';

    await stopActivityApi(id);

    expect(defHttp.post).toHaveBeenCalledWith({ url: `${Api.STOP}/${id}` });
  });

  it('getStaticsApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getStaticsApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.STATISTIC, params });
  });

  it('getStaticsListApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getStaticsListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.STATISTIC_LIST, params });
  });

  it('exportListApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await exportListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith(
      { url: Api.EXPORT_LIST, params, responseType: 'blob' },
      { isReturnNativeResponse: true },
    );
  });

  it('exportDayApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await exportDayApi(params);

    expect(defHttp.get).toHaveBeenCalledWith(
      { url: Api.EXPORT_DAY, params, responseType: 'blob' },
      { isReturnNativeResponse: true },
    );
  });
});
