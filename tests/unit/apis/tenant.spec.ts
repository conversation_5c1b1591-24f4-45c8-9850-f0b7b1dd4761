import { describe, it, expect, vi } from 'vitest';
import { getTenantUsers, getTenants } from '@/api/sys/tenant';
import { defHttp } from '@/utils/http/axios';
import type { TenantsParams } from '@/api/sys/model/tenantModel';

vi.mock('@/utils/http/axios');

describe('Tenant API', () => {
  it('should call getTenantUsers with correct url', async () => {
    await getTenantUsers();
    expect(defHttp.get).toHaveBeenCalledWith({ url: '/auth-permission/v1/tenant-users' });
  });

  it('should call getTenants with correct url and params', async () => {
    const params: TenantsParams = { pageNumber: '1', pageSize: '10' };
    await getTenants(params);
    expect(defHttp.get).toHaveBeenCalledWith({ url: '/tenant-mgmt/v1/tenants', params });
  });

  it('should handle empty params for getTenants', async () => {
    const params: TenantsParams = {};
    await getTenants(params);
    expect(defHttp.get).toHaveBeenCalledWith({ url: '/tenant-mgmt/v1/tenants', params });
  });

  it('should handle max page and size for getTenants', async () => {
    const params: TenantsParams = {
      pageNumber: String(Number.MAX_SAFE_INTEGER),
      pageSize: String(Number.MAX_SAFE_INTEGER),
    };
    await getTenants(params);
    expect(defHttp.get).toHaveBeenCalledWith({ url: '/tenant-mgmt/v1/tenants', params });
  });

  it('should handle min page and size for getTenants', async () => {
    const params: TenantsParams = {
      pageNumber: String(Number.MIN_SAFE_INTEGER),
      pageSize: String(Number.MIN_SAFE_INTEGER),
    };
    await getTenants(params);
    expect(defHttp.get).toHaveBeenCalledWith({ url: '/tenant-mgmt/v1/tenants', params });
  });
});
