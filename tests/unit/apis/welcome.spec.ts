import { defHttp } from '@/utils/http/axios';
import { describe, it, expect, vi } from 'vitest';
import { getListApi, getDetailApi, addApi, updateApi, deleteApi, Api } from '@/api/sys/welcome';

vi.mock('@/utils/http/axios');
describe('API Functions', () => {
  it('get<PERSON>ist<PERSON><PERSON> should call axios.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.ACTIVITYS, params });
  });

  it('getDetail<PERSON><PERSON> should call axios.get with correct url', async () => {
    const id = '123';

    await getDetailApi(id);

    expect(defHttp.get).toHaveBeenCalledWith({ url: `${Api.ACTIVITYS}/${id}` });
  });

  it('add<PERSON><PERSON> should call axios.post with correct params', async () => {
    const params = { someParam: 'value' };

    await addApi(params);

    expect(defHttp.post).toHaveBeenCalledWith({ url: Api.ACTIVITYS, params });
  });

  it('updateApi should call axios.put with correct params', async () => {
    const id = '123';
    const params = { someParam: 'value' };

    await updateApi(id, params);

    expect(defHttp.put).toHaveBeenCalledWith({ url: `${Api.ACTIVITYS}/${id}`, params });
  });

  it('deleteApi should call axios.delete with correct url', async () => {
    const id = '123';

    await deleteApi(id);

    expect(defHttp.delete).toHaveBeenCalledWith({ url: `${Api.ACTIVITYS}/${id}` });
  });
});
