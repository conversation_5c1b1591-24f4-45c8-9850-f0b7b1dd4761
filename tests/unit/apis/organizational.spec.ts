import { defHttp } from '@/utils/http/axios';
import { describe, it, expect, vi } from 'vitest';
import { getDeptApi, getEmpListApi, Api } from '@/api/sys/organizational';

vi.mock('@/utils/http/axios');
describe('API Functions', () => {
  it('should call getDeptApi successfully', async () => {
    await getDeptApi();

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.DEPT_LIST });
  });

  it('should call getEmpListApi with correct params', async () => {
    const params = { param1: 'value1', param2: 'value2' };
    await getEmpListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.EMP_LIST, params });
  });
});
