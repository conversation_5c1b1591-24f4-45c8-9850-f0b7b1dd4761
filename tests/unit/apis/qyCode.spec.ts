import { defHttp } from '@/utils/http/axios';
import { describe, it, expect, vi } from 'vitest';
import {
  getCodeListApi,
  deleteCode<PERSON>pi,
  generateRule<PERSON>pi,
  addCode<PERSON>pi,
  updateCode<PERSON>pi,
  getCodeDetailApi,
  getDetailList<PERSON>pi,
  exportApi,
  exportCustom<PERSON>pi,
  generateCode<PERSON>pi,
  Api,
} from '@/api/sys/qyCode';

vi.mock('@/utils/http/axios');

describe('API Functions', () => {
  it('getCode<PERSON>ist<PERSON><PERSON> should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getCodeListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.COMMON, params });
  });

  it('deleteCode<PERSON>pi should call defHttp.delete with correct url', async () => {
    const codeId = '123';

    await deleteCode<PERSON>pi(codeId);

    expect(defHttp.delete).toHaveBeenCalledWith({ url: Api.COMMON_OTHER + codeId });
  });

  it('generateRule<PERSON><PERSON> should call defHttp.post with correct params', async () => {
    const codeId = '123';

    await generateRuleApi(codeId);

    expect(defHttp.post).toHaveBeenCalledWith({ url: Api.GENERATERULE, params: { id: codeId } });
  });

  it('addCodeApi should call defHttp.post with correct params', async () => {
    const params = { someParam: 'value' };

    await addCodeApi(params);

    expect(defHttp.post).toHaveBeenCalledWith({ url: Api.COMMON, params });
  });

  it('updateCodeApi should call defHttp.put with correct params', async () => {
    const params = { id: '123', someOtherParam: 'value' };

    await updateCodeApi(params);

    expect(defHttp.put).toHaveBeenCalledWith({ url: Api.COMMON_OTHER + params.id, params });
  });

  it('getCodeDetailApi should call defHttp.get with correct url', async () => {
    const codeId = '123';

    await getCodeDetailApi(codeId);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.COMMON_DETAIL + codeId });
  });

  it('getDetailListApi should call defHttp.get with correct params', async () => {
    const params = { someParam: 'value' };

    await getDetailListApi(params);

    expect(defHttp.get).toHaveBeenCalledWith({ url: Api.DETAIL_LIST, params });
  });

  it('exportApi should call defHttp.get with correct params and timeout', async () => {
    const params = { someParam: 'value' };

    await exportApi(params);

    expect(defHttp.get).toHaveBeenCalledWith(
      {
        url: Api.COMMON + '/download/qrCode-data',
        params,
        timeout: 1000 * 60,
        responseType: 'blob',
      },
      { isReturnNativeResponse: true },
    );
  });

  it('exportCustomApi should call defHttp.get with correct params and timeout', async () => {
    const params = { someParam: 'value' };

    await exportCustomApi(params);

    expect(defHttp.get).toHaveBeenCalledWith(
      {
        url: Api.COMMON + '/download/qrCode-detail',
        params,
        timeout: 1000 * 60,
        responseType: 'blob',
      },
      { isReturnNativeResponse: true },
    );
  });

  it('generateCodeApi should call defHttp.put with correct params', async () => {
    const params = { someParam: 'value' };

    await generateCodeApi(params);

    expect(defHttp.put).toHaveBeenCalledWith({ url: Api.GENERATE, params });
  });
});
