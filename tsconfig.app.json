{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": [
    "env.d.ts",
    "src/**/*",
    "src/**/*.vue",
    "types/**/*.d.ts",
    "types/**/*.ts", "tests/unit/apis/tenant.spec.ts",
  ],
  "exclude": [
    "src/**/__tests__/*"
  ],
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "#/*": [
        "./types/*"
      ]
    }
  }
}
