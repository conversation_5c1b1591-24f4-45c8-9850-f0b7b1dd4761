# cus-fe-vue3-sample-app

This template should help get you started developing with Vue 3 in Vite.

## Preparation

- [node](http://nodejs.org/) and [git](https://git-scm.com/) - Project development environment
- [Vite](https://vitejs.dev/) - Familiar with vite features
- [Vue3](https://v3.vuejs.org/) - Familiar with Vue basic syntax
- [TypeScript](https://www.typescriptlang.org/) - Familiar with the basic syntax of `TypeScript`
- [Es6+](http://es6.ruanyifeng.com/) - Familiar with es6 basic syntax
- [Vue-Router-Next](https://next.router.vuejs.org/) - Familiar with the basic use of vue-router
- [Ant-Design-Vue](https://antdv.com/docs/vue/introduce-cn/) - ui basic use

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vitejs.dev/config/).

## Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
pnpm test:unit
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm lint
```

## Git Contribution submission specification

- reference [vue](https://github.com/vuejs/vue/blob/dev/.github/COMMIT_CONVENTION.md) specification ([Angular](https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-angular))

  - `feat` Add new features
  - `fix` Fix the problem/BUG
  - `style` Modify the code style/format that does not affect the feature
  - `perf` Optimization/performance improvement
  - `refactor` Refactor
  - `revert` Undo edit
  - `test` Test related
  - `docs` Documentation/notes
  - `chore` Dependency update/scaffolding configuration modification etc.
  - `workflow` Workflow improvements
  - `ci` Continuous integration
  - `types` Type definition file changes
  - `wip` In development

## ESLint Configuration

### extends

- [plugin:vue/vue3-essential](https://eslint.vuejs.org)
- [eslint:recommended](https://eslint.org/docs/rules)
- [@vue/eslint-config-typescript](https://www.npmjs.com/package/@vue/eslint-config-typescript)
- [@vue/eslint-config-prettier/skip-formatting](https://www.npmjs.com/package/@vue/eslint-config-prettier)

## Environment Requirements

- node.js >= 18.0.0
- npm >= 10.0.0
- pnpm >= 9.0.0

## Directory Structure

```Directory
.
├── README.md                                    # 项目说明文档
├── env.d.ts                                     # 存放环境变量类型声明文件
├── index.html                                   # 入口HTML文件
├── src                                          # 项目主目录
│   ├── App.vue                                  # 根组件
│   ├── api                                      # 存放接口请求文件
│   │   ├── model                                # 存放接口请求类型声明文件
│   │   └── sys                                  # 存放具体接口请求文件
│   ├── assets                                   # 存放静态资源（需要经过构建工具处理的静态资源）
│   │   ├── css                                  # 存放第三方css
│   │   ├── fonts                                # 存放字体资源
│   │   ├── images                               # 存放图片资源
│   │   └── scripts                              # 存放第三方js
│   ├── components                               # 存放公共组件
│   │   └── layout                               # layout组件
│   │       ├── index.spec.ts                    # 当前组件的单元测试文件
│   │       ├── index.vue                        # 当前组件的主文件
│   │       └── readme.md                        # 当前组件的说明文档
│   ├── constants                                # 存放常量
│   │   ├── global.ts                            # 全局常量
│   │   └── order.ts                             # 示例：模块、页面相关常量
│   ├── directives                               # 存放自定义指令
│   ├── enums                                    # 存放枚举类型
│   ├── hooks                                    # 存放自定义hooks
│   ├── locales                                  # 存放国际化语言包
│   ├── main.ts                                  # 入口文件
│   ├── plugins                                  # 存放插件配置
│   ├── router                                   # 存放路由配置
│   ├── settings                                 # 存放项目配置
│   ├── stores                                   # 存放Vuex状态管理
│   ├── theme                                    # 存放主题配置
│   ├── ut-sample                                # 存放单元测试示例
│   ├── utils                                    # 存放工具类
│   └── views                                    # 存放页面
│       ├── home                                 # 首页目录（示例）
│       │   └── index.vue                        # 首页的主文件
│       └── order                                # 订单目录（示例）
│           └── list                             # 订单列表目录
│               ├── components                   # 订单列表组件目录
│               ├── detail.vue                   # 订单详情页面
│               └── index.vue                    # 订单列表页面
├── types                                        # 存放TypeScript声明文件
└── vite.config.ts                               # Vitest配置文件
```
