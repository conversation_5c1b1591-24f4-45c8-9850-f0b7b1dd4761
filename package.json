{"name": "wecommgmt", "version": "1.0.0", "private": true, "type": "module", "scripts": {"format": "prettier --write src/", "commit": "czg", "prepare": "husky install", "dev": "vite --mode localhost", "build": "vite build", "build:dev": "vite build --mode dev", "build:qa": "vite build --mode qa", "build:preprod": "vite build --mode preprod", "build:prod": "vite build --mode prod", "preview": "vite preview", "test": "vitest run --coverage", "test:unit": "vitest", "vitest": "vitest --coverage", "coverage": "vitest run --coverage", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "stylelint": "stylelint --fix src/**/*.{vue,scss,less,styl,html}"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["prettier --write", "eslint --fix", "stylelint --fix"], "*.{scss,less,styl,html}": ["prettier --write", "stylelint --fix"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vueuse/core": "^11.2.0", "ant-design-vue": "^4.2.3", "axios": "^1.7.2", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "pinia": "^2.1.7", "qs": "^6.12.3", "vue": "^3.4.21", "vue-i18n": "^9.13.1", "vue-router": "^4.3.0"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/jsdom": "^21.1.6", "@types/lodash-es": "^4.17.12", "@types/node": "^20.12.5", "@types/qs": "^6.9.15", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vitest/coverage-istanbul": "^2.0.5", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.5", "@vue/tsconfig": "^0.5.1", "cz-git": "^1.9.3", "czg": "^1.9.3", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "happy-dom": "14.12.3", "husky": "^9.0.11", "jsdom": "^24.0.0", "less": "^4.2.0", "lint-staged": "^15.2.6", "npm-run-all2": "^6.1.2", "postcss-html": "^1.7.0", "prettier": "^3.3.2", "prettier-plugin-packagejson": "^2.5.0", "sass": "^1.77.5", "stylelint": "^16.6.1", "stylelint-config-property-sort-order-smacss": "^10.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.0", "typescript": "~5.4.0", "unplugin-auto-import": "^0.18.3", "vite": "^5.2.8", "vite-plugin-qiankun": "^1.0.15", "vite-plugin-vue-devtools": "^7.0.25", "vitest": "^2.0.5", "vitest-sonar-reporter": "^2.0.0", "vue-router-mock": "^1.1.0", "vue-tsc": "^2.0.11"}}