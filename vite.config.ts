/// <reference types="vitest" />
// vite
import { fileURLToPath, URL } from 'node:url';
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import VueDevTools from 'vite-plugin-vue-devtools';
import qiankun from 'vite-plugin-qiankun';
import { qiankunWindow } from 'vite-plugin-qiankun/dist/helper';
import { getScssVariables } from './src/theme';
import { configDefaults } from 'vitest/config';
import { name as packageName } from './package.json';
import AutoImport from 'unplugin-auto-import/vite';
// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  return {
    base: env.VITE_NODE_ENV === 'production' ? `/dist/${packageName}/` : '/',
    esbuild: {
      drop: mode !== 'localhost' ? ['console', 'debugger'] : [],
    },
    optimizeDeps: {
      include: ['ant-design-vue/es/locale/zh_CN', 'ant-design-vue/es/locale/en_US'],
    },
    plugins: [
      qiankun('vite', { useDevMode: true }),
      vue(),
      vueJsx(),
      !qiankunWindow.__POWERED_BY_QIANKUN__ && VueDevTools(),
      AutoImport({
        // 会在根目录生成auto-imports.d.ts，里面可以看到自动导入的api
        dts: true,
        // 匹配的文件，也就是哪些后缀的文件需要自动引入
        include: [/\.[tj]sx?$/, /\.vue$/],
        // 自动引入的api从这里找
        imports: ['vue', 'vue-router', 'pinia'],
        resolvers: [],
        eslintrc: {
          enabled: false, // 1、改为true用于生成eslint配置。2、生成后改回false，避免重复生成消耗
        },
      }),
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `${getScssVariables(env.VITE_PREFIX)}`,
        },
        less: {
          modifyVars: {
            'ant-prefix': 'auth',
          },
          javascriptEnabled: true,
        },
      },
    },
    resolve: {
      alias: {
        'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '#': fileURLToPath(new URL('./types', import.meta.url)),
      },
    },
    server: {
      port: 7301,
      origin: 'http://localhost:7301',
      proxy: {
        [env.VITE_GLOB_API_URL_PREFIX]: {
          target: env.VITE_GLOB_API_URL,
          changeOrigin: true,
          ws: true,
        },
      },
    },
    test: {
      server: {
        deps: {
          inline: ['ant-design-vue'],
        },
      },
      logHeapUsage: true,
      globals: true,
      includeSource: ['src/**/*.{vue,ts}'],
      environment: 'happy-dom',
      exclude: [...configDefaults.exclude, 'e2e/**'],
      root: fileURLToPath(new URL('./', import.meta.url)),
      coverage: {
        enabled: true,
        provider: 'v8',
        reporter: ['text', 'json', 'html', 'lcov'],
        reportsDirectory: './coverage',
      },
      reporters: [
        'default',
        ['vitest-sonar-reporter', { outputFile: './coverage/sonar-report.xml' }],
      ],
    },
  };
});
